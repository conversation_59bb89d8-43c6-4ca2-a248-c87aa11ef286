{"generator-jhipster": {"promptValues": {"packageName": "com.cowell.forest", "nativeLanguage": "zh-cn"}, "jhipsterVersion": "4.14.0", "baseName": "forest", "packageName": "com.cowell.forest", "packageFolder": "com/cowell/forest", "serverPort": "9060", "authenticationType": "uaa", "uaaBaseName": "uaa", "cacheProvider": "no", "websocket": false, "databaseType": "sql", "devDatabaseType": "mysql", "prodDatabaseType": "mysql", "searchEngine": false, "messageBroker": false, "serviceDiscoveryType": "eureka", "buildTool": "maven", "enableSocialSignIn": false, "enableSwaggerCodegen": false, "jwtSecretKey": "cc854380741c15f528c64d5b00e5dee10b36f520", "enableTranslation": true, "applicationType": "microservice", "testFrameworks": [], "jhiPrefix": "jhi", "nativeLanguage": "zh-cn", "languages": ["zh-cn"], "clientPackageManager": "yarn", "skipClient": true, "skipUserManagement": true, "enableHibernateCache": false, "generator-jhipster-entity-audit": {"auditFramework": "custom"}}}