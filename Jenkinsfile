#!/usr/bin/groovy
@Library('cowell-pipeline-lib@master')

def versionPrefix = ""
try {
  versionPrefix = VERSION_PREFIX
} catch (Throwable e) {
  versionPrefix = "1.0"
}

def canaryVersion

def fabric8Console = "${env.FABRIC8_CONSOLE ?: ''}"
def utils = new io.fabric8.Utils()
def label = "buildpod.${env.JOB_NAME}.${env.BUILD_NUMBER}".replace('-', '_').replace('/', '_')

// deal with multiple branch list
def envToDeploy = "${env.ENV_TO_DEPLOY}"
def cur_user_id
def deploy_status
def branch_list
def email_list = "devops,xqqin,wxsun,lcheng,jwqiu"

if("$envToDeploy".trim() == "Prod") {
   canaryVersion = "${versionPrefix}.${env.BUILD_NUMBER}.pd"
}
else{
   canaryVersion = "${versionPrefix}.${env.BUILD_NUMBER}"
}

mavenNode{
  container(name: 'maven') {

  //check whether the user has permission to run in prod
  wrap([$class: 'BuildUser']) {
    cur_user_id = env.BUILD_USER_ID
  }
  if("$envToDeploy".trim() == "Prod") {
      try {
          sh "cp /root/.ssh/prod_auth_user.list /home/<USER>/prod_auth_user.list && chmod 777 /home/<USER>/prod_auth_user.list"
          def propFileContent = readFile("../../prod_auth_user.list")
          if(!propFileContent.contains("user."+cur_user_id+".reject=") && !propFileContent.contains("user."+cur_user_id+".allow=")) {
              error("ERR: user $cur_user_id not correctly configured as authrized account !")
          }
          def rejReg = "user." + cur_user_id + ".reject=.*"
          propFileContent.eachMatch(rejReg) { line ->
            def endLine = line + ","
            if(endLine.contains("all,") || endLine.contains("${env.JOB_NAME}"+",")) {
              error("ERR: user $cur_user_id is rejected to deploy project ${env.JOB_NAME} to PROD env!")
            }
          }
          def allowReg = "user." + cur_user_id + ".allow=.*"
          propFileContent.eachMatch(allowReg) { line ->
            def endLine = line + ","
            if(!endLine.contains("all,") && !endLine.contains("${env.JOB_NAME}"+",")) {
              error("ERR: user $cur_user_id is not allowed to deploy project ${env.JOB_NAME} to PROD env!")
            }
          }
          echo "INFO: user $cur_user_id is ok to deploy project ${env.JOB_NAME} to PROD env, proceed to build phase..."
      } catch (err) {
          echo "Failed: ${err}"
          currentBuild.result = 'FAILURE'
          return
      }
  }

  def envTest = utils.environmentNamespace('testing')
  def envStage = utils.environmentNamespace('scrmstaging')
  def envProd = utils.environmentNamespace('scrmproduction')

  git([url: 'ssh://git@10.1.0.4:2222/gaojihealth-dev/purchase.git', credentialsId: 'ssh-key-credential'])
  sh """
     git clone ssh://git@10.1.0.4:2222/gaojihealth-dev/deploy-config.git
     cp "deploy-config/${env.JOB_NAME}-deploy.prop" "${env.JOB_NAME}-deploy.prop"
     if [ ! -f "${env.JOB_NAME}-deploy.prop" ]; then
       echo "ERROR: failed to download ${env.JOB_NAME}-deploy.prop"
       exit 1
     fi
     git config --global user.email "<EMAIL>"
     git config --global user.name "codemerger"
     branch_list=`cat "${env.JOB_NAME}-deploy.prop" | grep -i "deploy_to_${envToDeploy}=" | sed "s#.*deploy_to_${envToDeploy}=##" | sed "s#</.*##"`
     echo \$branch_list | awk -F, '{for(i=1;i<=NF;i++) print \$i}' | while read bracnch_name; do
       if [ "\$bracnch_name" == "" ]; then
         echo "INFO: no branch to merge or merge done"
         exit
       fi
       echo "merging branch \$bracnch_name ..."
       git merge origin/\$bracnch_name 
       merge_result=\$?
       if [ \$merge_result -ne 0 ]; then
         echo "ERROR: git branch merge fialed with error code \$merge_result !"
         exit \$merge_result
       fi
       echo "preventing project config files ..."
       git reset HEAD Jenkinsfile
       git checkout -- Jenkinsfile
     done || exit 1

     """
  if("$envToDeploy".trim() == "Prod") {
    readFile("${env.JOB_NAME}-deploy.prop").eachMatch("deploy_to_Prod=.*") { line ->
      branch_list = line.stripIndent(15)
    }
  }

  echo 'INFO: generating custom k8s configurations'
  def privateDockerRegistry = "docker-repo.gaojihealth.cn:80"

  def testYaml = readFile('src/main/k8s/app-test.yml').replaceAll("@REPLACEMENT_APP_VERSION@", canaryVersion).replaceAll("@REPLACEMENT_DOCKER_REGISTRY@", privateDockerRegistry)
  def stageYaml = readFile('src/main/k8s/app-stage.yml').replaceAll("@REPLACEMENT_APP_VERSION@", canaryVersion).replaceAll("@REPLACEMENT_DOCKER_REGISTRY@", privateDockerRegistry)
  def prodYaml = readFile('src/main/k8s/app-prod.yml').replaceAll("@REPLACEMENT_APP_VERSION@", canaryVersion).replaceAll("@REPLACEMENT_DOCKER_REGISTRY@", privateDockerRegistry)

  def envStr
  def envName
  def k8sConfig
  switch(envToDeploy) {
    case "Test":
      envStr = 'Testing'
      envName = envTest
      k8sConfig = testYaml
      break;
    case "Stage":
      envStr = 'Staging'
      envName = envStage
      k8sConfig = stageYaml
      break;
    case "Prod":
      envStr = 'Production'
      envName = envProd
      k8sConfig = prodYaml
      break;
    default:
      envStr = 'Testing'
      envName = envTest
      k8sConfig = testYaml
  }

  echo 'NOTE: running pipelines for the first time will take longer as build and base docker images are pulled onto the node'


    stage 'Build Release'
    mavenCanaryRelease {
      version = canaryVersion
      mvnprofile = "prod,swagger,no-liquibase,zipkin"
    }

    stage 'Approve to test or stage or prod env ?'
    if ("$envToDeploy".trim() == "Test") {
      echo "Test env doesn't need approval"
    } else {
//    approve {
//      room = null
//      version = canaryVersion
//      console = fabric8Console
//      environment = envStr
//    }
      echo "Stage and Prod is currently skipped!"
    }

    stage 'Test env'
    if ("$envToDeploy".trim() == "Test") {
      //echo "Deploying svc to $envName with configuration $k8sConfig"
      def apiserver = "*********"
      def apitoken = "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
      sh "chmod +x ./bin/remoteDeploy && ./bin/remoteDeploy --cluster=$apiserver --ns=\"$envName\" --resource=\"${k8sConfig}\" --token=$apitoken"
    } else {
      echo "Skip Test env"
    }

    stage 'Stage env'
    if ("$envToDeploy".trim() == "Stage") {
      //echo "Deploying svc to $envName with configuration $k8sConfig"
      def apiserver = "*********"
      def apitoken = "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
      sh "chmod +x ./bin/remoteDeploy && ./bin/remoteDeploy --cluster=$apiserver --ns=\"$envName\" --resource=\"${k8sConfig}\" --token=$apitoken"
    } else {
      echo "Skip Stage env"
    }

    stage 'Production env'
    if ("$envToDeploy".trim() == "Prod") {
      def apiserver = "*********"
      def apiport = "1443"
      def apitoken = "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
      sh "chmod +x ./bin/remoteDeploy && ./bin/remoteDeploy --cluster=$apiserver --port=$apiport --ns=\"$envName\" --resource=\"${k8sConfig}\" --token=$apitoken"
      //echo "Deploying svc to $envName with configuration $k8sConfig"
      //kubernetesApply(file: k8sConfig, environment: envName)
    } else {
      echo "Skip Production env"
    }
        // new stage for check status
    stage 'Deployment Status'
    if ("$envToDeploy".trim() == "Test" || "$envToDeploy".trim() == "Stage") {
      sh "chmod +x ./bin/checkDeploy && ./bin/checkDeploy --cluster=\"*********\" --port=\"6443\" --token=\"****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\" --ns=\"$envName\" --appname=\"${env.JOB_NAME}\" --appversion=\"${canaryVersion}\""
    } else if ("$envToDeploy".trim() == "Prod") {
      deploy_status = sh script: "chmod +x ./bin/checkDeploy && ./bin/checkDeploy --cluster=\"*********\" --port=\"1443\" --token=\"******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\" --ns=\"$envName\" --appname=\"${env.JOB_NAME}\" --appversion=\"${canaryVersion}\"", returnStatus: true
      if("$deploy_status" != "0") {
        emailext(
            subject: "[RELEASE NOTIFY] - FAILED!!! ${env.JOB_NAME} with build ${env.BUILD_NUMBER}",
            body: """
Service: ${env.JOB_NAME}
Deploy_status: Failure
Build_number: ${env.BUILD_NUMBER}
Started by: $cur_user_id
Bundle branch list: $branch_list
Check details here: ${env.BUILD_URL}
Msg: build passed but deployment status check failed, please check logs for more details
                  """,
            to: "$email_list"
            )
        currentBuild.result = 'FAILURE'
        return
      }
    } else {
      echo "Invalid env param"
    }

    // new stage for post build steps
    // 1. code merging ONLY happens for Prod with successfully deployment
    stage 'Post build'
    if ("$envToDeploy".trim() == "Prod") {
      def merge_result = sh script: """
         echo "Preparing to commit changes ..."
         git checkout -- .
         git branch --set-upstream-to origin/master
         git push origin HEAD:master
         push_status=\$?
         if [ \$push_status -eq 0 ]; then
           echo "Successfully commit changes to remote master branch"
         else
           echo "Failed to commit changes to remote master branch"
           echo "If the build and deploy passed, you should do commit manually again"
           exit 1
         fi
         """, returnStatus: true
      if("$merge_result" != "0") {
        emailext(
            subject: "[RELEASE NOTIFY] - FAILED!!! ${env.JOB_NAME} with build ${env.BUILD_NUMBER}",
            body: """
Service: ${env.JOB_NAME}
Deploy_status: Failure
Build_number: ${env.BUILD_NUMBER}
Started by: $cur_user_id
Bundle branch list: $branch_list
Check details here: ${env.BUILD_URL}
Msg: build passed, deployment status check passed, but failed to automatically merge codes, please check logs for more details
                  """,
            to: "$email_list"
            )
        currentBuild.result = 'FAILURE'
        return
      } else {
        emailext(
            subject: "[RELEASE NOTIFY] - ${env.JOB_NAME} with build ${env.BUILD_NUMBER} passed",
            body: """
Service: ${env.JOB_NAME}
Deploy_status: Success
Build_number: ${env.BUILD_NUMBER}
Started by: $cur_user_id
Bundle branch list: $branch_list
Check details here: ${env.BUILD_URL}
            """,
            to: "$email_list"
            )
      }
    } else {
      echo "Skip code merging"
    }
  }
}


