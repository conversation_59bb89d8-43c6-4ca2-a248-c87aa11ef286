package com.cowell.purchase.utils;/**
 * Create by hfzhang
 *
 * @date 2018/7/16
 */

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cowell.purchase.config.SapConstants;
import com.cowell.purchase.constant.Constants;
import com.cowell.purchase.rest.errors.BusinessErrorException;
import com.cowell.purchase.rest.util.BigDecimalUtils;
import com.cowell.purchase.rest.vo.PageBase;
import com.cowell.purchase.service.feign.enums.ReturnCodeEnum;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.junit.Assert.assertEquals;

/**
 * <AUTHOR>
 * @date 2018/7/16
 */
public class CommonUtil {
    private static final Logger log = LoggerFactory.getLogger(CommonUtil.class);
    public static final String line = "/";
    public static final String DECIMAL_REGLAR = "^[0-9]+(.[0-9]{1,5})?$";
    public static final String GOODS_REGLAR = "^[0-9]{7,8}$";

//    public static void main(String[] args) {
//        String goodsCode = "b1.2000";
//        String DECIMAL_REGLAR = "^[0-9]+(.[0-9]{1,5})?$";
//        System.out.println(goodsCode.matches(DECIMAL_REGLAR));
//    }

    public static boolean goodsReglar(String param){
        if(StringUtils.isBlank(param)){
            return false;
        }
        return param.matches(GOODS_REGLAR);
    }

    public static boolean decimalReglar(String param){
        if(StringUtils.isBlank(param)){
            return false;
        }
        return param.matches(DECIMAL_REGLAR);
    }

    /**
     * 保证每个线程的本地变量都是安全的，不同线程之间并不共享相同的SimpleDateFormat，从而避免了线程安全问题。
     * 虽然这种方式性能高，但是占用内存，不过也不是很夸张
     */
    private static ThreadLocal<DateFormat> threadLocal = new ThreadLocal<DateFormat>() {
        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        }
    };

    /**
     * 字符串时间转换成Date
     * @param dateStr
     * @throws ParseException
     */
    public static Date strToDate(String dateStr){
        try {
            return threadLocal.get().parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String getReturnStr(String str){
        if(StringUtils.isBlank(str) || "null".equals(str)){
            return "";
        }

        return str.trim();
    }

    public static String getReturnNum(String num){
        if(StringUtils.isBlank(num) || "null".equals(num)){
            return "0";
        }

        return num.trim();
    }

    public static Byte transeSapX(String str){
        if(StringUtils.isBlank(str) || "null".equals(str)){
            return 0;
        }

        if(StringUtils.isNotBlank(str) && str.equals(SapConstants.ZCLOSE_X)){
            return 1;
        }
        return 0;
    }

    public static String reductionSapX(Byte code){
        log.debug("reductionSapX|code:{}.", code);
        if(Objects.isNull(code)){
            return StringUtils.EMPTY;
        }

        if(code == 1){
            return SapConstants.ZCLOSE_X;
        }
        return StringUtils.EMPTY;
    }

    public static String delSingleQuotationMark(String companyCode) {
        if(StringUtils.isBlank(companyCode) || !companyCode.contains(Constants.SINGLE_QUO)){
            return companyCode;
        }
        return companyCode.replace(Constants.SINGLE_QUO, StringUtils.EMPTY);
    }


    public static class Purchase{
        private String userName;
        private String passWord;

        public Purchase(){};
        public Purchase(String userName, String passWord){
            this.userName = userName;
            this.passWord = passWord;
        }

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }

        public String getPassWord() {
            return passWord;
        }

        public void setPassWord(String passWord) {
            this.passWord = passWord;
        }
    }

    /**
     * 时间类型转换成字符串
     * @param date
     */
    public static String dateToString(Date date){
        if(date == null){
            return null;
        }
        return threadLocal.get().format(date);
    }

    /**
     * 解析前端传过来的GMT（格林尼治时间）时间字符串，例如：Sun Aug 19 2018 15:55:21 GMT+0800
     * @param dateString
     */
    public static Date gmtFrontToDate(String dateString){
        if(StringUtils.isBlank(dateString)) {
            return null;
        }
        dateString = dateString.replace("GMT", "").replaceAll("\\(.*\\)", "");
        SimpleDateFormat format = new SimpleDateFormat("EEE MMM dd yyyy hh:mm:ss z", Locale.US);
        Date dateTrans = null;
        try {
            dateTrans = format.parse(dateString);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return dateTrans;
    }

    /**
     * 随机数
     * @return
     */
    public static String getRandomValue(){
        return  String.valueOf(Math.random());
    }

    /**
     * GMT格林尼治时间转Date
     * @param stringDate  例如：Thu Oct 16 07:13:48 GMT 2015
     */
    public static Date gmtToDate(String stringDate){
        //字符串转Date
        SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM ddHH:mm:ss 'GMT' yyyy",Locale.US);
        Date date = null;
        try {
            date = sdf.parse(stringDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;

        //Date转字符串
//        sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        System.out.println(sdf.format(new Date()));
    }

    /**
     * GMT格林尼治时间转Date
     * @param stringDate  例如：Fri Jul 27 14:06:12 CST 2018
     */
    public static Date gstToDate(String stringDate){
        //字符串转Date
        SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss z yyyy",Locale.US);
        Date date = null;
        try {
            date = sdf.parse(stringDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    /**
     * 字符串转换成int
     * @param str
     * @return int
     */
    public static int strToInt(String str){
        if(StringUtils.isBlank(str)){
            return Integer.parseInt(str);
        }else{
            return 0;
        }
    }

    /**
     * 字符串转换成long
     * @param str
     * @return long
     */
    public static long strToLong(String str){
        if(!StringUtils.isBlank(str)){
            return Long.parseLong(str);
        }else{
            return 0L;
        }
    }

    /**
     * 字符串数组转int集合
     * @param strArr 字符串数组
     * @return int集合
     */
    public static List<Integer> strArrToList(String[] strArr){
        if(strArr==null || strArr.length<=0){
            return null;
        }
        int size = strArr.length;
        List<Integer> ints = new LinkedList<>();
        for(int i=0;i<size;i++){
            ints.add(Integer.parseInt(strArr[i]));
        }
        return ints;
    }

    /**
     * 检查分页参数
     *
     * @param page     页码
     * @param pageSize 一页长度
     */
    public static void isNullPage(Integer page, Integer pageSize) {
        if (page == null || pageSize == null) {
            throw new BusinessErrorException(ReturnCodeEnum.PROPERTY_PAGE_PARAMETER_NULL);
        }
    }

    /**
     * 创建分页对象
     *
     * @param page     页码
     * @param pageSize 一页长度
     */
    public static PageBase buildPageBase(Integer page, Integer pageSize) {
        PageBase pageBase  = new PageBase();
        pageBase.setPage(page);
        pageBase.setPageSize(pageSize);
        if (pageSize > 0) {
            pageBase.setLimit(pageSize);
            if(page > 0){
                pageBase.setOffset(pageBase.getPageSize() * pageBase.getPage());
            }
        }
        return pageBase;
    }

    /**
     * 判断json是不是数组
     * @param requestBody
     * @param key
     * @return
     */
    public static JSONArray covertJsonMap(JSONObject requestBody, String key) {
        if(requestBody == null){
            return null;
        }
        Object data = requestBody.get(key);
        if (data instanceof Map) {
            return JSONArray.parseArray(new StringBuilder().append("[").append(requestBody.getString(key)).append("]").toString());
        } else {
            return requestBody.getJSONArray(key);
        }
    }

    /**
     * 将字符串数量截取并返回数字类型
     * @param str
     * @return
     */
    public static Integer covertStrToInt(String str){
        if(StringUtils.isBlank(str)){
            return 0;
        }
        try {
            if(str.contains(".")){
                str = str.substring(0, str.indexOf("."));
            }

            return Integer.parseInt(str);
        }catch (Exception e){
            log.error("covertStrToInt|error",e);
        }
        return 0;
    }

    /**
     * @param targetClazz java.lang.reflect.Field 的数据类型 clazz。 getType 方法获取
     * @return
     */
    public  static boolean isNumberType(Class<?> targetClazz) {
        // 判断包装类
        if (Number.class.isAssignableFrom(targetClazz)) {
            return true;
        }
        // 判断原始类,过滤掉特殊的基本类型
        if (targetClazz == boolean.class || targetClazz == char.class || targetClazz == void.class) {
            return false;
        }
        return targetClazz.isPrimitive();
    }

    public static int length(String value) {
        int valueLength = 0;
        String chinese = "[\u0391-\uFFE5]";
        /* 获取字段值的长度，如果含中文字符，则每个中文字符长度为2，否则为1 */
        for (int i = 0; i < value.length(); i++) {
            /* 获取一个字符 */
            String temp = value.substring(i, i + 1);
            /* 判断是否为中文字符 */
            if (temp.matches(chinese)) {
                /* 中文字符长度为2 */
                valueLength += 1;
            } else {
                /* 其他字符长度为1 */
                valueLength += 1;
            }
        }
        return valueLength;
    }

    /**
     * 去除下划线
     * @param target
     * @return
     */
    public static String rejectUnderLine(String target){
        if(StringUtils.isBlank(target)){
            return "";
        }
        if(target.contains("_") && target.indexOf("_")!=0){
            return target.split("_")[0];
        }
        return target;
    }

    public static List<String> spliteTrgetStr(String target){
        if(StringUtils.isEmpty(target)){
            return Collections.EMPTY_LIST;
        }
        return Arrays.asList(target.split(","));
    }

    /**
     * 创建bguid
     * @return
     */
    public static String createBguid(){
        String bguid = String.format("%s-%s", "SRM-B2B-SAAS", UUID.randomUUID().toString());
        //将uuid中的-横杠去掉
        bguid = org.apache.commons.lang3.StringUtils.replace(bguid, "-", "");
        return bguid;
    }

    /**
     * 组装下划线key
     * @param front
     * @param behind
     * @return
     */
    public static String createUnderLineKey(String front, String behind){
        return new StringBuilder().append(StringUtils.trim(front)).append(Constants.UNDER_LINE).append(StringUtils.trim(behind)).toString();
    }
    /**
     * 组装
     * @param front
     * @param behind
     * @return
     */
    public static String createMiddleLineKey(String front, String behind){
        return new StringBuilder().append(StringUtils.isNotBlank(front)?StringUtils.trim(front):StringUtils.EMPTY).append(Constants.MIDDLE_LINE).append(StringUtils.isNotBlank(behind)?StringUtils.trim(behind):StringUtils.EMPTY).toString();
    }

    public static String createMiddleLineKey(String front, String middle, String behind){
        return new StringBuilder().append(StringUtils.isNotBlank(front)?StringUtils.trim(front):StringUtils.EMPTY).append(Constants.MIDDLE_LINE).append(middle).append(Constants.MIDDLE_LINE).append(StringUtils.isNotBlank(behind)?StringUtils.trim(behind):StringUtils.EMPTY).toString();
    }

    public static String createMiddleLineKey(String front, String middle, String three, String behind){
        return new StringBuilder().append(StringUtils.isNotBlank(front)?StringUtils.trim(front):StringUtils.EMPTY).append(Constants.MIDDLE_LINE).append(StringUtils.isNotBlank(middle)?StringUtils.trim(middle):StringUtils.EMPTY).append(Constants.MIDDLE_LINE).append(StringUtils.isNotBlank(three)?StringUtils.trim(three):StringUtils.EMPTY).append(Constants.MIDDLE_LINE).append(StringUtils.isNotBlank(behind)?StringUtils.trim(behind):StringUtils.EMPTY).toString();
    }

    public static String createMiddleLineKey(String front, String middle, String three, String four, String behind){
        return new StringBuilder().append(StringUtils.isNotBlank(front)?StringUtils.trim(front):StringUtils.EMPTY)
            .append(Constants.MIDDLE_LINE).append(StringUtils.isNotBlank(middle)?StringUtils.trim(middle):StringUtils.EMPTY)
            .append(Constants.MIDDLE_LINE).append(StringUtils.isNotBlank(three)?StringUtils.trim(three):StringUtils.EMPTY)
            .append(Constants.MIDDLE_LINE).append(StringUtils.isNotBlank(four)?StringUtils.trim(four):StringUtils.EMPTY)
            .append(Constants.MIDDLE_LINE).append(StringUtils.isNotBlank(behind)?StringUtils.trim(behind):StringUtils.EMPTY).toString();
    }

    /**
     * 是否是图片URL
     * @param fileName
     * @return
     */
    public static Boolean createAbleImg(String fileName) {
        if(StringUtils.isBlank(fileName)){
            return false;
        }
        fileName = fileName.toLowerCase();
        log.debug("createAbleImg|..|fileName:{}..", fileName);
        if(fileName.endsWith("pdf") || fileName.endsWith("ofd")){
            return false;
        }
        return true;
    }

    /**
     * 获取文件名称
     * @param fileUrl
     * @return
     */
    public static String createFileName(String fileUrl) {
        if(StringUtils.isBlank(fileUrl)){
            return StringUtils.EMPTY;
        }
        String fileName = fileUrl.substring(fileUrl.lastIndexOf("/")+1, fileUrl.length());
        log.debug("createAbleImg|fileUrl:{}..|fileName:{}..", fileUrl, fileName);
        return fileName;
    }

    /**
     * 去掉竖线
     * @param param
     * @return
     */
    public static String delLine(String param){
        if(!param.contains(line)){
            return param;
        }
        return param.replace(line,StringUtils.EMPTY);
    }

    public static String objectToString(Object obj){
        if(Objects.isNull(obj)){
            return StringUtils.EMPTY;
        }
        return String.valueOf(obj);
    }

    public static BigDecimal objectToDecimal(Object obj){
        if(Objects.isNull(obj)){
            return BigDecimal.ZERO;
        }
        return BigDecimalUtils.parseTsscDecimal(String.valueOf(obj));
    }

    public static Integer objectToInteger(Object obj){
        if(Objects.isNull(obj)){
            return -1;
        }
        return Integer.parseInt(String.valueOf(obj));
    }

    public static Map<String, Object> objectToMap(Object obj) {
        if(Objects.isNull(obj)){
            return new HashMap<>(1);
        }
        String json = JSON.toJSONString(obj);
        if(StringUtils.isBlank(json)){
            return new HashMap<>(1);
        }
        return JSON.parseObject(json, Map.class);
    }

    /**
     * 手机号脱敏处理
     * 保留前3位和后4位，中间用4个星号代替
     * 例如：13800138000 脱敏后为 138****8000
     *
     * @param phone 原始手机号
     * @return 脱敏后的手机号，如果输入为空或格式不正确则返回原字符串
     */
    public static String desensitizerPhone(String phone) {
        // 检查手机号是否为空
        if (Objects.isNull(phone) || phone.trim().isEmpty()) {
            return phone;
        }
        // 去除空格串中的空格
        String trimmedPhone = phone.trim();
        // 简单校验手机号格式（11位数字）
        if (trimmedPhone.length() != 11 || !trimmedPhone.matches("\\d+")) {
            return phone;
        }
        // 进行脱敏处理
        return trimmedPhone.substring(0, 3) + "****" + trimmedPhone.substring(7);
    }
    /**
     * 地址脱敏处理
     * 保留前6位字符，后续字符用星号代替
     * 例如：北京市朝阳区某某街道 脱敏后为 北京市朝***
     *
     * @param address 原始地址
     * @return 脱敏后的地址，如果输入为空则返回原字符串
     */
    public static String desensitizerAddress(String address) {
        // 检查地址是否为空
        if (Objects.isNull(address) || address.trim().isEmpty()) {
            return address;
        }
        // 去除空格
        String trimmedAddress = address.trim();
        // 如果地址长度小于等于6位，直接返回原地址
        if (trimmedAddress.length() <= 6) {
            return trimmedAddress;
        }
        // 保留前6位，后面用星号代替（星号数量与被替换字符数相同）
        StringBuilder sb = new StringBuilder();
        sb.append(trimmedAddress.substring(0, 6));
        // 计算需要替换的字符数并添加对应数量的星号
        int replaceLength = trimmedAddress.length() - 6;
        for (int i = 0; i < replaceLength; i++) {
            sb.append("*");
        }
        return sb.toString();
    }
}
