package com.cowell.purchase.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.purchase.cache.CacheVar;
import com.cowell.purchase.constant.Constants;
import com.cowell.purchase.entity.PortalGoodsPriceStockBatch;
import com.cowell.purchase.entity.SupplierBasicInfo;
import com.cowell.purchase.entityTidb.MdmStoreBase;
import com.cowell.purchase.enums.CartDelTypeEnum;
import com.cowell.purchase.enums.CartGoodsTypeEnum;
import com.cowell.purchase.enums.CartOperateTypeEnums;
import com.cowell.purchase.enums.GiftChangeTypeEnum;
import com.cowell.purchase.enums.GiftOptionEnum;
import com.cowell.purchase.enums.GoodsLabelEnum;
import com.cowell.purchase.enums.GoodsMoldEnum;
import com.cowell.purchase.enums.OrderOperateTypeEnum;
import com.cowell.purchase.enums.PayTermEnum;
import com.cowell.purchase.enums.PromotionTypeEnums;
import com.cowell.purchase.enums.SettlementMethodEnum;
import com.cowell.purchase.enums.SrmTaxCodeEnum;
import com.cowell.purchase.enums.StatusEnums;
import com.cowell.purchase.mapper.extend.SupplierCompanyRelationExtendMapper;
import com.cowell.purchase.rest.errors.AmisBadRequestException;
import com.cowell.purchase.rest.errors.BusinessErrorException;
import com.cowell.purchase.rest.errors.ErrorCodeEnum;
import com.cowell.purchase.rest.util.BigDecimalUtils;
import com.cowell.purchase.rest.util.ConstantPool;
import com.cowell.purchase.service.*;
import com.cowell.purchase.service.dto.*;
import com.cowell.purchase.service.dto.amis.OptionDto;
import com.cowell.purchase.service.dto.cart.*;
import com.cowell.purchase.service.dto.order.CreateOrderResponse;
import com.cowell.purchase.service.dto.order.GoodsErrInfo;
import com.cowell.purchase.service.dto.order.OrderBaseDTO;
import com.cowell.purchase.service.dto.order.OrderBaseExtendDTO;
import com.cowell.purchase.service.dto.order.OrderDetailDTO;
import com.cowell.purchase.service.dto.order.OrderDetailInfoDTO;
import com.cowell.purchase.service.dto.order.OrderOperateResponseDTO;
import com.cowell.purchase.service.dto.order.ValidGoods;
import com.cowell.purchase.service.dto.order.ValidGoodsErrorMsg;
import com.cowell.purchase.service.dto.sap.*;
import com.cowell.purchase.service.dto.settle.SettleFelParam;
import com.cowell.purchase.service.dto.store.StoreConsignee;
import com.cowell.purchase.service.dto.store.StoreContact;
import com.cowell.purchase.service.feign.CartFeignService;
import com.cowell.purchase.service.feign.OrderFeignService;
import com.cowell.purchase.service.feign.UaaService;
import com.cowell.purchase.service.feign.vo.SpuListSimpleVo;
import com.cowell.purchase.service.order.OrderDetailLastSaleService;
import com.cowell.purchase.service.param.cart.*;
import com.cowell.purchase.service.param.order.OrderOperateParam;
import com.cowell.purchase.service.param.sap.SapUnifyRequest;
import com.cowell.purchase.service.sap.SapConnectService;
import com.cowell.purchase.service.sap.SapStockService;
import com.cowell.purchase.utils.CommonUtil;
import com.cowell.purchase.utils.DateUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SapLYServiceImpl implements SapLYService {

    @Autowired
    private NoSequenceService noSequenceService;
    @Autowired
    private OrderFeignService orderFeignService;
    @Autowired
    private StoreExtService storeExtService;
    @Autowired
    private SupplierBasicInfoService supplierBasicInfoService;
    @Autowired
    private SupplierCompanyRelationExtendMapper supplierCompanyRelationExtendMapper;
    @Autowired
    private OrderSearchEngineExtService orderSearchEngineExtService;
    @Autowired
    private OrderDetailLastSaleService orderDetailLastSaleService;
    @Autowired
    private ItemSearchEngineExtService itemSearchEngineExtService;
    @Autowired
    private SapStockService sapStockService;
    @Autowired
    private PriceCenterFeignExtService priceCenterFeignExtService;
    @Autowired
    private ItemCenterCpExtService itemCenterCpExtService;
    @Autowired
    private CartFeignService cartFeignService;
    @Autowired
    private OaRestService oaRestService;
    @Value("${portal.item.batch.limit:20}")
    private int itemBatchLimit;
    @Value("${portal.cart.async.switch:0}")
    private int asyncSwitch;
    @Value("${portal.cart.last.order.switch:0}")
    private int lastOrderSwitch;
    @Autowired
    private SapLYCheckService sapLYCheckService;
    @Autowired
    private SapConnectService sapConnectService;
    @Autowired
    private IRedissonCacheService redissonCacheService;
    @Autowired
    private PermissionExtService permissionExtService;
    @Autowired
    private PortalConfigService portalConfigService;
    @Autowired
    private UaaService uaaService;
    @Autowired
    private PortalStoreService portalStoreService;
    @Autowired
    private PortalOrderOperateService portalOrderOperateService;
    @Autowired
    private OrderFeignExtService orderFeignExtService;

    @Value("${portal.thirdOrder.business.bigStoreId:403231:312322203231}")
    private String thirdOrderBusiness;
    @Value("${portal.ableCommit.days:5}")
    private Long ableCommitDays;
    @Value("${portal.kp.supper.role:}")
    private String supperRole;
    @Value("${portal.kp.bizCode:PARTICIPATE_VOLUME_BUSINESS}")
    private String bizCode;
    @Value("${portal.cart.exption.switch:0}")
    private Integer cartExptionSwitch;
    @Value("${portal.cart.exption.count:2}")
    private Integer cartExptioncount;

    public static final long expirMillSecond = 365*24*60*60*1000;

    //默认十天
    @Value("${portal.cart.orderEditUserExpireTime:864000}")
    public Long orderEditUserExpireTime;

    @Value("${portal.cart.storePlatformUserIdExpireTime:36000}")
    private Long storePlatformUserIdExpireTime;
    @Value("${portal.cart.orderPromotionChannelExpireTime:36000}")
    private Long orderPromotionChannelExpireTime;


    @Value("${portal.cart.creditInfoExpireTime:3600}")
    private Long creditInfoExpireTime;
    @Value("${portal.cart.creditInfoQueryCache:0}")
    private Integer creditInfoQueryCache;
    @Value("${portal.cart.useSapCreditInfo:0}")
    private Integer useSapCreditInfo;

    @Value("${portal.cart.checkStockLess:1}")
    private Integer checkStockLess;

    @Override
    public CartUnifyDTO cartHandle(CartUnifyParam cartUnifyParam, TokenUserDTO userInfobyToken) {
        if(Objects.isNull(cartUnifyParam.getHandleType())){
            throw new AmisBadRequestException(ErrorCodeEnum.PARAM_ERROR_EXCEPTION);
        }
        log.info("购物车操作 用户[{}] 参数[{}]",userInfobyToken.getUserId(),JSON.toJSONString(cartUnifyParam));
        Long storeId = storeExtService.findBusinessStoreId(cartUnifyParam.getBusinessId());
        if(Objects.isNull(storeId)){
            throw new AmisBadRequestException("该连锁下没有运营店，请检查！");
        }

        switch (cartUnifyParam.getHandleType()) {
            case 1:
                return addCart(userInfobyToken, storeId, cartUnifyParam.getOrderId(),cartUnifyParam.getCartSaveParamList());
            case 2:
                return deleteCart(userInfobyToken, storeId, cartUnifyParam);
            case 3:
                return updateCart(userInfobyToken, storeId, cartUnifyParam.getCartUpdateParam());
            case 4:
                return listCart(userInfobyToken, storeId, cartUnifyParam.getCartListParam());
            case 5:
                return deleteBatchCart(userInfobyToken, storeId, cartUnifyParam.getCartBatchDelParam());
            case 6:
                return addBatchCart(userInfobyToken, cartUnifyParam.getBusinessId(), storeId, cartUnifyParam.getStep(), cartUnifyParam);
            case 7:
                return queryPromoByCart(userInfobyToken, cartUnifyParam.getBusinessId(), storeId, cartUnifyParam);
            case 8:
                return giftSave(userInfobyToken, cartUnifyParam.getBusinessId(), storeId, cartUnifyParam.getCartGiftSaveParam());
            case 9:
                return giftStockHandle(userInfobyToken, cartUnifyParam.getBusinessId(), storeId, cartUnifyParam);
            case 10:
                return changeGiftCart(userInfobyToken, cartUnifyParam.getBusinessId(), storeId, cartUnifyParam.getCartGiftChangeParam(),null);
            case 11:
                return promotionChange(userInfobyToken, cartUnifyParam);
            default:
                break;
        }
        return null;
    }

    /**
     * 功    能：促销选择
     * 作    者：王 伟
     * 时    间：2024/9/12
     */
    private CartUnifyDTO promotionChange(TokenUserDTO userInfoToken, CartUnifyParam cartUnifyParam) {
        CartPromotionChangeParam changeParam = cartUnifyParam.getCartPromotionChangeParam();
        Long channelStoreId = changeParam.getChannelStoreId();
        Long storePlatformUserId = findStorePlatformUserId(channelStoreId);
        CartSwitchParam cartSwitchParam = new CartSwitchParam();
        cartSwitchParam.setStoreId(cartUnifyParam.getStoreId());
        cartSwitchParam.setBizCode(bizCode);
        cartSwitchParam.setBusinessId(cartUnifyParam.getBusinessId());
        cartSwitchParam.setCartSwitchMultiParams(changeParam.getCartSwitchMultiParams());
        cartSwitchParam.setCartSwitchSingleParams(changeParam.getCartSwitchSingleParams());
        log.info("促销选择 门店id[{}] 门店用户id[{}] 参数[{}]",channelStoreId,storePlatformUserId,JSON.toJSONString(cartSwitchParam));
        cartFeignService.switchPromotion(bizCode,channelStoreId,storePlatformUserId,cartSwitchParam);
        return null;
    }

    /**
     * 功    能：赠品变更
     * 作    者：王 伟
     * 时    间：2024/8/30
     */
    private CartUnifyDTO changeGiftCart(TokenUserDTO tokenUserDTO, Long businessId, Long storeId, CartGiftChangeParam cartGiftChangeParam,List<GiftInfoDTO> giftInfoList) {

        if(CollectionUtils.isEmpty(giftInfoList)){
            log.info("赠品信息为空");
            return null;
        }

        CartGiftSaveParam cartGiftSaveParam = new CartGiftSaveParam();
        cartGiftSaveParam.setBizCode(bizCode);
        cartGiftSaveParam.setBusinessId(businessId);
        cartGiftSaveParam.setChannelStoreId(cartGiftChangeParam.getChannelStoreId());
        cartGiftSaveParam.setPromotionId(cartGiftChangeParam.getPromotionId());
        cartGiftSaveParam.setPromotionType(cartGiftChangeParam.getPromotionType());
        cartGiftSaveParam.setPromotionWay(cartGiftChangeParam.getPromotionWay());
        if(Objects.equals(PromotionTypeEnums.MULTI_GIFT.getType(), cartGiftChangeParam.getPromotionType())){
            cartGiftSaveParam.setOptionType(GiftOptionEnum.MULTI.getType());
        }else {
            cartGiftSaveParam.setOptionType(GiftOptionEnum.SINGLE.getType());
        }

        cartGiftSaveParam.setIds(cartGiftChangeParam.getGiftParentId()+"");

        List<CartGiftSaveDetailParam> detailParamList = Lists.newArrayList();

        for(GiftInfoDTO giftInfo : giftInfoList){

            CartGiftSaveDetailParam cartGiftSaveDetailParam = new CartGiftSaveDetailParam();

            cartGiftSaveDetailParam.setGiftId(Long.valueOf(cartGiftChangeParam.getGiftId()));
            cartGiftSaveDetailParam.setGiftType(1);
            cartGiftSaveDetailParam.setItemId(StringUtils.isBlank(giftInfo.getItemId())?0L:Long.valueOf(giftInfo.getItemId()));
            cartGiftSaveDetailParam.setRuleId(giftInfo.getRuleId());
            cartGiftSaveDetailParam.setOrderId(cartGiftChangeParam.getOrderId());
            cartGiftSaveDetailParam.setCount(giftInfo.getCount());
            //如果是同一个商品
            if(Objects.equals(giftInfo.getGiftId(), cartGiftChangeParam.getGiftId().toString())
                && Objects.equals(giftInfo.getRuleId(), cartGiftChangeParam.getRuleId())
                && Objects.equals(giftInfo.getPromotionId(), cartGiftChangeParam.getPromotionId().toString())){
                if(Objects.equals(GiftChangeTypeEnum.DELETE_GIFT.getType(), cartGiftChangeParam.getGiftChangeType())){
                    cartGiftSaveDetailParam.setCount("0");
                } else if (Objects.equals(GiftChangeTypeEnum.COUNT_CHANGE.getType(), cartGiftChangeParam.getGiftChangeType())) {
                    cartGiftSaveDetailParam.setCount(cartGiftChangeParam.getCount());
                }
            }
            if(StringUtils.isNotBlank(giftInfo.getIds())){
                cartGiftSaveParam.setIds(giftInfo.getIds());
            }
            detailParamList.add(cartGiftSaveDetailParam);
        }

        cartGiftSaveParam.setDetailParamList(detailParamList);

        //保存赠品库存信息
        log.info("保存赠品商品 参数[{}]",JSON.toJSONString(cartGiftSaveParam));
        giftSave(tokenUserDTO, businessId, storeId, cartGiftSaveParam);
        return null;
    }

    /**
     * 功    能：校验赠品库存
     * 作    者：王 伟
     * 时    间：2024/8/19
     */
    private void checkGiftStock(CartUnifyDTO cartUnifyDTO) {
        cartUnifyDTO.setStockLess(StatusEnums.NO.getCode());

        //如果不打开直接返回
        if(Objects.equals(checkStockLess, StatusEnums.NO.getCode())){
            return;
        }

        List<PortalLYSkuDTO>  cartListDtoList = cartUnifyDTO.getCartListDtoList();
        if(CollectionUtils.isEmpty(cartListDtoList)){
            return;
        }
        GiftStockInfoDTO giftStockInfoDTO = getGiftStockInfoDTO(cartListDtoList);
        if(CollectionUtils.isNotEmpty(giftStockInfoDTO.getAllGiftList())){
            List<PortalLYSkuDTO> editGiftList = giftStockInfoDTO.getAllGiftList().stream().filter(PortalLYSkuDTO::getGiftChange).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(editGiftList)){
                cartUnifyDTO.setStockLess(StatusEnums.YES.getCode());
            }
            cartUnifyDTO.setChangeGiftList(editGiftList);
        }
    }
    /**
     * 功    能：购物车赠品库存处理(循环处理赠品信息，临时方案)
     * 作    者：王 伟
     * 时    间：2024/8/16
     */
    private CartUnifyDTO giftStockHandle(TokenUserDTO tokenUserDTO, Long businessId, Long storeId, CartUnifyParam cartUnifyParam) {

        CartUnifyDTO cartUnifyDTO = listCart(tokenUserDTO, storeId, cartUnifyParam.getCartListParam());

        List<PortalLYSkuDTO>  cartListDtoList = cartUnifyDTO.getCartListDtoList();
        if(CollectionUtils.isEmpty(cartListDtoList)){
            throw new AmisBadRequestException("购物车为空");
        }
        //获取需要删除的商品和编辑数量的赠品
        GiftStockInfoDTO giftStockInfoDTO = getGiftStockInfoDTO(cartListDtoList);
        giftStockInfoDTO.setOrderId(Long.valueOf(cartUnifyDTO.getOrderId()));

        //调用购物车数量处理赠品数量
        giftStockHandle(tokenUserDTO, businessId, storeId, cartUnifyParam, giftStockInfoDTO);
        return null;
    }

    /**
     * 功    能：确定需要编辑的赠品和删除的赠品信息
     * 作    者：王 伟
     * 时    间：2024/8/19
     */
    private GiftStockInfoDTO getGiftStockInfoDTO(List<PortalLYSkuDTO> cartListDtoList) {
        //购物车所有商品的库存
        Map<String, Integer> stockMap = Maps.newHashMap();
        //使用商品的库存
        Map<String, Integer> usedMap = Maps.newHashMap();
        //剩余商品的库存
        Map<String, Integer> surplusMap = Maps.newHashMap();

        //主品的数量
        for (PortalLYSkuDTO portalLYSkuDTO : cartListDtoList) {
            //赠品目前不检验库存，这里需要验证
            if(!Objects.equals(portalLYSkuDTO.getSelfGoodsType(),1)){
                String stockStr =  portalLYSkuDTO.getAvailableCount();
                int stock = StringUtils.isEmpty(stockStr) ? 0 : new BigDecimal(stockStr).intValue();
                stockMap.put(portalLYSkuDTO.getGoodsNo(),stock);

                String countStr =  portalLYSkuDTO.getCount();
                Integer count = StringUtils.isEmpty(countStr) ? 0 : new BigDecimal(countStr).intValue();
                Integer oldCount = usedMap.get(portalLYSkuDTO.getGoodsNo());
                oldCount = Objects.isNull(oldCount) ? 0 : oldCount;
                usedMap.put(portalLYSkuDTO.getGoodsNo(),oldCount+count);
            }else {
                //库存信息需要拿主品的库存信息
                if(!stockMap.containsKey(portalLYSkuDTO.getGoodsNo())){
                    String stockStr =  portalLYSkuDTO.getAvailableCount();
                    int stock = StringUtils.isEmpty(stockStr) ? 0 : new BigDecimal(stockStr).intValue();
                    stockMap.put(portalLYSkuDTO.getGoodsNo(),stock);
                }
            }
        }
        log.info("赠品库存处理-库存信息 [{}]",stockMap);
        log.info("赠品库存处理-使用库存 [{}]",usedMap);
        stockMap.keySet().forEach(goodsNo->{
            if(usedMap.containsKey(goodsNo)){
                int used = usedMap.get(goodsNo);
                int stock = stockMap.get(goodsNo);
                int surplus = stock - used;
                surplusMap.put(goodsNo,surplus);
            }else{
                surplusMap.put(goodsNo,stockMap.get(goodsNo));
            }
        });
        log.info("赠品库存处理-剩余库存 [{}]",surplusMap);

        List<PortalLYSkuDTO> allGiftList = Lists.newArrayList();

        for (PortalLYSkuDTO portalLYSkuDTO : cartListDtoList) {
            if(Objects.equals(portalLYSkuDTO.getSelfGoodsType(), StatusEnums.YES.getCode())){
                log.info("赠品库存处理-赠品信息 [{}]",JSON.toJSONString(portalLYSkuDTO));
                String giftCountStr =  portalLYSkuDTO.getCount();
                int giftCount = StringUtils.isEmpty(giftCountStr) ? 0 : new BigDecimal(giftCountStr).intValue();
                int surplusCount = surplusMap.get(portalLYSkuDTO.getGoodsNo());
                //深拷贝 不影响列表返回的数据
                String copyStr = JSON.toJSONString(portalLYSkuDTO);
                PortalLYSkuDTO copy = JSON.parseObject(copyStr, PortalLYSkuDTO.class);
                copy.setGiftChange(false);
                if(surplusCount >= giftCount){
                    //如果商品库存大于赠品库存数量，赠品不处理，剩余库存减去当前赠品的数量
                    surplusCount = surplusCount - giftCount;
                    surplusMap.put(portalLYSkuDTO.getGoodsNo(),surplusCount);
                }else {
                    copy.setPromotionCount(copy.getCount());
                    //如果剩余数量是0，那么赠品需要删除
                    if(surplusCount <= 0){
                        copy.setCount("0");
                    }else {
                        //如果还有赠品，那么需要修改赠品数量
                        copy.setCount(surplusCount+"");
                    }
                    copy.setGiftChange(true);
                    //赠品数量库存不足，赠品数量就是剩余库存的数量,剩余库存变为0
                    surplusMap.put(portalLYSkuDTO.getGoodsNo(),0);
                }
                allGiftList.add(copy);
                log.info("赠品库存处理-设置后剩余 [{}]",surplusMap);
            }
        }
        GiftStockInfoDTO giftStockInfoDTO = new GiftStockInfoDTO();
        giftStockInfoDTO.setAllGiftList(allGiftList);
        return giftStockInfoDTO;
    }

    /**
     * 功    能：调用购物车数量处理赠品数量
     * 作    者：王 伟
     * 时    间：2024/8/17
     */
    private void giftStockHandle(TokenUserDTO tokenUserDTO, Long businessId, Long storeId, CartUnifyParam cartUnifyParam, GiftStockInfoDTO giftStockInfoDTO) {

        List<PortalLYSkuDTO> allGiftList = giftStockInfoDTO.getAllGiftList();
        List<PortalLYSkuDTO> editGiftList = allGiftList.stream().filter(PortalLYSkuDTO::getGiftChange).collect(Collectors.toList());
        log.info("赠品库存处理-修改赠品信息 [{}]",JSON.toJSONString(editGiftList));
        //删除赠品数量
        if(CollectionUtils.isEmpty(editGiftList)){
            return;
        }
        //修改赠品数量
        //根据促销和规则id分组
        Map<String, List<PortalLYSkuDTO>> giftGroup =  allGiftList.stream().collect(Collectors.groupingBy(v->{
            String giftInfo = v.getGiftInfo();
            List<GiftInfoDTO> giftInfoDTOS = JSON.parseArray(giftInfo,GiftInfoDTO.class);
            return v.getPromotionId()+Constants.MIDDLE_LINE+giftInfoDTOS.get(0).getIds();

        }));

        for (String key : giftGroup.keySet()){
            List<PortalLYSkuDTO> editPromotionGiftList = giftGroup.get(key);

            boolean isGiftChange =  editPromotionGiftList.stream().anyMatch(PortalLYSkuDTO::getGiftChange);

            //如果赠品数量没有变更
            if(!isGiftChange){
                continue;
            }

            Integer promotionType = editPromotionGiftList.get(0).getPromotionType();

            //如果不是多品买赠或者单品买赠 不处理
            if(!Objects.equals(PromotionTypeEnums.MULTI_GIFT.getType(), promotionType)
                && !Objects.equals(PromotionTypeEnums.SINGLE_GIFT.getType(), promotionType)){
                continue;
            }

            CartGiftSaveParam cartGiftSaveParam = new CartGiftSaveParam();
            cartGiftSaveParam.setBizCode(bizCode);
            cartGiftSaveParam.setBusinessId(businessId);
            cartGiftSaveParam.setChannelStoreId(cartUnifyParam.getCartListParam().getChannelStoreId());
            cartGiftSaveParam.setPromotionId(Long.valueOf(editPromotionGiftList.get(0).getPromotionId()));
            cartGiftSaveParam.setPromotionType(editPromotionGiftList.get(0).getPromotionType());
            cartGiftSaveParam.setPromotionWay(editPromotionGiftList.get(0).getPromotionWay());
            if(Objects.equals(PromotionTypeEnums.MULTI_GIFT.getType(), promotionType)){
                cartGiftSaveParam.setOptionType(GiftOptionEnum.MULTI.getType());
            }else {
                cartGiftSaveParam.setOptionType(GiftOptionEnum.SINGLE.getType());
            }

            List<Long> idsList = Lists.newArrayList();
            String giftInfo = editPromotionGiftList.get(0).getGiftInfo();
            if(StringUtils.isNotBlank(giftInfo)){
                List<GiftInfoDTO> giftInfoDTOS = JSON.parseArray(giftInfo,GiftInfoDTO.class);
                String ids = giftInfoDTOS.get(0).getIds();
                cartGiftSaveParam.setIds(ids);

                List<String> idsArr = Lists.newArrayList(ids.split(Constants.COMMA));
                idsList.addAll(idsArr.stream().map(v->Long.valueOf(v)).collect(Collectors.toList()));
            }

            List<CartDelParam> deleteList = Lists.newArrayList();
            List<CartGiftSaveDetailParam> detailParamList = Lists.newArrayList();
            for (PortalLYSkuDTO portalLYSkuDTO : editPromotionGiftList){
                if(Objects.equals(PromotionTypeEnums.MULTI_GIFT.getType(), promotionType) && Integer.valueOf(portalLYSkuDTO.getCount()) <= 0){
                    String giftInfoStr = portalLYSkuDTO.getGiftInfo();
                    String mutiSkuId = "0";
                    if(StringUtils.isNotBlank(giftInfoStr)){
                        List<GiftInfoDTO> giftInfoDTOS = JSON.parseArray(giftInfoStr,GiftInfoDTO.class);
                        mutiSkuId = giftInfoDTOS.get(0).getSkuId();
                    }
                    CartDelParam cartDelParam = new CartDelParam();
                    cartDelParam.setBizCode(bizCode);
                    cartDelParam.setBusinessId(businessId);
                    cartDelParam.setChannelStoreId(cartUnifyParam.getCartListParam().getChannelStoreId());
                    cartDelParam.setIds(idsList);
                    cartDelParam.setItemId(portalLYSkuDTO.getItemId());
                    cartDelParam.setOrderId(giftStockInfoDTO.getOrderId());
                    cartDelParam.setDelType(CartDelTypeEnum.DEL_GIFT.getType());
                    CartGiftDelParam cartGiftDelParam = new CartGiftDelParam();
                    cartGiftDelParam.setPromotionId(portalLYSkuDTO.getPromotionId());
                    cartGiftDelParam.setRuleId(portalLYSkuDTO.getRuleId());
                    cartGiftDelParam.setPromotionType(portalLYSkuDTO.getPromotionType());
                    cartGiftDelParam.setPromotionWay(portalLYSkuDTO.getPromotionWay());
                    cartGiftDelParam.setSkuId(Long.valueOf(mutiSkuId));
                    cartDelParam.setCartGiftDelParam(cartGiftDelParam);
                    deleteList.add(cartDelParam);
                }else {
                    CartGiftSaveDetailParam cartGiftSaveDetailParam = new CartGiftSaveDetailParam();
                    cartGiftSaveDetailParam.setCount(portalLYSkuDTO.getCount());
                    cartGiftSaveDetailParam.setGiftId(Long.valueOf(portalLYSkuDTO.getGiftId()));
                    cartGiftSaveDetailParam.setGiftType(portalLYSkuDTO.getSelfGoodsType());
                    cartGiftSaveDetailParam.setItemId(portalLYSkuDTO.getItemId());
                    cartGiftSaveDetailParam.setRuleId(portalLYSkuDTO.getRuleId());
                    cartGiftSaveDetailParam.setOrderId(giftStockInfoDTO.getOrderId());
                    detailParamList.add(cartGiftSaveDetailParam);
                }

            }

            if(CollectionUtils.isNotEmpty(deleteList)){
                log.info("删除赠品商品 参数[{}]",JSON.toJSONString(deleteList));
                for (CartDelParam cartDelParam : deleteList){
                    deleteGiftCart(cartDelParam.getChannelStoreId(),cartDelParam);
                }
            }
            if(CollectionUtils.isNotEmpty(detailParamList)){
                cartGiftSaveParam.setDetailParamList(detailParamList);
                //保存赠品库存信息
                log.info("保存赠品商品 参数[{}]",JSON.toJSONString(cartGiftSaveParam));
                giftSave(tokenUserDTO, businessId, storeId, cartGiftSaveParam);
            }
        }
    }

    /**
     * 功    能：赠品保存
     * 作    者：王 伟
     * 时    间：2024/8/14
     */
    private CartUnifyDTO giftSave(TokenUserDTO tokenUserDTO, Long businessId, Long storeId, CartGiftSaveParam cartGiftSaveParam) {
        try {

            Long channelStoreId = cartGiftSaveParam.getChannelStoreId();

            Long storePlatformUserId = findStorePlatformUserId(channelStoreId);

            log.info("赠品保存信息 channelStoreId[{}] storePlatformUserId[{}] 参数[{}]",channelStoreId,storePlatformUserId,JSON.toJSONString(cartGiftSaveParam));
            cartFeignService.giftSave(bizCode,channelStoreId,storePlatformUserId,cartGiftSaveParam);
            return null;
        }catch (Exception e){
            log.warn("赠品保存数据错误",e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    /**
     * 检查并通过购物车查询促销活动
     * @return
     */
    private CartUnifyDTO queryPromoByCart(TokenUserDTO userDTO, Long businessId, Long storeId, CartUnifyParam cartUnifyParam) {
        Long userId = userDTO.getUserId();
        List<CartSaveParam> cartSaveParamList = cartUnifyParam.getCartSaveParamList();
        CartSaveParam cartSaveParam = cartSaveParamList.stream().findFirst().get();
        Long channelStoreId = cartSaveParam.getChannelStoreId();
        Long storePlatformUserId = findStorePlatformUserId(channelStoreId);
        //先清空购物车
        log.info("查询促销清空购物车 门店[{}] 平台用户id[{}] 门店用户id[{}]",channelStoreId,userDTO.getPlatformUserId(),storePlatformUserId);
        //此处需要删除快批购物车（以下查询和添加同样如此），身份标识还是快批
        cartFeignService.deleteUserCart(bizCode,  channelStoreId, storePlatformUserId, storePlatformUserId,storePlatformUserId,null,null);
        //加购
        addCart(userDTO, storeId, null, cartSaveParamList);
        //查询列表
        CartListParam cartListParam = new CartListParam();
        cartListParam.setBusinessIds(Lists.newArrayList(businessId));
        cartListParam.setBusinessId(businessId);
        cartListParam.setChannelStoreId(channelStoreId);
        cartListParam.setStoreId(storeId);
        cartListParam.setStoreIds(Lists.newArrayList(storeId));
        CartUnifyDTO cartUnifyDTO = listCart(userDTO, storeId, cartListParam);
        return cartUnifyDTO;
    }

    @Override
    public OrderInfoDTO createOrder(PortalLYOrderHeadDTO cartUnifyParam, TokenUserDTO userInfobyToken) {
        if(Objects.isNull(cartUnifyParam.getChannelStoreId())){
            throw new AmisBadRequestException("该连锁下没有运营店，请检查！");
        }

        Long storeId = storeExtService.findBusinessStoreId(cartUnifyParam.getBusinessId());
        if(Objects.isNull(storeId)){
            throw new AmisBadRequestException("该连锁下没有运营店，请检查！");
        }

        //门店是否被冻结
        boolean isComFreeze = portalStoreService.isComFreeze(cartUnifyParam.getBusinessId(),cartUnifyParam.getChannelStoreId());
        if(isComFreeze){
            throw new AmisBadRequestException("门店被冻结，无法操作");
        }

        //更新最后一笔购物车数据
        CartUpdateParam cartUpdateParam = cartUnifyParam.getCartUpdateParam();
        if(Objects.nonNull(cartUpdateParam)){
            //更新购物车
            updateCart(userInfobyToken,storeId,cartUnifyParam.getCartUpdateParam());
        }

        //获取购物车信息
        CartUnifyDTO cartUnifyDTO = findCartInfo(cartUnifyParam, userInfobyToken, storeId);
        if(CollectionUtils.isEmpty(cartUnifyDTO.getCartListDtoList()) || StringUtils.isBlank(cartUnifyDTO.getOrderId())){
            throw new AmisBadRequestException("购物车无商品，请检查！");
        }
        String cartOrderId = cartUnifyDTO.getOrderId();

        try {
            List<PortalLYContactsDTO> contactsDTOList = cartUnifyParam.getContactsDTOList();
            log.info("createOrder|contactsDTOList:{}.", JSON.toJSONString(contactsDTOList));

            if(CollectionUtils.isEmpty(contactsDTOList)){
                throw new AmisBadRequestException("联系人信息为空！");
            }
            PortalLYContactsDTO contactsDTO = contactsDTOList.get(0);
            if(StringUtils.isBlank(contactsDTO.getContactsName()) || StringUtils.isBlank(contactsDTO.getContactsPhone())
                || StringUtils.isBlank(contactsDTO.getDeliveryPerson()) || StringUtils.isBlank(contactsDTO.getDeliveryPhone())){
                throw new AmisBadRequestException("联系人信息错误！");
            }

            log.info("createOrder|contactsDTO:{}.", JSON.toJSONString(contactsDTO));
            CreateOrderRequestBody orderRequestBody = new CreateOrderRequestBody();

            if(Objects.nonNull(cartUnifyParam.getShiftId())){
                orderRequestBody.setShiftId(cartUnifyParam.getShiftId());
            }
            orderRequestBody.setContacts(contactsDTO.getContactsName());
            orderRequestBody.setContactsPhone(contactsDTO.getContactsPhone());
            orderRequestBody.setConsignee(contactsDTO.getDeliveryPerson());
            orderRequestBody.setConsigneePhone(contactsDTO.getDeliveryPhone());

            log.info("createOrder|contacts:{}.consignee:{}.contactsPhone:{}.consigneePhone:{}.", orderRequestBody.getContacts(),orderRequestBody.getConsignee(),orderRequestBody.getContactsPhone(),orderRequestBody.getConsigneePhone());

            if(StringUtils.isBlank(orderRequestBody.getContacts()) || StringUtils.isBlank(orderRequestBody.getContactsPhone())){
                throw new AmisBadRequestException("联系人信息为空！");
            }

            orderRequestBody.setBusinessId(cartUnifyParam.getBusinessId());
            orderRequestBody.setConsigneeAddress(StringUtils.trimToNull(cartUnifyParam.getDeliveryAddress()));
            orderRequestBody.setChannelStoreId(cartUnifyParam.getChannelStoreId());
            orderRequestBody.setRemarks(cartUnifyParam.getComment());
            orderRequestBody.setStoreId(storeId);
            orderRequestBody.setTotalAmount(BigDecimalUtils.convertFenByYuan(cartUnifyDTO.getTotalAmount()));
            orderRequestBody.setPayType(cartUnifyParam.getPayType());//支付类型
            orderRequestBody.setPayAmount(BigDecimalUtils.convertFenByYuan(cartUnifyDTO.getPayAmount()));
            orderRequestBody.setFromCart(Boolean.TRUE);
//            orderRequestBody.setType(68);
            orderRequestBody.setDeliveryType(cartUnifyParam.getDeliveryType());
            orderRequestBody.setExpressType(4);
//        orderRequestBody.setTotalAmount();
            orderRequestBody.setUserId(userInfobyToken.getUserId());
            orderRequestBody.setPlatformUserId(userInfobyToken.getUserId());
            orderRequestBody.setExtendParamMap(cartUnifyDTO.getExtendParamMap());
            if(StringUtils.isNotBlank(cartUnifyDTO.getOrderId())){
                orderRequestBody.setOrderId(Long.parseLong(cartUnifyDTO.getOrderId()));
            }
            Long storePlatformUserId = findStorePlatformUserId(cartUnifyParam.getChannelStoreId());
            orderRequestBody.setStorePlatformUserId(storePlatformUserId);
            orderRequestBody.setValidStockStatus(cartUnifyDTO.getValidStockStatus());
            if(CollectionUtils.isNotEmpty(cartUnifyDTO.getPromotionInfo())){
                orderRequestBody.setPromotionInfos(cartUnifyDTO.getPromotionInfo());
            }
            orderRequestBody.setOrderDetail(createOrderDetail(cartUnifyDTO));
//            log.info("createOrder|orderRequestBody:{}.", orderRequestBody);
//            log.info("createOrder|detail:{}.", orderRequestBody.getOrderDetail());
            log.info("创建订单 提交参数[{}]",JSON.toJSONString(orderRequestBody));
            long start = System.currentTimeMillis();
            ResponseEntity<CreateOrderResponse> responseEntity = orderFeignService.createOrder(Constants.PARTICIPATE_VOLUME_BUSINESS, orderRequestBody);
            log.info("createOrder|耗时:{}.|responseEntity:{}.", (System.currentTimeMillis()-start),JSONObject.toJSONString(responseEntity));
            if (responseEntity == null || Objects.isNull(responseEntity.getBody()) || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))) {
                log.warn("createOrder|调用创建订单给失败, 返回结果 -> {}", responseEntity);
                throw new AmisBadRequestException("创建订单失败");
            }
            log.info("orderId:{}.", responseEntity.getBody());
            //成功之后删除缓存价格

//            String priceRedisKey = CommonUtil.createMiddleLineKey(ConstantPool.REDIS_PORTAL_B2B_CART_GOODS_KEY,String.valueOf(storeId),String.valueOf(cartUnifyParam.getChannelStoreId()));
//            redissonCacheService.del(priceRedisKey);

            CreateOrderResponse response = responseEntity.getBody();
            if(Objects.isNull(response) || StringUtils.isBlank(response.getOrderId())){
                log.warn("createThirdOrder|调用创建订单返回结果为空, 返回结果 -> {}", response);
                throw new AmisBadRequestException("创建订单失败");
            }
            OrderInfoDTO orderInfoDTO = new OrderInfoDTO();
            orderInfoDTO.setOrderNo(response.getOrderId());
            orderInfoDTO.setCartOrderId(cartOrderId);
            return orderInfoDTO;
        } catch (BusinessErrorException e) {
            log.warn("createOrder|创建订单失败.", e);
            log.info("createOrder|创建订单失败 [{}] [{}]", e.getErrorCode(),e.getTitle());
            if(Objects.equals(e.getErrorCode(),ErrorCodeEnum.CREATE_ORDER_ERROR_TIP_EXCEPTION.getCode())){
                throw new AmisBadRequestException(ErrorCodeEnum.CREATE_ORDER_ERROR_TIP_EXCEPTION,e.getMessage(),e.getDetail());
            }
            throw new AmisBadRequestException(e.getMessage());
        }catch (Exception e) {
            log.warn("createOrder|创建订单异常.", e);
            if(e instanceof RetryableException){
                throw new AmisBadRequestException(ErrorCodeEnum.PORTAL_CREATE_ORDER_TIMEOUT_EXCEPTION);
            }
            throw new AmisBadRequestException(ErrorCodeEnum.PORTAL_CREATE_ORDER_EXCEPTION.getMsg()+": "+e.getMessage());
        }

    }

    private String failureTip(BusinessErrorException e) {
        String errorMessage = e.getMessage();
        List<ValidGoods> list = JSON.parseArray(errorMessage, ValidGoods.class);

        Map<String,Set<String>> map = Maps.newHashMap();
        for(ValidGoods validGoods:list){
            List<ValidGoodsErrorMsg> errorTypeList = validGoods.getErrorTypeList();
            if(CollectionUtils.isEmpty(errorTypeList)){
                continue;
            }
            for(ValidGoodsErrorMsg validGoodsErrorMsg:errorTypeList){
                Set<String> goodsNoSet = map.get(validGoodsErrorMsg.getErrorMsg());
                if(CollectionUtils.isEmpty(goodsNoSet)){
                    goodsNoSet = Sets.newHashSet();
                }
                goodsNoSet.add(validGoods.getGoodsNo());
                map.put(validGoodsErrorMsg.getErrorMsg(),goodsNoSet);
            }
        }
        if(map.size() == 0){
            throw new AmisBadRequestException(e.getMessage());
        }
        String message = "";
        for(Map.Entry<String,Set<String>> entry:map.entrySet()){
            message = message + StringUtils.join(entry.getValue(),",") + ":" + entry.getKey() + "<br>";
        }
        return message;
    }

    /**
     * 功    能：获取购物车信息
     * 作    者：王 伟
     * 时    间：2024/8/13
     */
    private CartUnifyDTO findCartInfo(PortalLYOrderHeadDTO cartUnifyParam, TokenUserDTO userInfobyToken, Long storeId) {
        CartListParam cartListParam = new CartListParam();
        cartListParam.setBizCode(Constants.PARTICIPATE_VOLUME_BUSINESS);
        cartListParam.setBusinessId(cartUnifyParam.getBusinessId());
        cartListParam.setChannelStoreId(cartUnifyParam.getChannelStoreId());
        if(StringUtils.isNotBlank(cartUnifyParam.getOrderId())){
            cartListParam.setOrderId(Long.parseLong(cartUnifyParam.getOrderId()));
        }
        CartUnifyDTO cartUnifyDTO = listCart(userInfobyToken, storeId, cartListParam);
        log.info("购物车信息 [{}]",JSON.toJSONString(cartUnifyDTO));
        return cartUnifyDTO;
    }

    /**
     * 功    能：订单详情
     * 作    者：王 伟
     * 时    间：2024/8/9
     */
    private List<CreateOrderDetailRequestBody> createOrderDetail(CartUnifyDTO cartUnifyDTO) {
        List<PortalLYSkuDTO> skuDTOList = cartUnifyDTO.getCartListDtoList();
        if(CollectionUtils.isEmpty(skuDTOList)){
            throw new AmisBadRequestException("订单明细为空");
        }
        Set<String> orderDetailIdSet = Sets.newHashSet();
        List<CreateOrderDetailRequestBody> detailRequestBodyList = new ArrayList<>(skuDTOList.size());
        for(int i=0;i<skuDTOList.size();i++){
            CreateOrderDetailRequestBody detailRequestBody = new CreateOrderDetailRequestBody();
            PortalLYSkuDTO v = skuDTOList.get(i);
            detailRequestBody.setExtendMap(v.getExtendMap());
            detailRequestBody.setPromotionGift(v.getSelfGoodsType());
            detailRequestBody.setOrderDetailRowNo(String.valueOf(i+1));
            detailRequestBody.setSkuPrice(BigDecimalUtils.convertFenByDecimalYuan(BigDecimalUtils.parseTsscDecimal(v.getSettleprice())));
            detailRequestBody.setSkuPriceSup(BigDecimalUtils.convertFenByDecimalYuanSup(BigDecimalUtils.parseTsscDecimal(v.getSettleprice())));
            detailRequestBody.setSkuCount(StringUtils.isNotBlank(v.getCount())?BigDecimalUtils.parseTsscDecimal(v.getCount()).intValue():0);
            detailRequestBody.setItemId(v.getItemId());
//            detailRequestBody.setSettlePrice(detailRequestBody.getSkuPrice()*detailRequestBody.getSkuCount());
            detailRequestBody.setSettlePrice(BigDecimalUtils.convertFenByYuan(v.getPayAmount()));
            detailRequestBody.setGoodsNo(v.getGoodsNo());
            detailRequestBody.setSkuName(v.getGoodsName());
            detailRequestBody.setEditPrice(v.getEditPrice());
            detailRequestBody.setLockStockCount(v.getLockStockCount());

            if(StringUtils.isNotBlank(v.getOrderDetailId()) && !orderDetailIdSet.contains(v.getOrderDetailId())){
                detailRequestBody.setDetailId(Long.parseLong(v.getOrderDetailId()));
                orderDetailIdSet.add(v.getOrderDetailId());
            }
            if(CollectionUtils.isNotEmpty(v.getPromotionInfo())){
                detailRequestBody.setPromotionInfos(v.getPromotionInfo());
            }
//            if(detailRequestBody.getSettlePrice()<=0){
//                log.warn("createOrderDetail|goodsNo:{}.|商品总价或单价有误!", v.getGoodsNo());
//                throw new AmisBadRequestException(ErrorCodeEnum.PORTAL_CREATE_ORDER_PRICE_EXCEPTION);
//            }
            detailRequestBodyList.add(detailRequestBody);
        }
        return detailRequestBodyList;
    }

    private CartUnifyDTO addBatchCart(TokenUserDTO tokenUserDTO, Long businessId, Long storeId, Integer step, CartUnifyParam cartUnifyParam) {

        try {
            log.info("批量加车-开始 用户id[{}] 连锁[{}] 门店[{}] 步骤[{}] 参数[{}]",tokenUserDTO.getUserId(),  businessId, storeId, step,JSON.toJSONString(cartUnifyParam));
            Long userId = tokenUserDTO.getUserId();
            Long platformUserId = tokenUserDTO.getPlatformUserId();
            List<CartSaveParam> cartSaveParamList = cartUnifyParam.getCartSaveParamList();
            if(CollectionUtils.isEmpty(cartSaveParamList)){
                throw new AmisBadRequestException("加车商品为空");
            }
            CartSaveParam cartSaveParam = cartSaveParamList.stream().findFirst().get();
            Long channelStoreId = cartSaveParam.getChannelStoreId();
            Map<String,String> settlePriceMap = Maps.newHashMap();
            List<PortalLYSkuDTO> portalLYSkuDTOList = cartSaveParamList.stream().map(v->{
                PortalLYSkuDTO skuDTO = new PortalLYSkuDTO();
                skuDTO.setItemId(v.getItemId());
                skuDTO.setGoodsNo(v.getGoodsNo());
                skuDTO.setCount(String.valueOf(v.getCount()));
                skuDTO.setSettleprice(v.getChannelPrice());
                skuDTO.setEditPrice(v.getEditPrice());
                skuDTO.setWholesalePrice(v.getWholesalePrice());
                settlePriceMap.put(v.getGoodsNo(),v.getChannelPrice());

                return skuDTO;
            }).collect(Collectors.toList());


            List<PortalLYSkuDTO> rerurnGoodsList = sapLYCheckService.auditAndRerurnGoodsList(businessId, storeId, channelStoreId, portalLYSkuDTOList);
            log.info("批量加车-过滤前 [{}]",rerurnGoodsList.size());
            long errorCount = rerurnGoodsList.stream().filter(v->!v.getAbleAddCart()).count();
            log.info("批量加车-错误数量 [{}]",errorCount);
            CartUnifyDTO cartUnifyDTO = new CartUnifyDTO();
            cartUnifyDTO.setCartListDtoList(rerurnGoodsList);
            if(1 == step && errorCount>0){
                log.info("addBacthCart|第一步加购返回错误信息");
                return cartUnifyDTO;
            }
            List<PortalLYSkuDTO> rightRerurnGoodsList = rerurnGoodsList.stream().filter(v->v.getAbleAddCart()).collect(Collectors.toList());
            log.info("addBacthCart|rightRerurnGoodsList|过滤后size:{}.:{}.",rightRerurnGoodsList.size());
            if(CollectionUtils.isEmpty(rightRerurnGoodsList)){
                log.info("addBacthCart|要发送的数据为空");
                return cartUnifyDTO;
            }
            log.info("批量加车-数据 [{}]",JSON.toJSONString(rightRerurnGoodsList));
            log.info("批量加车-价格参数 [{}]",JSON.toJSONString(settlePriceMap));

            List<CartSaveParam> finalCartSaveParamList = rightRerurnGoodsList.stream().map(v->{
                CartSaveParam saveParam = new CartSaveParam();
                saveParam.setBusinessId(businessId);
                saveParam.setChannelStoreId(channelStoreId);
                saveParam.setItemId(v.getItemId());
                saveParam.setGoodsNo(v.getGoodsNo());
                saveParam.setCount(Long.parseLong(v.getCount()));
                saveParam.setChannelPrice(v.getSettleprice());
                saveParam.setAblePromo(v.getAblePromo());

                //传入商品的金额和目录价不相同
                String channelPrice = settlePriceMap.get(v.getGoodsNo());
                if(NumberUtils.isNumber(v.getWholesalePrice())&& NumberUtils.isNumber(channelPrice)
                    && new BigDecimal(v.getWholesalePrice()).compareTo(new BigDecimal(channelPrice)) != 0){
                    saveParam.setEditPrice(StatusEnums.YES.getCode());
                    saveParam.setPrice(BigDecimalUtils.convertFenByYuan(channelPrice).longValue());
                }

                if(Objects.equals(v.getGoodsMold(), GoodsMoldEnum.LOCK_PRICE.getCode())){
                    saveParam.setEditPrice(StatusEnums.YES.getCode());
                    saveParam.setPrice(BigDecimalUtils.convertFenByYuan(channelPrice).longValue());
                }

                return saveParam;
            }).collect(Collectors.toList());
            //添加购物车
            addCart(tokenUserDTO, storeId, null,finalCartSaveParamList);
            return cartUnifyDTO;
        } catch (Exception e) {
            log.warn("addCart|批量加购失败。",e);
            throw e;
        }
    }

    private String covertOrderNo(String orderNoStr){
        if(StringUtils.isBlank(orderNoStr)){
            return StringUtils.EMPTY;
        }
        try {
            if(orderNoStr.contains("=") && orderNoStr.contains("}")){
                String orderNoStr2 = orderNoStr.split("=")[1];
//                log.info("orderNoStr:{}.", orderNoStr2);
                return orderNoStr2.substring(0,orderNoStr2.indexOf("}"));
            }
            return orderNoStr;
        } catch (Exception e) {
            log.warn("covertOrderNo|warn.", e);
            return orderNoStr;
        }
    }

    private List<CreateOrderDetailRequestBody> createOrderDetail(PortalLYOrderHeadDTO cartUnifyParam) {
        if(CollectionUtils.isEmpty(cartUnifyParam.getSkuDTOList())){
            throw new AmisBadRequestException("订单明细为空");
        }

        List<CreateOrderDetailRequestBody> detailRequestBodyList = new ArrayList<>(cartUnifyParam.getSkuDTOList().size());
        for(int i=0;i<cartUnifyParam.getSkuDTOList().size();i++){
            CreateOrderDetailRequestBody detailRequestBody = new CreateOrderDetailRequestBody();
            PortalLYSkuDTO v = cartUnifyParam.getSkuDTOList().get(i);
            detailRequestBody.setExtendMap(v.getExtendMap());
            detailRequestBody.setPromotionGift(v.getSelfGoodsType());
            detailRequestBody.setOrderDetailRowNo(String.valueOf(i+1));
            detailRequestBody.setSkuPrice(BigDecimalUtils.convertFenByDecimalYuan(BigDecimalUtils.parseTsscDecimal(v.getSettleprice())));
            detailRequestBody.setSkuPriceSup(BigDecimalUtils.convertFenByDecimalYuanSup(BigDecimalUtils.parseTsscDecimal(v.getSettleprice())));
            detailRequestBody.setSkuCount(StringUtils.isNotBlank(v.getCount())?BigDecimalUtils.parseTsscDecimal(v.getCount()).intValue():0);
            detailRequestBody.setItemId(v.getItemId());
//            detailRequestBody.setSettlePrice(detailRequestBody.getSkuPrice()*detailRequestBody.getSkuCount());
            detailRequestBody.setSettlePrice(BigDecimalUtils.convertFenByYuan(v.getPayAmount()));
            detailRequestBody.setGoodsNo(v.getGoodsNo());
            detailRequestBody.setSkuName(v.getGoodsName());
            if(detailRequestBody.getSettlePrice()<=0){
                log.warn("createOrderDetail|goodsNo:{}.|商品总价或单价有误!", v.getGoodsNo());
                throw new AmisBadRequestException(ErrorCodeEnum.PORTAL_CREATE_ORDER_PRICE_EXCEPTION);
            }
            detailRequestBodyList.add(detailRequestBody);
        }
        return detailRequestBodyList;
    }

    private List<CreateOrderDetailRequestBody> createThirdOrderDetail(PortalLYOrderHeadDTO cartUnifyParam) {
        if(CollectionUtils.isEmpty(cartUnifyParam.getSkuDTOList())){
            throw new AmisBadRequestException("订单明细为空");
        }

        List<CreateOrderDetailRequestBody> detailRequestBodyList = new ArrayList<>(cartUnifyParam.getSkuDTOList().size());
        for(int i=0;i<cartUnifyParam.getSkuDTOList().size();i++){
            PortalLYSkuDTO v = cartUnifyParam.getSkuDTOList().get(i);
            CreateOrderDetailRequestBody detailRequestBody = new CreateOrderDetailRequestBody();
            detailRequestBody.setOrderDetailRowNo(String.valueOf(i+1));
            detailRequestBody.setSkuPrice(BigDecimalUtils.convertFenByDecimalYuan(BigDecimalUtils.parseTsscDecimal(v.getRealPrice())));
            detailRequestBody.setSkuCount(StringUtils.isNotBlank(v.getCount())?Integer.parseInt(v.getCount()):0);
            detailRequestBody.setSettlePrice(detailRequestBody.getSkuPrice()*detailRequestBody.getSkuCount());
            detailRequestBody.setGoodsNo(v.getGoodsNo());
            detailRequestBody.setSkuName(v.getGoodsName());
            log.info("createThirdOrderDetail|goodsNo:{}.|skuPrice:{}.skuCount:{}.settlePrice:{}.serialNumber:{}.",
                v.getGoodsNo(),
                detailRequestBody.getSkuPrice(),
                detailRequestBody.getSkuCount(),
                detailRequestBody.getSettlePrice(),
                detailRequestBody.getOrderDetailRowNo());
            detailRequestBodyList.add(detailRequestBody);
        }
        return detailRequestBodyList;

    }

    /**
     * 功    能：获取促销渠道
     * 作    者：王 伟
     * 时    间：2024/9/3
     */
    public String findPromotionChannel(Long orderId) {

        if(Objects.isNull(orderId)){
            return Constants.PARTICIPATE_VOLUME_BUSINESS;
        }

        String key = CommonUtil.createMiddleLineKey(ConstantPool.REDIS_PORTAL_ORDER_SOURCE_KEY, orderId+"");
        String promotionChannel = redissonCacheService.get(key);
        if(StringUtils.isNotBlank(promotionChannel)){
            return promotionChannel;
        }
        String orderPromotionChannel = getOrderPromotionChannel(orderId+"");
        redissonCacheService.setNxString(key, promotionChannel, orderPromotionChannelExpireTime);
        return orderPromotionChannel;
    }

    private String getOrderPromotionChannel(String orderId) {

        OrderDetailInfoDTO orderDetailInfoDTO = orderFeignExtService.queryOrderDetailMap(orderId);
        if(Objects.isNull(orderDetailInfoDTO) || CollectionUtils.isEmpty(orderDetailInfoDTO.getOrderDetailDTOList())){
            return Constants.PARTICIPATE_VOLUME_BUSINESS;
        }

        OrderBaseDTO orderBaseDTO = orderDetailInfoDTO.getOrderBase();

        //快批订单
        if(orderBaseDTO.getType()==68 && "2".equals(orderBaseDTO.getOrderSource())){
            return Constants.PARTICIPATE_VOLUME_BUSINESS;
        }

        return Constants.PARTICIPATE_B2B;
    }


    /**
     * 功    能：获取门店对应的平台用户id
     * 作    者：王 伟
     * 时    间：2024/8/16
     */
    private Long findStorePlatformUserId(Long channelStoreId){
        return portalStoreService.findStorePlatformUserId(channelStoreId);
    }

    @Override
    public PortalLYOrderHeadDTO headInfo(CartBaseParam cartUnifyParam, TokenUserDTO userInfobyToken) {
        long start = System.currentTimeMillis();
        log.info("headInfo|userInfobyToken:{}.", userInfobyToken);
        if(Objects.isNull(cartUnifyParam)){
            throw new AmisBadRequestException(ErrorCodeEnum.PARAM_ERROR_EXCEPTION);
        }

        List<MdmStoreBase> storeBaseList = storeExtService.findStoreInfoAndExtendByStoreIds(Lists.newArrayList(cartUnifyParam.getChannelStoreId()), Boolean.FALSE);
        log.info("headInfo|storeBaseList:{}.", storeBaseList);
        MdmStoreBase mdmStoreBase = storeBaseList.stream().filter(v->StringUtils.isNotBlank(v.getStoreNo())).findFirst().get();
        log.info("headInfo|门店查询耗时:{}.|mdmStoreBase:{}.", (System.currentTimeMillis()-start), mdmStoreBase);
        if(Objects.isNull(mdmStoreBase)){
            log.warn("门店编码为空");
            return null;
        }
        PortalLYOrderHeadDTO orderHeadDTO = new PortalLYOrderHeadDTO();
        orderHeadDTO.setExtendParamMap(new HashMap<>());
        boolean isComFreeze = portalStoreService.isComFreeze(mdmStoreBase.getStoreNo());
        orderHeadDTO.setStoreFreeze(isComFreeze);
        if(isComFreeze){
            return orderHeadDTO;
        }
        //多联系人信息
        contactInfo(mdmStoreBase, orderHeadDTO);
        String deliveryAddress = getDeliveryAddress(mdmStoreBase);
        orderHeadDTO.setDeliveryAddress(deliveryAddress);
        orderHeadDTO.setCity(mdmStoreBase.getCity());
        orderHeadDTO.setProvince(mdmStoreBase.getProvince());
        orderHeadDTO.setArea(mdmStoreBase.getArea());
        //默认不能使用账期
        orderHeadDTO.setAbleCredit(false);
        //购物车
        Long storePlatformUserId = findStorePlatformUserId(cartUnifyParam.getChannelStoreId());
        orderHeadDTO.setStorePlatformUserId(storePlatformUserId);

        if(Objects.equals(useSapCreditInfo,StatusEnums.YES.getCode())){
            buildSapCreditInfo(orderHeadDTO,cartUnifyParam,mdmStoreBase,userInfobyToken);
        }else {
            buildOaCreditInfo(orderHeadDTO,cartUnifyParam,mdmStoreBase,userInfobyToken);
        }

        //订单编辑的逻辑
        orderEditHead(cartUnifyParam, orderHeadDTO);

        log.info("head信息返回 [{}]", JSON.toJSONString(orderHeadDTO));
        log.info("headInfo|总耗时:{}.", (System.currentTimeMillis()-start));
        return orderHeadDTO;
    }

    /**
     * 功    能：拼装收货地址
     * 作    者：王 伟
     * 时    间：2024/10/30
     */
    private static String getDeliveryAddress(MdmStoreBase mdmStoreBase) {
        try {
            //如果地址包含省市区地址  那么直接使用地址
            String address = CommonUtil.getReturnStr(mdmStoreBase.getAddress());
            String province = CommonUtil.getReturnStr(mdmStoreBase.getProvince());
            String city = CommonUtil.getReturnStr(mdmStoreBase.getCity());
            String area = CommonUtil.getReturnStr(mdmStoreBase.getArea());
            if(address.startsWith(province)){
                address = address.substring(province.length());
            }
            if(address.startsWith(city)){
                address = address.substring(city.length());
            }
            if (address.startsWith(area)){
                address = address.substring(area.length());
            }
            return province + city + area + address;
        }catch (Exception e){
            log.warn("拼装收货地址错误",e);
        }

        return CommonUtil.getReturnStr(mdmStoreBase.getAddress());
    }

    /**
     * 功    能：查询oa的账期信息
     * 作    者：王 伟
     * 时    间：2024/9/5
     */
    private void buildOaCreditInfo(PortalLYOrderHeadDTO orderHeadDTO,CartBaseParam cartUnifyParam,MdmStoreBase mdmStoreBase, TokenUserDTO userInfoToken){
        String storeNo = mdmStoreBase.getStoreNo();
        long start = System.currentTimeMillis();
        OaCreditLitDTO oaCreditLitDTO = oaRestService.queryCacheCreditLimit(storeNo);
        log.info("queryCacheCreditLimit|耗时：{}.",System.currentTimeMillis()-start);
        if(Objects.nonNull(oaCreditLitDTO)){
            orderHeadDTO.setAbleCreditLimitAmount(oaCreditLitDTO.getSxye());
            orderHeadDTO.setDebtAmount(BigDecimalUtils.computeDebtAmount(oaCreditLitDTO.getSxye(), oaCreditLitDTO.getSxze()));
        }
        long start2 = System.currentTimeMillis();
        Long days = queryAbleDays(cartUnifyParam.getBusinessId(), cartUnifyParam.getChannelStoreId());
        log.info("headInfo|耗时：{}.|days:{}.", (System.currentTimeMillis()-start2), days);
        orderHeadDTO.setAbleCredit(true);
        if(Objects.nonNull(days)){
            if(days.compareTo(-ableCommitDays)<0){
                orderHeadDTO.setAbleCredit(false);
            }
            orderHeadDTO.setAbleCreditDay(days);
        }else{
            orderHeadDTO.setAbleCreditDay(null);
            orderHeadDTO.setAbleCredit(false);
        }

        orderHeadDTO.setOptions(SettlementMethodEnum.allList());
        if(!orderHeadDTO.getAbleCredit()){
            orderHeadDTO.setOptions(SettlementMethodEnum.cashList());
            if(Objects.isNull(orderHeadDTO.getAbleCreditDay())){
                orderHeadDTO.setCreditRemind(false);
            }else {
                orderHeadDTO.setCreditRemind(true);
            }
        }

    }

    /**
     * 功    能：查询oa的账期信息
     * 作    者：王 伟
     * 时    间：2024/9/5
     */
    private void buildSapCreditInfo(PortalLYOrderHeadDTO orderHeadDTO,CartBaseParam cartUnifyParam,MdmStoreBase mdmStoreBase, TokenUserDTO userInfoToken){

        //查询账期信息
        CreditInfoDTO creditInfoDTO = findSapCreditInfo(mdmStoreBase,userInfoToken);

        if(Objects.nonNull(creditInfoDTO)){
            orderHeadDTO.setAbleCreditLimitAmount(creditInfoDTO.getCreditAmount());
            String creditDays = creditInfoDTO.getCreditDays();
            if(StringUtils.isBlank(creditDays)){
                orderHeadDTO.setAbleCreditDay(null);
            }else {
                creditDays = creditDays.trim();
                if(creditDays.contains("-")){
                    creditDays = creditDays.replace("-","");
                    creditDays = "-"+creditDays;
                }
                orderHeadDTO.setAbleCreditDay(Long.parseLong(creditDays));
            }
            //是否可以使用账期
            orderHeadDTO.setAbleCredit(StringUtils.isNotBlank(creditInfoDTO.getChangeCash()) && Objects.equals(Constants.N,creditInfoDTO.getChangeCash()));
            orderHeadDTO.setDebtAmount(creditInfoDTO.getDebtAmount());
        }

        orderHeadDTO.setOptions(SettlementMethodEnum.allList());
        if(!orderHeadDTO.getAbleCredit()){
            orderHeadDTO.setOptions(SettlementMethodEnum.cashList());
            if(StringUtils.isNotBlank(creditInfoDTO.getChangeCash())){
                orderHeadDTO.setCreditRemind(true);
            }
        }
    }

    /**
     * 功    能：获取sap账期信息
     * 作    者：王 伟
     * 时    间：2024/9/4
     */
    private CreditInfoDTO findSapCreditInfo(MdmStoreBase mdmStoreBase, TokenUserDTO userInfoToken) {
        String storeId = mdmStoreBase.getStoreId()+"";
        String creditKey = CommonUtil.createMiddleLineKey(ConstantPool.REDIS_PORTAL_STORE_CREDIT_INFO_KEY,storeId);

        if(Objects.equals(creditInfoQueryCache,StatusEnums.YES.getCode())){
            String creditInfoStr = redissonCacheService.get(creditKey);
            if(StringUtils.isNotBlank(creditInfoStr)){
                return JSON.parseObject(creditInfoStr, CreditInfoDTO.class);
            }
            CreditInfoDTO creditInfoDTO = findCreditInfo(mdmStoreBase,userInfoToken);
            if(Objects.nonNull(creditInfoDTO) && StringUtils.isNotBlank(creditInfoDTO.getCreditAmount())
                && StringUtils.isNotBlank(creditInfoDTO.getCreditDays()) && StringUtils.isNotBlank(creditInfoDTO.getChangeCash())){
                String jsonStr = JSON.toJSONString(creditInfoDTO);
                redissonCacheService.setNxString(creditKey, jsonStr,creditInfoExpireTime);
            }
            return creditInfoDTO;
        }
        //直接查询
        return findCreditInfo(mdmStoreBase,userInfoToken);
    }

    /**
     * 功    能：账期信息
     * 作    者：王 伟
     * 时    间：2024/9/4
     */
    private CreditInfoDTO findCreditInfo(MdmStoreBase mdmStoreBase, TokenUserDTO userInfoToken) {
        try {
            long start = System.currentTimeMillis();
            SapUnifyRequest sapUnifyRequest = new SapUnifyRequest();
            sapUnifyRequest.setFunctionName(Constants.SAP_CREDIT_FUNCTION_NAME);
            Map<String, Map<String, Object>> paramStructureMap = new HashMap<>(1);
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("IV_WERKS", mdmStoreBase.getStoreNo());
            paramStructureMap.put("IS_HEAD", paramMap);
            sapUnifyRequest.setParamStructureMap(paramStructureMap);

            Map<String, List<Map<String, Object>>> paramTableMap = new HashMap<>();
            sapUnifyRequest.setParamTableMap(paramTableMap);

            SapUnifyReponse sapUnifyReponse = sapConnectService.rfcConnect(sapUnifyRequest, userInfoToken);
            Map<String, Map<String, Object>> responseStructureMap = sapUnifyReponse.getResponseStructureMap();
            log.info("查询sap账期信息 [{}]",JSON.toJSONString(responseStructureMap));
            if(MapUtils.isEmpty(responseStructureMap)){
                return null;
            }
            Map<String, Object> resultMap = responseStructureMap.get("OS_RETURN");
            if(MapUtils.isEmpty(resultMap)){
                return null;
            }
            //可用信用额度
            String creditAmount = Objects.nonNull(resultMap.get("ZKYED")) ? resultMap.get("ZKYED").toString() : "";
            //可用信用天数
            String creditDays = Objects.nonNull(resultMap.get("ZKYTS")) ? resultMap.get("ZKYTS").toString() : "";
            //账期是否转现结
            String changeCash = Objects.nonNull(resultMap.get("ZSFXJ")) ? resultMap.get("ZSFXJ").toString() : "";
            //币种 CNY
            String currency = Objects.nonNull(resultMap.get("WAERS")) ? resultMap.get("WAERS").toString() : "";
            //币种 CNY
            String debtAmount = Objects.nonNull(resultMap.get("ZQKJE")) ? resultMap.get("ZQKJE").toString() : "";
            log.info("查询sap账期信息耗时 [{}]",(System.currentTimeMillis() - start));
            //返回构建账期信息
            return new CreditInfoDTO(creditAmount,creditDays,changeCash,currency,debtAmount);

        }catch (Exception e){
            log.warn("查询sap账期信息错误",e);
        }
        return null;
    }

    /**
     * 功    能：多联系人信息
     * 作    者：王 伟
     * 时    间：2024/8/21
     */
    private void contactInfo(MdmStoreBase mdmStoreBase, PortalLYOrderHeadDTO orderHeadDTO) {
        List<StoreContact> contactList = mdmStoreBase.getContactList();
        //以多联系人为准，如果没有使用原来的联系人,如果有联系人是否存在原来的联系人信息不存在的话添加原来联系人
        if(CollectionUtils.isNotEmpty(contactList)){
            orderHeadDTO.setContactList(contactList);
            String contactName = StringUtils.isNotBlank(mdmStoreBase.getContact())? mdmStoreBase.getContact():"";
            String contactPhone = mdmStoreBase.getTel();
            boolean isExist = false;
            for(StoreContact storeContact : contactList){
                List<StoreConsignee> consigneeList = storeContact.getConsigneeList();
                for(StoreConsignee storeConsignee : consigneeList){
                    if(Objects.equals(storeContact.getContactName(), contactName)
                        && Objects.equals(storeContact.getContactPhone(), contactPhone)
                        && Objects.equals(storeConsignee.getConsigneeName(), contactName)
                        && Objects.equals(storeConsignee.getConsigneePhone(), contactPhone)){
                        isExist = true;
                        break;
                    }
                }
            }
            if(!isExist){
                contactList.add(defaultContactInfo(mdmStoreBase));
            }
            //默认的联系人和收货人
            StoreContact defaultStoreContact = contactList.get(0);
            orderHeadDTO.setSelectedStoreContact(defaultStoreContact);
            orderHeadDTO.setSelectedStoreConsigneeList(defaultStoreContact.getConsigneeList());
            if(CollectionUtils.isNotEmpty(defaultStoreContact.getConsigneeList())){
                orderHeadDTO.setSelectedStoreConsignee(defaultStoreContact.getConsigneeList().get(0));
            }
            return;
        }
        contactList = Lists.newArrayList();
        StoreContact storeContact = defaultContactInfo(mdmStoreBase);
        contactList.add(storeContact);
        orderHeadDTO.setContactList(contactList);

        //默认的联系人和收货人
        StoreContact defaultStoreContact = contactList.get(0);
        orderHeadDTO.setSelectedStoreContact(defaultStoreContact);
        orderHeadDTO.setSelectedStoreConsigneeList(defaultStoreContact.getConsigneeList());
        if(CollectionUtils.isNotEmpty(defaultStoreContact.getConsigneeList())){
            orderHeadDTO.setSelectedStoreConsignee(defaultStoreContact.getConsigneeList().get(0));
        }
    }

    /**
     * 功    能：默认的联系人和收货人信息
     * 作    者：王 伟
     * 时    间：2024/8/27
     */
    private StoreContact defaultContactInfo(MdmStoreBase mdmStoreBase) {
        String contactName = StringUtils.isNotBlank(mdmStoreBase.getContact())? mdmStoreBase.getContact():"";
        StoreContact storeContact = new StoreContact();
        storeContact.setContactName(contactName);
        storeContact.setContactPhone(mdmStoreBase.getTel());
        List<StoreConsignee> consigneeList = Lists.newArrayList();
        consigneeList.add(new StoreConsignee(contactName, mdmStoreBase.getTel()));
        storeContact.setConsigneeList(consigneeList);
        return storeContact;
    }

    /**
     * 功    能：订单编辑的逻辑
     * 作    者：王 伟
     * 时    间：2024/8/22
     */
    private void orderEditHead(CartBaseParam cartUnifyParam, PortalLYOrderHeadDTO orderHeadDTO) {
        if(Objects.isNull(cartUnifyParam.getOrderId())){
            return;
        }
        String orderId = cartUnifyParam.getOrderId().toString();
        OrderDetailInfoDTO orderDetailInfoDTO = orderFeignExtService.queryOrderDetailMap(orderId);
        if(Objects.isNull(orderDetailInfoDTO)){
            return;
        }
        OrderBaseDTO orderBase = orderDetailInfoDTO.getOrderBase();

        //编辑订单的支付方式
        editPayInfo(orderHeadDTO, orderBase, orderId);
        //编辑订单的联系人
        editContactInfo(orderHeadDTO, orderBase);


    }

    /**
     * 功    能：数据脱敏
     * 作    者：王代军
     * 时    间：2025-08-27 19:18:18
     */
    private void dataDesensitizer(CartBaseParam cartUnifyParam, PortalLYOrderHeadDTO orderHeadDTO) {
        if(!cartUnifyParam.isShow()){
            Map<String, String> extendParamMap = orderHeadDTO.getExtendParamMap();
            extendParamMap.put("da",orderHeadDTO.getDeliveryAddress());
            orderHeadDTO.setDeliveryAddress(CommonUtil.desensitizerAddress(orderHeadDTO.getDeliveryAddress()));
        }
    }

    /**
     * 功    能：编辑订单的联系人
     * 作    者：王 伟
     * 时    间：2024/8/26
     */
    private void editContactInfo(PortalLYOrderHeadDTO orderHeadDTO, OrderBaseDTO orderBase) {
        String extend = orderBase.getExtend();
        if(StringUtils.isBlank(extend)){
            return;
        }
        OrderBaseExtendDTO orderBaseExtendDTO = JSON.parseObject(extend, OrderBaseExtendDTO.class);
        if(Objects.isNull(orderBaseExtendDTO) || StringUtils.isBlank(orderBaseExtendDTO.getCashierName())
            || StringUtils.isBlank(orderBaseExtendDTO.getCashierNo())){
            return;
        }

        String cashierName = orderBaseExtendDTO.getCashierName();
        String cashierNo = orderBaseExtendDTO.getCashierNo();

        List<StoreContact> contactList = orderHeadDTO.getContactList();

        for(StoreContact storeContact : contactList){
            List<StoreConsignee> consigneeList = storeContact.getConsigneeList();
            for(StoreConsignee storeConsignee:consigneeList){
                if(Objects.equals(storeContact.getContactName(), cashierName)
                    && Objects.equals(storeContact.getContactPhone(), cashierNo)
                    && Objects.equals(storeConsignee.getConsigneeName(), orderBase.getConsignee())
                    && Objects.equals(storeConsignee.getConsigneePhone(), orderBase.getConsigneePhone())){
                    storeContact.setSelected(1);
                    storeConsignee.setSelected(1);
                    //编辑时选中的联系人和收货人
                    orderHeadDTO.setSelectedStoreContact(storeContact);
                    orderHeadDTO.setSelectedStoreConsigneeList(consigneeList);
                    orderHeadDTO.setSelectedStoreConsignee(storeConsignee);
                    break;
                }
            }
        }

    }

    /**
     * 功    能：编辑订单的支付方式
     * 作    者：王 伟
     * 时    间：2024/8/26
     */
    private void editPayInfo(PortalLYOrderHeadDTO orderHeadDTO, OrderBaseDTO orderBase, String orderId) {
        Integer payType = orderBase.getPayType();
        log.info("订单编辑 订单id[{}] 支付方式[{}]", orderId,payType);
        if(Objects.isNull(payType)){
            return;
        }
        orderHeadDTO.setCreditRemind(false);
        if(Objects.equals(SettlementMethodEnum.ACCOUNT_PERIOD.getCode(), payType) && !orderHeadDTO.getAbleCredit()){
            orderHeadDTO.setCreditRemind(true);
        }

        List<OptionDto> options = orderHeadDTO.getOptions();
        boolean isExist = options.stream().anyMatch(v->Objects.equals(payType.toString(), v.getValue()));
        if(!isExist){
            return;
        }

        List<OptionDto> newOptions = options.stream().filter(v->!Objects.equals(payType.toString(), v.getValue())).collect(Collectors.toList());
        OptionDto defaultOptionDto = SettlementMethodEnum.getOptionDto(payType);
        if(Objects.nonNull(defaultOptionDto)){
            newOptions.add(0,defaultOptionDto);
            orderHeadDTO.setOptions(newOptions);
        }
    }

    private Long queryAbleDays(Long businessId, Long channelStoreId) {
        try {
            String companyCode = CacheVar.portalCompanyCodeMap.get(businessId);
            log.info("queryAbleDays|companyCode:{}.", companyCode);
            String storeNo = null;
            List<MdmStoreSimpleBase> mdmStoreSimpleBaseList = CacheVar.operateStoreMap.get(businessId);
            if(CollectionUtils.isNotEmpty(mdmStoreSimpleBaseList)){
                MdmStoreSimpleBase mdmStoreSimpleBase = mdmStoreSimpleBaseList.stream().filter(v-> channelStoreId.equals(v.getStoreId())).findFirst().get();
                if(Objects.nonNull(mdmStoreSimpleBase)){
                    storeNo = mdmStoreSimpleBase.getStoreNo();
                }
            }
            if(StringUtils.isBlank(companyCode) || StringUtils.isBlank(storeNo)){
                log.info("queryAbleDays|公司门店信息为空");
                return null;
            }
            String payCode = supplierCompanyRelationExtendMapper.getBpayconditionByCompanyAmdSupplier(storeNo, companyCode);
            Long beliveDays = PayTermEnum.getDaysByCode(payCode);
            log.info("queryAbleDays:{}.payCode:{}.beliveDays:{}..", payCode, beliveDays);
            if(StringUtils.isBlank(payCode) || Objects.isNull(beliveDays)){
                log.info("queryAbleDays|没有信用账期");
                return null;
            }


            SupplierBasicInfo supplierBasicInfo = supplierBasicInfoService.getSupplierInfoByCode(storeNo);
            Long tempBeliveDays = computeTempDays(supplierBasicInfo);
            Long orderDays = orderSearchEngineExtService.queryEarlyOrderDays(channelStoreId);
            log.info("queryAbleDays|beliveDays:{}.tempBeliveDays:{}.orderDays:{}].", beliveDays, tempBeliveDays, orderDays);
            orderDays = Objects.isNull(orderDays)?0L:orderDays;
            Long resutDaye = beliveDays+tempBeliveDays-orderDays;
            log.info("queryAbleDays|resutDaye:{}.", resutDaye);
            return resutDaye;
        } catch (Exception e) {
            log.warn("queryAbleDays|warn.", e);
        }
        return null;
    }

    private Long computeTempDays(SupplierBasicInfo supplierBasicInfo) {
        if(Objects.isNull(supplierBasicInfo)){
            return 0L;
        }
        String creditStarttimeJt = supplierBasicInfo.getCreditStarttimeJt();
        String creditEndtimeJt = supplierBasicInfo.getCreditEndtimeJt();
        String temporarycreditdayJt = supplierBasicInfo.getTemporarycreditdayJt();
        if(StringUtils.isBlank(creditStarttimeJt) || StringUtils.isBlank(creditEndtimeJt) || StringUtils.isBlank(temporarycreditdayJt)){
            return 0L;
        }
        log.info("computeTempDays|creditStarttimeJt:{}.creditEndtimeJt:{}.temporarycreditdayJt:{}.", creditStarttimeJt, creditEndtimeJt, temporarycreditdayJt);
        try {
            Boolean ableBetween = DateUtils.between(creditStarttimeJt, creditEndtimeJt);
            log.info("computeTempDays|ableBetween:{}。", ableBetween);
            if(ableBetween){
                BigDecimal temporaryCreditday = new BigDecimal(temporarycreditdayJt.trim());
                return temporaryCreditday.longValue();
            }
        }catch (Exception e){
            log.warn("computeTempDays|格式化失败.", e);
        }
        return 0L;
    }

    @Override
    public String createThirdOrder(PortalLYOrderHeadDTO cartUnifyParam) {
        if(StringUtils.isBlank(cartUnifyParam.getChannelStoreNo()) || CollectionUtils.isEmpty(cartUnifyParam.getSkuDTOList()) || StringUtils.isBlank(cartUnifyParam.getThirdPartyOrderNo())){
            throw new AmisBadRequestException(ErrorCodeEnum.PARAM_ERROR_EXCEPTION);
        }
        try {
            String[] thirdStr = thirdOrderBusiness.split(Constants.COLON);
            Long businessId = Long.parseLong(thirdStr[0]);
            Long storeId = Long.parseLong(thirdStr[1]);
            String channelStoreNo = cartUnifyParam.getChannelStoreNo();
            List<MdmStoreBase> storeBaseList = storeExtService.findStoreInfoAndExtendByStoreNos(Lists.newArrayList(channelStoreNo));
            if(CollectionUtils.isEmpty(storeBaseList)){
                throw new AmisBadRequestException("三方订单销售/采购方未找到对应配置");
            }
            MdmStoreBase mdmStoreBase = storeBaseList.stream().findFirst().get();


            CreateOrderRequestBody orderRequestBody = new CreateOrderRequestBody();
            orderRequestBody.setShiftId(1L);
            orderRequestBody.setThirdPartyOrderNo(cartUnifyParam.getThirdPartyOrderNo());
            orderRequestBody.setBusinessId(businessId);
            orderRequestBody.setContacts(mdmStoreBase.getContact());
            orderRequestBody.setContactsPhone(mdmStoreBase.getTel());
            orderRequestBody.setConsignee(mdmStoreBase.getContact());
            orderRequestBody.setConsigneeAddress(mdmStoreBase.getAddress());
            orderRequestBody.setConsigneePhone(mdmStoreBase.getTel());
            orderRequestBody.setStoreId(storeId);
            orderRequestBody.setChannelStoreId(mdmStoreBase.getStoreId());
            orderRequestBody.setRemarks("三方订单");
//            orderRequestBody.setStoreId(storeIdList.stream().findFirst().get());
            orderRequestBody.setTotalAmount(BigDecimalUtils.convertFenByYuan(cartUnifyParam.getTotalAmount()));
            orderRequestBody.setPayType(6);//支付类型 待定
            orderRequestBody.setPayAmount(BigDecimalUtils.convertFenByYuan(cartUnifyParam.getTotalAmount()));
            orderRequestBody.setFromCart(Boolean.TRUE);
//            orderRequestBody.setType(68);
            orderRequestBody.setDeliveryType(4);
            orderRequestBody.setExpressType(4);
//        orderRequestBody.setTotalAmount();
            Long thirdUserId = -Long.parseLong(String.valueOf(storeId).concat(String.valueOf(businessId)));
            log.info("createThirdOrder|thirdUserId:{}.", thirdUserId);
            orderRequestBody.setUserId(thirdUserId);
            orderRequestBody.setPlatformUserId(thirdUserId);
            orderRequestBody.setOrderDetail(createThirdOrderDetail(cartUnifyParam));

            ResponseEntity<CreateOrderResponse> responseEntity = orderFeignService.createOrder(Constants.PARTICIPATE_BUSINESS_LONGYI, orderRequestBody);
            log.info("createThirdOrder|responseEntity:{}.", JSONObject.toJSONString(responseEntity));
            if (responseEntity == null || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))) {
                log.warn("createThirdOrder|调用创建订单给失败, 返回结果 -> {}", responseEntity);
                return StringUtils.EMPTY;
            }
            log.info("orderId:{}.", responseEntity.getBody());

            //成功之后删除缓存价格
//            String priceRedisKey = CommonUtil.createMiddleLineKey(ConstantPool.REDIS_PORTAL_B2B_CART_GOODS_KEY,String.valueOf(storeId),String.valueOf(cartUnifyParam.getChannelStoreId()));
//            redissonCacheService.del(priceRedisKey);

//            orderRequestBody.getOrderDetail().forEach(v->{
//                String retailCodeKey = CommonUtil.createMiddleLineKey(ConstantPool.REDIS_PORTAL_B2B_CART_GOODS_KEY,String.valueOf(cartUnifyParam.getStoreId()),String.valueOf(cartUnifyParam.getChannelStoreId()), String.valueOf(v.getItemId()));
//                log.info("createOrder|删除价格缓存|retailCodeKey：{}.", retailCodeKey);
//                redissonCacheService.del(retailCodeKey);
//            });
            CreateOrderResponse response = responseEntity.getBody();
            if(Objects.isNull(response) || StringUtils.isBlank(response.getOrderId())){
                log.warn("createThirdOrder|调用创建订单返回结果为空, 返回结果 -> {}", response);
                return StringUtils.EMPTY;
            }
            return response.getOrderId();
        } catch (Exception e) {
            log.warn("createThirdOrder|创建三方订单失败.", e);
            throw new AmisBadRequestException(e.getMessage());
        }
    }

    /**
     * 获取商品类型 正常品 0 促销品 1 ，锁价品 2
     * @param srmPermSpuDto
     * @return
     */
    @Override
    public int getGoodsMold(PortalLYSkuDTO srmPermSpuDto) {
        int goodsMold = 0;
        if (NumberUtils.isNumber(srmPermSpuDto.getMaxPrice())
            && NumberUtils.isNumber(srmPermSpuDto.getMinPrice())
            && NumberUtils.isNumber(srmPermSpuDto.getWholesalePrice())
            && srmPermSpuDto.getMaxPrice().equals(srmPermSpuDto.getMinPrice())
            && srmPermSpuDto.getMaxPrice().equals(srmPermSpuDto.getWholesalePrice())
        ){
            goodsMold = 2;
        }
        return goodsMold;
    }

    @Override
    public OrderBaseDTO refreshCartPriceByOrder(String orderId,Boolean deleteCart,TokenUserDTO userDTO){
        log.info("编辑订单 订单id[{}] 操作人[{}]",orderId,JSON.toJSONString(userDTO));
        OrderDetailInfoDTO orderDetailInfoDTO = orderFeignExtService.queryOrderDetailMap(orderId);
        if(Objects.isNull(orderDetailInfoDTO)){
            throw new AmisBadRequestException("订单不存在");
        }
        List<OrderDetailDTO> detailDTOList = orderDetailInfoDTO.getOrderDetailDTOList();
        OrderBaseDTO orderBase = orderDetailInfoDTO.getOrderBase();
        Long storeId = orderBase.getChannelStoreId();
        Long channelStoreId = orderBase.getStoreId();
        Long orderPlatformUserId = orderBase.getPlatformUserId();

        if(Objects.nonNull(deleteCart) && deleteCart){
            Long storePlatformUserId = findStorePlatformUserId(channelStoreId);
            //先清空快批购物车
            log.info("编辑订单，清空快批购物车。channelStoreId[{}] storePlatformUserId[{}] orderPlatformUserId[{}] orderId[{}]",channelStoreId,storePlatformUserId,orderPlatformUserId,orderId);
            cartFeignService.deleteUserCart(bizCode,  channelStoreId, storePlatformUserId,storePlatformUserId,storePlatformUserId,Long.valueOf(orderId),orderPlatformUserId);
            return orderBase;
        }

        Boolean ableCmall = Boolean.FALSE;
        if(orderBase.getType()==68 && "1".equals(orderBase.getOrderSource())){
            ableCmall = Boolean.TRUE;
        }
        orderBase.setAbleCmall(ableCmall);

        log.info("refreshCartPriceByOrder|orderId:{}.ableCmall:{}.", orderId, ableCmall);
        Map<String, List<OrderDetailDTO>> listMap = detailDTOList.stream().collect(Collectors.groupingBy(v-> CommonUtil.createMiddleLineKey(v.getGoodsNo(), String.valueOf(v.getPromotionGift()))));
        Map<String, OrderDetailDTO> orderDetailDTOMap = new HashMap<>(listMap.size());
        listMap.forEach((k,list)->{
            orderDetailDTOMap.put(CommonUtil.createMiddleLineKey(orderId, k),list.stream().findFirst().get());
        });

        Long storePlatformUserId = findStorePlatformUserId(channelStoreId);
        CartCountParam param = new CartCountParam();
        param.setBizCode(bizCode);
        param.setChannelStoreId(channelStoreId);
        param.setOrderId(Long.valueOf(orderId));
        log.info("获取购物车数量查询  storePlatformUserId [{}] 参数[{}]", storePlatformUserId,param);
        Long count = cartFeignService.cartCount(bizCode,channelStoreId,storePlatformUserId,param);
        log.info("获取购物车数量结果  [{}]", count);
        //如果购物车数量存在 那么不需要取单
        if(count <= 0){
            orderFeignService.takeoutCart(Constants.PARTICIPATE_VOLUME_BUSINESS, orderId);
        }

        return orderBase;
    }

    @Override
    public SapImportResponseDTO againOrderGoodsList(Long businessId, Long channelStoreId, String orderId) {
        SapImportResponseDTO responseDTO = new SapImportResponseDTO();

        if(StringUtils.isBlank(orderId)){
            log.info("againOrderGoodsList|只查采购门店信息");
            if(Objects.isNull(businessId) || Objects.isNull(channelStoreId)){
                return responseDTO;
            }
            List<MdmStoreSimpleBase> simpleBaseList = sapConnectService.getBusinessOperateStore(businessId, channelStoreId, null);
            if(CollectionUtils.isEmpty(simpleBaseList)){
                return responseDTO;
            }
            MdmStoreSimpleBase storeSimpleBase = simpleBaseList.get(0);
            responseDTO.setStoreNo(storeSimpleBase.getStoreNo());
            responseDTO.setStoreName(storeSimpleBase.getStoreName());
            return responseDTO;
        }

        OrderDetailInfoDTO orderDetailInfoDTO = orderFeignExtService.queryOrderDetailMap(orderId);
        if(Objects.isNull(orderDetailInfoDTO)){
            throw new AmisBadRequestException("订单信息为空");
        }
        log.info("againOrderGoodsList|orderDetailInfoDTO:{}.", JSON.toJSONString(orderDetailInfoDTO));
        List<OrderDetailDTO> detailDTOList = orderDetailInfoDTO.getOrderDetailDTOList();
        OrderBaseDTO orderBase = orderDetailInfoDTO.getOrderBase();
        businessId = orderBase.getBusinessId();
        Long storeId = orderBase.getChannelStoreId();
        channelStoreId = orderBase.getStoreId();

        boolean isComFreeze = portalStoreService.isComFreezeCache(businessId,channelStoreId);

        if(isComFreeze){
            throw new AmisBadRequestException("门店被冻结，无法操作");
        }

        List<PortalLYSkuDTO> targetGoodsDTOS = detailDTOList.stream().filter(v->v.getPromotionGift() == 0).map(v->{
            PortalLYSkuDTO skuDTO = new PortalLYSkuDTO();
            skuDTO.setGoodsNo(v.getGoodsNo());
            skuDTO.setCount(String.valueOf(v.getSkuCount()));
            String priceSup = BigDecimalUtils.convertYuanByDecimalFenSup(v.getSkuPriceSup()).toPlainString();
            skuDTO.setSettleprice(priceSup);
            String extend = v.getExtend();
            if(StringUtils.isNotBlank(extend)){
                OrderDetailExtendDTO extendDTO = JSON.parseObject(extend, OrderDetailExtendDTO.class);
                skuDTO.setEditPrice(extendDTO.getEditPrice());
            }
            return skuDTO;
        }).collect(Collectors.toList());
        log.info("againOrderGoodsList|targetGoodsDTOS:{}.", CollectionUtils.isEmpty(targetGoodsDTOS)?StringUtils.EMPTY:JSON.toJSONString(targetGoodsDTOS));
        List<PortalLYSkuDTO> portalLYSkuDTOList = sapLYCheckService.auditAndRerurnGoodsList(businessId, storeId, channelStoreId, targetGoodsDTOS);
        log.info("againOrderGoodsList|size:{}.|portalLYSkuDTOList:{}.", portalLYSkuDTOList.size(), JSON.toJSONString(portalLYSkuDTOList));
        responseDTO.setCartListDtoList(portalLYSkuDTOList);

        //用订单里的信息查询门店信息
        List<MdmStoreSimpleBase> simpleBaseList = sapConnectService.getBusinessOperateStore(businessId, channelStoreId, null);
        if(CollectionUtils.isNotEmpty(simpleBaseList)){
            MdmStoreSimpleBase storeSimpleBase = simpleBaseList.get(0);
            responseDTO.setStoreNo(storeSimpleBase.getStoreNo());
            responseDTO.setStoreName(storeSimpleBase.getStoreName());
        }
        responseDTO.setChannelStoreId(channelStoreId);
        return responseDTO;
    }

    /**
     * 功    能：订单操作
     * 作    者：王 伟
     * 时    间：2024/9/6
     */
    @Override
    public OrderOperateResponseDTO
    orderOperate(OrderOperateParam operateParam, TokenUserDTO userDTO) {

        String operateType = operateParam.getOperateType();
        OrderOperateTypeEnum orderOperateTypeEnum = OrderOperateTypeEnum.getByCode(operateType);
        if(Objects.isNull(orderOperateTypeEnum)){
            throw new AmisBadRequestException("订单操作类型不存在");
        }

        switch (orderOperateTypeEnum){
            case ORDER_EDIT:
                return portalOrderOperateService.orderEdit(operateParam,userDTO);
            case ORDER_CHANGE:
                return portalOrderOperateService.orderChange(operateParam,userDTO);
            case ORDER_AUDIT:
                return portalOrderOperateService.orderAudit(operateParam,userDTO);
            case ORDER_CANCEL:
                return portalOrderOperateService.orderCancel(operateParam,userDTO);
            case ORDER_AGAIN:
                return portalOrderOperateService.orderAgain(operateParam,userDTO);
            default:
                throw new AmisBadRequestException("订单操作类型不存在");
        }

    }

    /**
     * 加购
     * @param cartSaveParamList
     * @return
     */
    private CartUnifyDTO addCart(TokenUserDTO tokenUserDTO, Long storeId, Long orderId,List<CartSaveParam> cartSaveParamList) {
        if(CollectionUtils.isEmpty(cartSaveParamList)){
            throw new AmisBadRequestException(ErrorCodeEnum.PARAM_ERROR_EXCEPTION);
        }
        try {
            Long realPlatformUserId = tokenUserDTO.getPlatformUserId();
            log.info("加购商品-开始 平台用户id[{}] 用户[{}] 参数[{}]",realPlatformUserId,tokenUserDTO.getUserName(), JSON.toJSONString(cartSaveParamList));
            Long channelStoreId = cartSaveParamList.stream().findFirst().get().getChannelStoreId();
            if(Objects.isNull(channelStoreId)){
                throw new AmisBadRequestException("采购门店为空");
            }
            //调用购物车加购
            Long storePlatformUserId = findStorePlatformUserId(channelStoreId);

            if(Objects.nonNull(orderId)){
                for(CartSaveParam cartSaveParam : cartSaveParamList){
                    cartSaveParam.setOrderPlatformUserId(storePlatformUserId);
                    cartSaveParam.setOrderId(orderId);
                }
            }

            for(CartSaveParam cartSaveParam : cartSaveParamList){
                if(Objects.nonNull(cartSaveParam.getEditPrice())){
                    continue;
                }
                if(Objects.equals(cartSaveParam.getGoodsMold(),GoodsMoldEnum.LOCK_PRICE.getCode())){
                    cartSaveParam.setEditPrice(StatusEnums.YES.getCode());
                    cartSaveParam.setPrice(BigDecimalUtils.convertFenByYuan(cartSaveParam.getChannelPrice()).longValue());
                    continue;
                }
                if(!NumberUtils.isNumber(cartSaveParam.getWholesalePrice())
                    || !NumberUtils.isNumber(cartSaveParam.getChannelPrice())){
                    continue;
                }
                if(new BigDecimal(cartSaveParam.getWholesalePrice()).compareTo(new BigDecimal(cartSaveParam.getChannelPrice())) != 0){
                    cartSaveParam.setEditPrice(StatusEnums.YES.getCode());
                    cartSaveParam.setPrice(BigDecimalUtils.convertFenByYuan(cartSaveParam.getChannelPrice()).longValue());
                }
            }

//            String priceRedisKey = CommonUtil.createMiddleLineKey(ConstantPool.REDIS_PORTAL_B2B_CART_GOODS_KEY,String.valueOf(storeId),String.valueOf(channelStoreId));
//            Map<String, String> priceRMap = redissonCacheService.getRedisMap(priceRedisKey);
            //调用购物车加购
            log.info("加购商品-购物车调用 bizCode[{}] channelStoreId[{}] storePlatformUserId[{}] cartSaveParamList[{}]",bizCode,channelStoreId,storePlatformUserId,JSON.toJSONString(cartSaveParamList));
            cartFeignService.cardAdd(bizCode,channelStoreId,storePlatformUserId,storePlatformUserId,cartSaveParamList);
//            for (CartSaveParam cartSaveParam : cartSaveParamList) {
//                if(Objects.isNull(cartSaveParam.getItemId()) || StringUtils.isBlank(cartSaveParam.getChannelPrice()) || cartSaveParam.getAblePromo()){
                    //促销品不允许改价
//                    continue;
//                }
//                String retailCodeKey = CommonUtil.createMiddleLineKey(ConstantPool.REDIS_PORTAL_B2B_CART_GOODS_KEY,String.valueOf(storeId),String.valueOf(cartSaveParam.getChannelStoreId()), String.valueOf(cartSaveParam.getItemId()));
//                CartRedisParam redisParam = new CartRedisParam();
//                redisParam.setCount(String.valueOf(cartSaveParam.getCount()));
//                redisParam.setChannelPrice(cartSaveParam.getChannelPrice());
//                priceRMap.put(retailCodeKey, cartSaveParam.getChannelPrice());
//                redissonCacheService.setNxString(retailCodeKey, JSONObject.toJSONString(redisParam), expirMillSecond);
//            }
//            redissonCacheService.setRedisMap(priceRedisKey, priceRMap, expirMillSecond);
        } catch (Exception e) {
            log.warn("addCart|加购失败。",e);
            throw new AmisBadRequestException(new StringBuilder().append("加入购物车失败: ").append(e.getMessage()).toString());
        }
        return null;
    }

    /**
     * 批量删除
     * @param storeId
     * @param cartBatchDelParam
     * @return
     */
    private CartUnifyDTO deleteBatchCart(TokenUserDTO tokenUserDTO, Long storeId, CartBatchDelParam cartBatchDelParam) {
        if(Objects.isNull(cartBatchDelParam) || CollectionUtils.isEmpty(cartBatchDelParam.getItemIdList())){
            throw new AmisBadRequestException(ErrorCodeEnum.PARAM_ERROR_EXCEPTION);
        }
        try {

            String orderId = cartBatchDelParam.getOrderId()+"";
            //订单的平台用户id
            Long storePlatformUserId = findStorePlatformUserId(cartBatchDelParam.getChannelStoreId());
            Long orderPlatformUserId = storePlatformUserId;
            log.info("批量删除购物车-开始 用户id[{}] 门店id[{}] 门店用户id[{}] 参数[{}]",tokenUserDTO.getUserId(),storeId,storePlatformUserId, JSON.toJSONString(cartBatchDelParam));
            cartFeignService.deleteUserCart(bizCode,  cartBatchDelParam.getChannelStoreId(), storePlatformUserId,storePlatformUserId,storePlatformUserId,cartBatchDelParam.getOrderId(),orderPlatformUserId);

//            String priceRedisKey = CommonUtil.createMiddleLineKey(ConstantPool.REDIS_PORTAL_B2B_CART_GOODS_KEY,String.valueOf(storeId),String.valueOf(cartBatchDelParam.getChannelStoreId()));
//            redissonCacheService.del(priceRedisKey);

//            cartBatchDelParam.getItemIdList().forEach(v->{
//                String retailCodeKey = CommonUtil.createMiddleLineKey(ConstantPool.REDIS_PORTAL_B2B_CART_GOODS_KEY,String.valueOf(storeId),String.valueOf(cartBatchDelParam.getChannelStoreId()), String.valueOf(v));
//                redissonCacheService.del(retailCodeKey);
//            });
        } catch (Exception e) {
            log.warn("deleteBatchCart|批量删除购物车失败。",e);
            throw new AmisBadRequestException(new StringBuilder().append("删除购物车失败: ").append(e.getMessage()).toString());
        }
        return null;
    }

    /**
     * 删除购物车
     * @return
     */
    private CartUnifyDTO deleteCart(TokenUserDTO tokenUserDTO, Long storeId, CartUnifyParam cartUnifyParam) {
        if(Objects.isNull(cartUnifyParam) || Objects.isNull(cartUnifyParam.getCartGoodsDeleteParam())){
            throw new AmisBadRequestException(ErrorCodeEnum.PARAM_ERROR_EXCEPTION);
        }
        try {

            CartGoodsDeleteParam cartGoodsDeleteParam = cartUnifyParam.getCartGoodsDeleteParam();

            Integer selfGoodsType = cartGoodsDeleteParam.getSelfGoodsType();

            //赠品删除
            if(Objects.equals(selfGoodsType,StatusEnums.YES.getCode())){
                return giftDelete(tokenUserDTO,storeId,cartUnifyParam);
            }


            CartDelParam cartDelParam = new CartDelParam();
            BeanUtil.copyProperties(cartGoodsDeleteParam,cartDelParam);
            cartDelParam.setIds(cartGoodsDeleteParam.getIds());
            cartDelParam.setOrderId(cartGoodsDeleteParam.getOrderId());
            cartDelParam.setDelType(Objects.isNull(cartUnifyParam.getCartGoodsDeleteParam().getDelType())
                ? CartDelTypeEnum.ASSIGN.getType() : cartUnifyParam.getCartGoodsDeleteParam().getDelType());
            cartDelParam.setChannelStoreId(cartGoodsDeleteParam.getChannelStoreId());
            cartDelParam.setBusinessId(cartGoodsDeleteParam.getBusinessId());

            Long storePlatformUserId = findStorePlatformUserId(cartGoodsDeleteParam.getChannelStoreId());
            log.info("删除购物车-开始 用户id[{}] 门店id[{}] 门店用户[{}] 参数[{}]",tokenUserDTO.getUserId(),storeId, storePlatformUserId,JSON.toJSONString(cartDelParam));
            cartFeignService.cardDelete(bizCode, cartGoodsDeleteParam.getChannelStoreId(),storePlatformUserId, cartDelParam);

//            String priceRedisKey = CommonUtil.createMiddleLineKey(ConstantPool.REDIS_PORTAL_B2B_CART_GOODS_KEY,String.valueOf(storeId),String.valueOf(cartDelParam.getChannelStoreId()));
//            Map<String, String> priceRMap = redissonCacheService.getRedisMap(priceRedisKey);

//            String retailCodeKey = CommonUtil.createMiddleLineKey(ConstantPool.REDIS_PORTAL_B2B_CART_GOODS_KEY,String.valueOf(storeId),String.valueOf(cartDelParam.getChannelStoreId()), String.valueOf(cartDelParam.getItemId()));
//            redissonCacheService.del(retailCodeKey);
//            priceRMap.remove(retailCodeKey);
//            redissonCacheService.setRedisMap(priceRedisKey,priceRMap,expirMillSecond);
        } catch (Exception e) {
            log.warn("deleteCart|删除购物车失败。",e);
            throw new AmisBadRequestException(new StringBuilder().append("删除购物车失败: ").append(e.getMessage()).toString());
        }
        return null;
    }

    /**
     * 删除购物车
     * @return
     */
    private CartUnifyDTO deleteGiftCart(Long channelStoreId, CartDelParam cartDelParam) {
        try {

            Long storePlatformUserId = findStorePlatformUserId(channelStoreId);
            log.info("删除购物车多品赠品-开始 门店id[{}] 门店用户[{}] 参数[{}]",channelStoreId, storePlatformUserId,JSON.toJSONString(cartDelParam));
            cartFeignService.cardDelete(bizCode, channelStoreId,storePlatformUserId, cartDelParam);
        } catch (Exception e) {
            log.warn("deleteCart|删除购物车失败。",e);
            throw new AmisBadRequestException(new StringBuilder().append("删除购物车失败: ").append(e.getMessage()).toString());
        }
        return null;
    }



    /**
     * 功    能：赠品删除
     * 作    者：王 伟
     * 时    间：2024/8/30
     */
    private CartUnifyDTO giftDelete(TokenUserDTO tokenUserDTO, Long storeId, CartUnifyParam cartUnifyParam) {
        log.info("赠品删除 [{}]",JSON.toJSONString(cartUnifyParam));
        CartGoodsDeleteParam cartGoodsDeleteParam = cartUnifyParam.getCartGoodsDeleteParam();

        String giftInfo = cartGoodsDeleteParam.getGiftInfo();

        List<GiftInfoDTO> giftInfoList = Lists.newArrayList();
        if(StringUtils.isNotBlank(giftInfo)){
            giftInfoList = JSON.parseArray(giftInfo, GiftInfoDTO.class);
        }
        String ids = "";
        String skuId = "";
        if(CollectionUtils.isNotEmpty(giftInfoList)){
            ids = giftInfoList.get(0).getIds();
            skuId = giftInfoList.get(0).getSkuId();
        }

        //多品赠品的删除 需要直接删除
        if(Objects.equals(cartGoodsDeleteParam.getPromotionType(), PromotionTypeEnums.MULTI_GIFT.getType())){

            CartDelParam cartDelParam = new CartDelParam();
            BeanUtil.copyProperties(cartGoodsDeleteParam,cartDelParam);
            List<String> idsList = Lists.newArrayList(ids.split(","));
            List<Long> idsLongList = idsList.stream().map(Long::valueOf).collect(Collectors.toList());
            cartDelParam.setIds(idsLongList);
            cartDelParam.setOrderId(cartGoodsDeleteParam.getOrderId());
            cartDelParam.setDelType(CartDelTypeEnum.DEL_GIFT.getType());
            cartDelParam.setChannelStoreId(cartGoodsDeleteParam.getChannelStoreId());
            cartDelParam.setBusinessId(cartGoodsDeleteParam.getBusinessId());
            CartGiftDelParam cartGiftDelParam = new CartGiftDelParam();
            cartGiftDelParam.setPromotionId(cartGoodsDeleteParam.getPromotionId().toString());
            cartGiftDelParam.setRuleId(cartGoodsDeleteParam.getRuleId());
            cartGiftDelParam.setPromotionType(cartGoodsDeleteParam.getPromotionType());
            cartGiftDelParam.setPromotionWay(cartGoodsDeleteParam.getPromotionWay());
            cartGiftDelParam.setSkuId(Long.valueOf(skuId));
            cartDelParam.setCartGiftDelParam(cartGiftDelParam);
            deleteGiftCart(cartGoodsDeleteParam.getChannelStoreId(), cartDelParam);
            return null;
        }



        CartGiftChangeParam cartGiftChangeParam = new CartGiftChangeParam();
        cartGiftChangeParam.setBusinessId(cartGoodsDeleteParam.getBusinessId());
        cartGiftChangeParam.setPromotionId(cartGoodsDeleteParam.getPromotionId());
        cartGiftChangeParam.setPromotionType(cartGoodsDeleteParam.getPromotionType());
        cartGiftChangeParam.setPromotionWay(cartGoodsDeleteParam.getPromotionWay());
        cartGiftChangeParam.setGiftParentId(cartGoodsDeleteParam.getGiftParentId());
        cartGiftChangeParam.setIds(ids);
        cartGiftChangeParam.setChannelStoreId(cartGoodsDeleteParam.getChannelStoreId());
        cartGiftChangeParam.setCount("0");
        cartGiftChangeParam.setItemId(cartGoodsDeleteParam.getItemId());
        cartGiftChangeParam.setGiftId(cartGoodsDeleteParam.getGiftId());
        cartGiftChangeParam.setGiftType(cartGoodsDeleteParam.getGiftType());
        cartGiftChangeParam.setRuleId(cartGoodsDeleteParam.getRuleId());
        cartGiftChangeParam.setOrderId(cartGoodsDeleteParam.getOrderId());
        cartGiftChangeParam.setGiftChangeType(GiftChangeTypeEnum.DELETE_GIFT.getType());


        return changeGiftCart(tokenUserDTO,cartUnifyParam.getBusinessId(),storeId,cartGiftChangeParam,giftInfoList);
    }

    /**
     * 更新购物车
     * @param cartUpdateParam
     * @return
     */
    private CartUnifyDTO updateCart(TokenUserDTO tokenUserDTO, Long storeId, CartUpdateParam cartUpdateParam) {
        if(Objects.isNull(cartUpdateParam) || StringUtils.isBlank(cartUpdateParam.getChannelPrice()) || Objects.isNull(cartUpdateParam.getCount())){
            throw new AmisBadRequestException(ErrorCodeEnum.PARAM_ERROR_EXCEPTION);
        }
        long start = System.currentTimeMillis();
        try {
            log.info("更新购物车-开始 用户id[{}] 门店id[{}] 参数[{}]",tokenUserDTO.getUserId(),storeId, JSON.toJSONString(cartUpdateParam));
            Set<String> roles = tokenUserDTO.getRoles();
            boolean onlyUpdateCount = Objects.nonNull(cartUpdateParam.getOperateType());
            log.info("updateCart|roles:{}.|cartUpdateParam:{}.itemId:{}.channelStoreId:{}.onlyUpdateCount{}.", roles, cartUpdateParam, cartUpdateParam.getItemId(), cartUpdateParam.getChannelStoreId(),onlyUpdateCount);

            //不是更新价格 那么需要
            if(!onlyUpdateCount){
                String decimalChannelPrice = cartUpdateParam.getChannelPrice();
                cartUpdateParam.setPrice((long)BigDecimalUtils.convertFenByYuan(decimalChannelPrice));
            }

            if(Objects.isNull(cartUpdateParam.getOperateType())){
                cartUpdateParam.setOperateType(CartOperateTypeEnums.MODIFY.getType());
            }

            Long storePlatformUserId = findStorePlatformUserId(cartUpdateParam.getChannelStoreId());
            log.info("更新购物车-开始 用户id[{}] 门店id[{}] 参数[{}]",tokenUserDTO.getUserId(),storeId, JSON.toJSONString(cartUpdateParam));
            cartFeignService.cardUpate(bizCode, cartUpdateParam.getChannelStoreId(),storePlatformUserId, cartUpdateParam);
            log.info("updateCart|购物车更新接口耗时|{}.", (System.currentTimeMillis()-start));

            long start2 = System.currentTimeMillis();
            CartListParam cartListParam = new CartListParam();
            cartListParam.setBusinessIds(Lists.newArrayList(cartUpdateParam.getBusinessId()));
            cartListParam.setBusinessId(cartUpdateParam.getBusinessId());
            cartListParam.setChannelStoreId(cartUpdateParam.getChannelStoreId());
            cartListParam.setStoreId(storeId);
            cartListParam.setStoreIds(Lists.newArrayList(storeId));
            if(Objects.nonNull(cartUpdateParam.getOrderId())){
                cartListParam.setOrderId(cartUpdateParam.getOrderId());
            }
            Boolean ableSupperRole = StringUtils.isBlank(supperRole) ? Boolean.FALSE : permissionExtService.getUserRolesCacheMap(tokenUserDTO, supperRole);
            cartListParam.setSupperRole(ableSupperRole);
            CartUnifyDTO cartUnifyDTO = listFeignCart(tokenUserDTO,cartListParam);
            if(CollectionUtils.isEmpty(cartUnifyDTO.getCartListDtoList())){
                throw new AmisBadRequestException("购物车为空");
            }
            cartUnifyDTO.setSupperRole(ableSupperRole);
            log.info("updateCart|权限管控耗时|{}.", (System.currentTimeMillis()-start2));

            //检验赠品的库存
            checkGiftStock(cartUnifyDTO);

            //处理价格
            recoverCartList(cartUnifyDTO, cartListParam.getBusinessId(),cartListParam.getChannelStoreId(), storeId);

            //订单编辑处理
//            goodsStockHandle(cartUnifyDTO,cartListParam);


            log.info("updateCart|总耗时|{}.", (System.currentTimeMillis()-start));
            return cartUnifyDTO;
        } catch (Exception e) {
            log.warn("updateCart|更新购物车失败。",e);
            throw new AmisBadRequestException(new StringBuilder().append("更新购物车失败: ").append(e.getMessage()).toString());
        }
    }

    /**
     * 查询购物车
     * @param cartListParam
     * @return
     */
    private CartUnifyDTO listCart(TokenUserDTO tokenUserDTO, Long storeId, CartListParam cartListParam) {
        if(Objects.isNull(cartListParam)){
            throw new AmisBadRequestException(ErrorCodeEnum.REQUEST_ERROR);
        }
        try {
            log.info("查询购物车-开始 用户id[{}] 门店id[{}] 参数[{}]",tokenUserDTO.getUserId(),storeId, JSON.toJSONString(cartListParam));
            Long userId = tokenUserDTO.getUserId();
            long start = System.currentTimeMillis();
            Boolean ableSupperRole = StringUtils.isBlank(supperRole) ? Boolean.FALSE : permissionExtService.getUserRolesCacheMap(tokenUserDTO, supperRole);
            log.info("listCart|userId:{}.supperRole:{}.ableSupperRole:{}|权限查询耗时:{}.}.", userId,supperRole,ableSupperRole, (System.currentTimeMillis()-start));
            cartListParam.setBusinessId(cartListParam.getBusinessId());
            cartListParam.setBusinessIds(Lists.newArrayList(cartListParam.getBusinessId()));
            cartListParam.setStoreId(storeId);
            cartListParam.setStoreIds(Lists.newArrayList(storeId));
            cartListParam.setSupperRole(ableSupperRole);
            CartUnifyDTO cartUnifyDTO = listFeignCart(tokenUserDTO,cartListParam);
            //重新组装价格和金额
            cartUnifyDTO.setSupperRole(ableSupperRole);

            //订单编辑处理
            goodsStockHandle(cartUnifyDTO,cartListParam);

            //检验赠品的库存
            checkGiftStock(cartUnifyDTO);

            recoverCartList(cartUnifyDTO, cartListParam.getBusinessId(),cartListParam.getChannelStoreId(), storeId);

            //标签处理
            goodsLabelHandle(cartUnifyDTO);

            log.info("购物车列表返回:{}.",JSON.toJSONString(cartUnifyDTO));
            log.info("listCart|总耗时|{}.", System.currentTimeMillis()-start);
            return cartUnifyDTO;
        } catch (Exception e) {
            log.warn("listCart|购物车列表失败。",e);
            throw new AmisBadRequestException(ErrorCodeEnum.REQUEST_ERROR, "购物车列表失败: "+e.getMessage());
        }
    }

    /**
     * 功    能：标签处理
     * 作    者：王 伟
     * 时    间：2024/9/11
     */
    private void goodsLabelHandle(CartUnifyDTO cartUnifyDTO) {
        List<PortalLYSkuDTO>  list = cartUnifyDTO.getCartListDtoList();
        if(CollectionUtils.isEmpty(list)){
            return;
        }
        for (PortalLYSkuDTO portalLYSkuDTO : list){
            List<Integer> labelList = Lists.newArrayList();
            if(Objects.equals(portalLYSkuDTO.getSelfGoodsType(), CartGoodsTypeEnum.GIFT.getType())){
                portalLYSkuDTO.setGoodsLabelList(Lists.newArrayList(GoodsLabelEnum.GIFT_GOODS.getCode()));
                continue;
            }
            if(Objects.equals(portalLYSkuDTO.getGoodsMold(), GoodsMoldEnum.LOCK_PRICE.getCode())){
                portalLYSkuDTO.setGoodsLabelList(Lists.newArrayList(GoodsLabelEnum.LOCK_GOODS.getCode()));
                continue;
            }
            portalLYSkuDTO.setGoodsLabelList(Lists.newArrayList(GoodsLabelEnum.NORMAL_GOODS.getCode()));
        }

    }

    /**
     * 功    能：订单编辑处理
     * 作    者：王 伟
     * 时    间：2024/8/22
     */
    private void goodsStockHandle(CartUnifyDTO cartUnifyDTO, CartListParam cartListParam) {
        try {
            Long orderId = cartListParam.getOrderId();
            if(Objects.isNull(orderId)){
                return;
            }
            OrderDetailInfoDTO orderDetailInfoDTO = orderFeignExtService.queryOrderDetailMap(orderId+"");
            if(Objects.isNull(orderDetailInfoDTO) || CollectionUtils.isEmpty(orderDetailInfoDTO.getOrderDetailDTOList())){
                return;
            }

            OrderBaseDTO orderBaseDTO = orderDetailInfoDTO.getOrderBase();

//            List<Integer> statusList = Lists.newArrayList(OrderStatusCurEnum.CREATE_ING.getStatusValue(),OrderStatusCurEnum.CREATE_FAIL.getStatusValue());
//            if(statusList.contains(orderBaseDTO.getOrderStatusCur())){
//                log.info("非扣减成功状态[{}]",orderDetailInfoDTO.getOrderDetailDTOList().size());
//                return;
//            }
            //订单明细
            Map<String,Integer> orderStockMap = Maps.newHashMap();
            List<OrderDetailDTO>  detailList = orderDetailInfoDTO.getOrderDetailDTOList();
            for(OrderDetailDTO detailDTO : detailList){
                String extend = detailDTO.getExtend();
                if(StringUtils.isBlank(extend)){
                    continue;
                }
                JSONObject jsonObject = JSON.parseObject(extend);
                String stockLockSuccessCount = jsonObject.getString("stockLockSuccessCount");
                if(StringUtils.isBlank(stockLockSuccessCount) || !NumberUtils.isNumber(stockLockSuccessCount)){
                    continue;
                }

                orderStockMap.put(detailDTO.getGoodsNo(),new BigDecimal(stockLockSuccessCount).intValue());
            }
            log.info("编辑订单 商品个数[{}]",orderStockMap);

            List<PortalLYSkuDTO> cartListDtoList = cartUnifyDTO.getCartListDtoList();

            for(PortalLYSkuDTO portalLYSkuDTO : cartListDtoList){
//                if(Objects.equals(portalLYSkuDTO.getSelfGoodsType(),StatusEnums.YES.getCode())){
//                    continue;
//                }
                log.info("商品库存处理:{}", JSON.toJSONString(portalLYSkuDTO));
                Integer countStr = orderStockMap.get(portalLYSkuDTO.getGoodsNo());
                if(Objects.isNull(countStr)){
                    countStr = 0;
                }
                String availableCountStr = portalLYSkuDTO.getAvailableCount();
                log.info("商品库存处理 商品[{}] 订单个数 {} 库存个数{}", portalLYSkuDTO.getGoodsNo(),countStr,availableCountStr);

                int availableStock = new BigDecimal(availableCountStr).intValue();
                int count = new BigDecimal(countStr).intValue();
                int availableCount = availableStock + count;
                portalLYSkuDTO.setAvailableCount(availableCount+"");
            }
            cartUnifyDTO.setCartListDtoList(cartListDtoList);
        }catch (Exception e){
            log.warn("设置编辑订单的可用数量错误。",e);
        }
    }


    /**
     * 购物车列表
     * @param cartListParam
     * @return
     */
    private CartUnifyDTO listFeignCart(TokenUserDTO tokenUserDTO,CartListParam cartListParam){
        long start = System.currentTimeMillis();

        String orderId = cartListParam.getOrderId()+"";
        //订单的平台用户id
        Long storePlatformUserId = findStorePlatformUserId(cartListParam.getChannelStoreId());
        String promotionChannel = findPromotionChannel(cartListParam.getOrderId());
        cartListParam.setOrderPlatformUserId(storePlatformUserId);
        cartListParam.setPromotionChannel(promotionChannel);
        //获取取消渠道
        cartListParam.setOrderPlatformUserId(storePlatformUserId);

        log.info("listFeignCart|bizCode:{}.storePlatformUserId:{}.|cartListParam:{}。|itemId:{}.channelStoreId：{}.", bizCode,storePlatformUserId, JSON.toJSONString(cartListParam), cartListParam.getItemId(), cartListParam.getChannelStoreId());
        ResponseEntity<CartListDto> responseEntity = cartFeignService.cardList(bizCode, cartListParam.getChannelStoreId(),storePlatformUserId, storePlatformUserId, cartListParam);
        log.info("listFeignCart|查询购物车列表耗时|{}.", System.currentTimeMillis()-start);
        if (Objects.isNull(responseEntity) || Objects.isNull(responseEntity.getBody()) || responseEntity.getStatusCode() != HttpStatus.OK) {
            log.warn("listCart|获取购物车列表失败");
            return null;
        }
        CartListDto cartListDto = responseEntity.getBody();
        log.info("购物车列表-购物车返回列表信息:{}.", JSON.toJSONString(cartListDto));
        long start1 = System.currentTimeMillis();
        CartUnifyDTO cartUnifyDTO = new CartUnifyDTO();
        cartUnifyDTO.setTotalAmount(cartListDto.getTotalAmount());
        cartUnifyDTO.setTotalFav(cartListDto.getTotalFav());
        cartUnifyDTO.setOrderId(cartListDto.getOrderId());
        cartUnifyDTO.setValidStockStatus(cartListDto.getValidStockStatus());
        List<StorePromotionGroupDto> storePromotionGroupList = cartListDto.getStorePromotionGroupList();
//        log.info("listCart|storePromotionGroupList:{}.", storePromotionGroupList);
        if(CollectionUtils.isNotEmpty(storePromotionGroupList)){
            StorePromotionGroupDto promotionGroupDto = storePromotionGroupList.stream().findFirst().get();
            List<PromotionGroupDto> promotionGroupList = promotionGroupDto.getPromotionGroupList();
//            log.info("listCart|promotionGroupList:{}.", promotionGroupList);
            if(CollectionUtils.isNotEmpty(promotionGroupList)){
                List<PortalLYSkuDTO> cartSkuList = new ArrayList<>();

                //对商品进行排序
                sortPromotionGroup(promotionGroupList);
                //多品的促销信息
                List<GoodsPromotionDTO> promotionDTOList = Lists.newArrayList();
                promotionGroupList.forEach(groupDto->{
                    cartUnifyDTO.setPromotionTitle(groupDto.getPromotionTitle());
                    cartUnifyDTO.setPromotionId(groupDto.getPromotionId());
                    cartUnifyDTO.setPromotionType(groupDto.getPromotionType());
                    cartUnifyDTO.setPromotionWay(groupDto.getPromotionWay());
                    List<PortalLYSkuDTO> cartListDtoList = copySkuList(cartListDto.getOrderId(),groupDto, cartListParam,promotionDTOList);
                    if(CollectionUtils.isNotEmpty(cartListDtoList)){
                        cartSkuList.addAll(cartListDtoList);
                    }
                });
                //添加促销信息
                cartUnifyDTO.setPromotionInfo(promotionDTOList);
                //补充商品信息
                supplementSkuList(cartSkuList, cartListParam);
                log.info("listCart|cartSkuList:{}", JSON.toJSONString(cartSkuList));
                cartUnifyDTO.setCartListDtoList(cartSkuList);
            }
        }
        log.info("listFeignCart|分析补全数据耗时|{}.", System.currentTimeMillis()-start1);
        return cartUnifyDTO;
    }

    /**
     * 功    能：促销组排序
     * 作    者：王 伟
     * 时    间：2024/9/27
     */
    private void sortPromotionGroup(List<PromotionGroupDto> promotionGroupList) {
        if(CollectionUtils.isEmpty(promotionGroupList)){
            return;
        }
        try {
            // 对每个 PromotionGroupDto 内部的 products 进行排序
            for (PromotionGroupDto group : promotionGroupList) {
                List<SkuDto> skuList = group.getSkuList();
                if(CollectionUtils.isNotEmpty(skuList)){
                    skuList.sort(Comparator.comparing(SkuDto::getGoodsType)
                        .thenComparing(SkuDto::getSortDate, Comparator.nullsLast(Comparator.reverseOrder())));
                }
            }

            // 对外部的 PromotionGroupDto 列表进行排序，根据内部 products 的最大 sortDate
            promotionGroupList.sort(Comparator.comparing(group -> {
                List<SkuDto> skuList = group.getSkuList();
                if (CollectionUtils.isNotEmpty(skuList)) {
                    if(Objects.isNull(skuList.get(0).getSortDate())){
                        return null;
                    }
                    return skuList.get(0).getSortDate();
                }
                return null;
            }, Comparator.nullsLast(Comparator.reverseOrder())));
        }catch (Exception e){
            log.warn("促销组排序错误",e);
        }
    }

    /**
     * 并行
     * @param goodsNoList
     * @param cartListParam
     */
    private CartAsyncApiDTO supplementAsyncSkuList(List<String> goodsNoList, CartListParam cartListParam){
        long start = System.currentTimeMillis();
        CartAsyncApiDTO cartAsyncApiDTO = new CartAsyncApiDTO();
        if(asyncSwitch == 0){
            log.info("supplementAsyncSkuList|异步批量处理");
            //查询上次销售情况
            Map<String, OrderSkuSaleDTO> skuSaleDTOMap = lastOrder(cartListParam.getBusinessId(),cartListParam.getChannelStoreId(), goodsNoList);

            cartAsyncApiDTO.setSkuSaleDTOMap(skuSaleDTOMap);

            //查询目录价
            CompletableFuture<Map<String, ItemSkuQueryApiDTO>> mljAndLimitInfo =
                CompletableFuture.supplyAsync(() -> itemCenterCpExtService.querySkuMLJPriceMap(cartListParam.getBusinessId(), cartListParam.getStoreId(), cartListParam.getChannelStoreId(), goodsNoList));
            mljAndLimitInfo.thenAccept((result) -> {
                log.info("supplementAsyncSkuList|查询目录价和限价情况:{}.", result);
                cartAsyncApiDTO.setMljPriceMap(result);
            }).exceptionally((e) -> {
                log.warn("supplementAsyncSkuList|查询目录价和限价情况异常|e.", e);
                return null;
            });

            //查询成本价
            CompletableFuture<Map<String, String>> costPriceInfo =
                CompletableFuture.supplyAsync(() -> priceCenterFeignExtService.queryCostPrice(cartListParam.getBusinessId(), cartListParam.getStoreId(), goodsNoList));
            costPriceInfo.thenAccept((result) -> {
                log.info("supplementAsyncSkuList|查询成本价情况:{}.", result);
                cartAsyncApiDTO.setCbjPriceMap(result);
            }).exceptionally((e) -> {
                log.warn("supplementAsyncSkuList|查询成本价情况异常|e.", e);
                return null;
            });

            //查询商品扩展属性
            CompletableFuture<Map<String, SpuListSimpleVo>> skuExtInfo =
                CompletableFuture.supplyAsync(() -> itemSearchEngineExtService.batcBusinessSpuMap(cartListParam.getBusinessId(), goodsNoList));
            skuExtInfo.thenAccept((result) -> {
                log.info("supplementAsyncSkuList|查询商品扩展属性情况:{}.", result);
                cartAsyncApiDTO.setSimpleVoMap(result);
            }).exceptionally((e) -> {
                log.warn("supplementAsyncSkuList|查询商品扩展属性情况异常|e.", e);
                return null;
            });

            //查询库存批次信息
            CompletableFuture<Map<String, PortalGoodsPriceStockBatch>> stockBatchInfo =
                CompletableFuture.supplyAsync(() -> sapStockService.stockBatchMap(cartListParam.getBusinessId(), cartListParam.getStoreId(), goodsNoList));
            stockBatchInfo.thenAccept((result) -> {
                log.info("supplementAsyncSkuList|查询商品批次库存属性情况:{}.", result);
                cartAsyncApiDTO.setStockBatchMap(result);
            }).exceptionally((e) -> {
                log.warn("supplementAsyncSkuList|查询商品批次库存属性情况|e.", e);
                return null;
            });


            //合并
            CompletableFuture<Void> allQuery = CompletableFuture.allOf(mljAndLimitInfo, costPriceInfo, skuExtInfo,stockBatchInfo);
            CompletableFuture<CartAsyncApiDTO> future = allQuery.thenApply((result) -> {
                log.warn("supplementAsyncSkuList|全部完成|cartAsyncApiDTO:{}.", cartAsyncApiDTO);
                return cartAsyncApiDTO;
            }).exceptionally((e) -> {
                log.warn("supplementAsyncSkuList|全部完成异常|e.", e);
                return null;
            });
            //获取异步返回值
            future.join();

        }else{
            Map<String, OrderSkuSaleDTO> skuSaleDTOMap = lastOrder(cartListParam.getBusinessId(),cartListParam.getChannelStoreId(), goodsNoList);
            Map<String, ItemSkuQueryApiDTO> mljPriceMap = itemCenterCpExtService.querySkuMLJPriceMap(cartListParam.getBusinessId(), cartListParam.getStoreId(), cartListParam.getChannelStoreId(), goodsNoList);
            Map<String, String> cbjPriceMap = priceCenterFeignExtService.queryCostPrice(cartListParam.getBusinessId(), cartListParam.getStoreId(), goodsNoList);//超级管理员才能查成本价
            Map<String, SpuListSimpleVo> simpleVoMap = itemSearchEngineExtService.batcBusinessSpuMap(cartListParam.getBusinessId(), goodsNoList);
            Map<String, PortalGoodsPriceStockBatch> stockBatchMap = sapStockService.stockBatchMap(cartListParam.getBusinessId(), cartListParam.getStoreId(), goodsNoList);
            cartAsyncApiDTO.setSkuSaleDTOMap(skuSaleDTOMap);
            cartAsyncApiDTO.setMljPriceMap(mljPriceMap);
            cartAsyncApiDTO.setCbjPriceMap(cbjPriceMap);
            cartAsyncApiDTO.setSimpleVoMap(simpleVoMap);
            cartAsyncApiDTO.setStockBatchMap(stockBatchMap);
        }
        log.info("supplementAsyncSkuList|耗时:{}.", System.currentTimeMillis()-start);
        return cartAsyncApiDTO;
    }

    /**
     * 功    能：获取最后销售数据
     * 作    者：王 伟
     * 时    间：2024/12/30
     */
    private Map<String, OrderSkuSaleDTO> lastOrder(Long businessId,Long storeId,List<String> goodsNoList){
        if(Objects.equals(lastOrderSwitch,1)){
            return orderDetailLastSaleService.querySkuLastOrderMap(businessId,storeId, goodsNoList);
        }
        return orderSearchEngineExtService.querySkuLastOrderMap(storeId, goodsNoList);
    }

    /**
     * 补全数据
     * @param cartSkuList
     * @param cartListParam
     */
    private void supplementSkuList(List<PortalLYSkuDTO> cartSkuList, CartListParam cartListParam){
        long start = System.currentTimeMillis();
        List<String> goodsNoList = cartSkuList.stream().filter(s->StringUtils.isNotBlank(s.getGoodsNo())).map(s->s.getGoodsNo()).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(goodsNoList)){
            return;
        }
        CartAsyncApiDTO cartAsyncApiDTO = supplementAsyncSkuList(goodsNoList, cartListParam);
        Map<String, OrderSkuSaleDTO> skuSaleDTOMap = cartAsyncApiDTO.getSkuSaleDTOMap();
        Map<String, ItemSkuQueryApiDTO> mljPriceMap = cartAsyncApiDTO.getMljPriceMap();
        Map<String, String> cbjPriceMap = cartAsyncApiDTO.getCbjPriceMap();//超级管理员才能查成本价
        Map<String, SpuListSimpleVo> simpleVoMap = cartAsyncApiDTO.getSimpleVoMap();
        Map<String, PortalGoodsPriceStockBatch> stockBatchMap = cartAsyncApiDTO.getStockBatchMap();

        Map<String, GoodsErrInfo> goodsErrInfoMap = StringUtils.isNotBlank(cartListParam.getOrderNo()) ? orderFeignExtService.queryOrderGoodsErrorMap(cartListParam.getOrderNo()) : null;
        log.info("supplementSkuList|补全数据接口耗时:{}.", System.currentTimeMillis()-start);
        long start1 = System.currentTimeMillis();
        //补数据
        cartSkuList.forEach(lySkuDTO->{
            lySkuDTO.setSupperRole(cartListParam.getSupperRole());
            if(MapUtils.isNotEmpty(skuSaleDTOMap) && Objects.nonNull(skuSaleDTOMap.get(lySkuDTO.getGoodsNo()))){
                OrderSkuSaleDTO stockDTO = skuSaleDTOMap.get(lySkuDTO.getGoodsNo());
                lySkuDTO.setLastSaleCount(stockDTO.getLastSaleCount());
                lySkuDTO.setLastSaleDate(stockDTO.getLastSaleDate());
                lySkuDTO.setLastSalePrice(stockDTO.getLastSalePrice());
            }

            SpuListSimpleVo simpleVo = simpleVoMap.get(lySkuDTO.getGoodsNo());
            if(Objects.nonNull(simpleVo)){
                lySkuDTO.setMpacking(simpleVo.getMpacking());
                lySkuDTO.setJhiSpecification(simpleVo.getJhiSpecification());
                lySkuDTO.setIncometaxrate(simpleVo.getIncometaxrate());
                lySkuDTO.setInnerqty(simpleVo.getInnerqty());
                lySkuDTO.setHabitat(simpleVo.getProdarea());
                lySkuDTO.setManufacturer(simpleVo.getFactoryid());
                lySkuDTO.setGoodsline(simpleVo.getGoodsline());
                lySkuDTO.setPrepspec(simpleVo.getPrepspec());
                lySkuDTO.setUnit(simpleVo.getGoodsunit());
                lySkuDTO.setAdvicePrice(StringUtils.isNotBlank(simpleVo.getComretailprice())?simpleVo.getComretailprice():simpleVo.getRefretailprice());
                lySkuDTO.setClassone(simpleVo.getClassone());
                lySkuDTO.setRxtype(simpleVo.getRxtype());
                lySkuDTO.setWholesalepushlevel(simpleVo.getWholesalepushlevel());
                lySkuDTO.setCaseqty(simpleVo.getCaseqty());
                lySkuDTO.setApprdocno(simpleVo.getApprdocno());
                if(StringUtils.isNotBlank(lySkuDTO.getAdvicePrice())){
                    lySkuDTO.setAdvicePrice(new BigDecimal(lySkuDTO.getAdvicePrice()).setScale(Constants.YUAN_SCALE, BigDecimal.ROUND_HALF_UP).toPlainString());
                }
                lySkuDTO.setIncometaxratePersend(SrmTaxCodeEnum.getPersentByCode(lySkuDTO.getIncometaxrate()));
            }

            ItemSkuQueryApiDTO mljPrice = mljPriceMap.get(CommonUtil.createMiddleLineKey(lySkuDTO.getGoodsNo(), String.valueOf(cartListParam.getStoreId()), String.valueOf(cartListParam.getChannelStoreId())));
            if(Objects.nonNull(mljPrice)){
                lySkuDTO.setWholesalePrice(BigDecimalUtils.fenToYuan(mljPrice.getPriceStr(), Constants.YUAN_SCALE).toPlainString());
                lySkuDTO.setMinPrice(BigDecimalUtils.fenToYuan(mljPrice.getLowerLimit(), Constants.YUAN_SCALE).toPlainString());
                lySkuDTO.setMaxPrice(BigDecimalUtils.fenToYuan(mljPrice.getUpperLimit(), Constants.YUAN_SCALE).toPlainString());
            }
            String cbjPrice = cbjPriceMap.get(lySkuDTO.getGoodsNo());
            if(StringUtils.isNotBlank(cbjPrice)){
                lySkuDTO.setCostPrice(BigDecimalUtils.fenToYuan(cbjPrice, Constants.YUAN_SCALE).toPlainString());
            }

            if(MapUtils.isNotEmpty(stockBatchMap) && Objects.nonNull(stockBatchMap.get(lySkuDTO.getGoodsNo()))){
                lySkuDTO.setRecentlyDate(DateUtils.conventDateStrByDate(stockBatchMap.get(lySkuDTO.getGoodsNo()).getExpireDate(), DateUtils.DATE_PATTERN));
            }

            String key = CommonUtil.createMiddleLineKey(cartListParam.getOrderNo(), lySkuDTO.getGoodsNo());
            if(MapUtils.isNotEmpty(goodsErrInfoMap) && Objects.nonNull(goodsErrInfoMap.get(key))){
                GoodsErrInfo goodsErrInfo = goodsErrInfoMap.get(key);
                lySkuDTO.setErrorMsg(goodsErrInfo.getErrorMsg());
            }
        });
        log.info("supplementSkuList|补全数据赋值耗时:{}.", System.currentTimeMillis()-start1);
    }

    /**
     * 功    能：构建商品信息
     * 作    者：王 伟
     * 时    间：2024/9/23
     */
    private List<PortalLYSkuDTO> copySkuList(String orderId,PromotionGroupDto groupDto, CartListParam cartListParam,List<GoodsPromotionDTO> promotionDTOList){
        long start0 = System.currentTimeMillis();
        List<SkuDto> skuList = groupDto.getSkuList();
        log.info("copySkuList|skuList:{}.", JSON.toJSONString(skuList));
        if(CollectionUtils.isNotEmpty(skuList)){
            List<PortalLYSkuDTO> cartListDtoList = new ArrayList<>();

            List<SkuDto> masterSkuList = skuList.stream().filter(v->!Objects.equals(v.getGoodsType(), CartGoodsTypeEnum.GIFT.getType())
                && !Objects.equals(v.getGoodsType(), CartGoodsTypeEnum.ADDITION.getType())).collect(Collectors.toList());
            List<SkuDto> giftSkuList = skuList.stream().filter(v->Objects.equals(v.getGoodsType(), CartGoodsTypeEnum.GIFT.getType())
                || Objects.equals(v.getGoodsType(), CartGoodsTypeEnum.ADDITION.getType())).collect(Collectors.toList());

            String promotionId = groupDto.getPromotionId();

            //添加主品
            for(SkuDto skuDto : masterSkuList){
                PortalLYSkuDTO lySkuDTO = buildGoodsInfoDTO(groupDto, skuDto,false);
                cartListDtoList.add(lySkuDTO);
                //赠品
                List<SkuDto> giftList = skuDto.getGiftList();
                //这是赠品信息
                buildGiftInfo(skuDto, giftList, cartListDtoList);
            }

            //本组的赠品信息
            List<String> groupGiftIds = Lists.newArrayList();

            //添加赠品商品
            for(SkuDto v : giftSkuList){
                PortalLYSkuDTO lySkuDTO = buildGoodsInfoDTO(groupDto, v,true);
                lySkuDTO.setOrderId(orderId);
                groupGiftIds.add(lySkuDTO.getGiftId());
                cartListDtoList.add(lySkuDTO);
            }

            if(StringUtils.isNotBlank(promotionId)){
                GoodsPromotionDTO promotionDTO = new GoodsPromotionDTO();
                promotionDTO.setId(Long.valueOf(promotionId));
                promotionDTO.setTitle(groupDto.getPromotionTitle());
                promotionDTO.setType(groupDto.getPromotionType());
                promotionDTO.setGiftIds(groupGiftIds);
                promotionDTO.setWay(groupDto.getPromotionWay());
                promotionDTOList.add(promotionDTO);
            }

            log.info("copySkuList|解析商品耗时:{}.cartListDtoList:{}.", (System.currentTimeMillis()-start0), cartListDtoList);
            return cartListDtoList;
        }
        return null;
    }

    /**
     * 功    能：赠品的信息处理
     * 作    者：王 伟
     * 时    间：2024/9/20
     */
    private void buildGiftInfo(SkuDto v, List<SkuDto> giftList, List<PortalLYSkuDTO> cartListDtoList) {
        if(CollectionUtils.isEmpty(giftList)){
            return;
        }
        List<GiftInfoDTO> giftInfoList = Lists.newArrayList();
        List<PortalLYSkuDTO> giftSkuDtoList = Lists.newArrayList();
        for(SkuDto gift : giftList){
            PortalLYSkuDTO giftSkuDTO = buildGoodsInfoDTO(null, gift,true);
            giftSkuDTO.setPromoDTOList(null);
            giftSkuDTO.setAblePromo(Boolean.FALSE);
            giftSkuDTO.setLabelTitle(null);
            giftSkuDTO.setCartSettleprice(giftSkuDTO.getSettleprice());
            giftSkuDTO.setSelfGoodsType(StatusEnums.YES.getCode());
            giftSkuDTO.setOrderId(gift.getOrderId());
            giftSkuDTO.setOrderDetailId(gift.getOrderDetailId());
            giftSkuDTO.setGiftParentId(v.getId());
            List<PromotionDto> promotionList = gift.getPromotionList();
            if(CollectionUtils.isNotEmpty(promotionList)){
                PromotionDto promotionDto = promotionList.get(0);
                giftSkuDTO.setPromotionId(promotionDto.getPromotionId());
                giftSkuDTO.setPromotionType(promotionDto.getPromotionType());
                giftSkuDTO.setPromotionWay(promotionDto.getPromotionWay());
                PromotionGiftRuleDto promotionGiftRuleDto = promotionDto.getPromotionGiftRuleDto();
                if(Objects.nonNull(promotionGiftRuleDto)){
                    giftSkuDTO.setRuleId(promotionGiftRuleDto.getRuleId());
                    giftSkuDTO.setGiftId(promotionGiftRuleDto.getGiftId());
                }
            }
            //赠品不显示促销信息
            giftSkuDTO.setSelectPromotionIdList(Lists.newArrayList());
            GiftInfoDTO giftInfoDTO = new GiftInfoDTO();
            giftInfoDTO.setItemId(Objects.isNull(giftSkuDTO.getItemId())?"":giftSkuDTO.getItemId().toString());
            giftInfoDTO.setGiftId(giftSkuDTO.getGiftId());
            giftInfoDTO.setCount(giftSkuDTO.getCount());
            giftInfoDTO.setRuleId(giftSkuDTO.getRuleId());
            giftInfoDTO.setPromotionId(giftSkuDTO.getPromotionId());
            giftInfoDTO.setGiftType(giftSkuDTO.getGiftType());
            giftInfoDTO.setIds(v.getId());
            giftInfoList.add(giftInfoDTO);
            giftSkuDtoList.add(giftSkuDTO);
        }

        //赠品信息
        String giftInfo = JSON.toJSONString(giftInfoList);
        giftSkuDtoList.forEach(gift->{
            gift.setGiftInfo(giftInfo);
        });
        cartListDtoList.addAll(giftSkuDtoList);
    }

    /**
     * 功    能：构建返回商品的信息
     * 作    者：王 伟
     * 时    间：2024/9/20
     */
    private PortalLYSkuDTO buildGoodsInfoDTO(PromotionGroupDto groupDto, SkuDto v, boolean isGift){
        PortalLYSkuDTO lySkuDTO = new PortalLYSkuDTO();
        lySkuDTO.setCartId(v.getId());
        lySkuDTO.setItemId(Long.parseLong(v.getItemId()));
        lySkuDTO.setSkuId(v.getSkuId());
        lySkuDTO.setGoodsNo(v.getGoodsNo());
        lySkuDTO.setGoodsName(v.getItemTitle());
        lySkuDTO.setManufacturer(v.getFactory());
        lySkuDTO.setGoodsType(v.getGoodsType());
        lySkuDTO.setGoodsTypeDesc(CartGoodsTypeEnum.getEnumDesc(v.getGoodsType()));
        lySkuDTO.setSelfGoodsType(Objects.equals(v.getGoodsType(), CartGoodsTypeEnum.GIFT.getType()) || Objects.equals(v.getGoodsType(), CartGoodsTypeEnum.ADDITION.getType()) ? StatusEnums.YES.getCode() : StatusEnums.NO.getCode());
        lySkuDTO.setCount(v.getCount());
        lySkuDTO.setPayAmount(v.getAmount());
        lySkuDTO.setWholesalePrice(v.getPrice());
        lySkuDTO.setSettleprice(v.getSettlePrice());
        lySkuDTO.setArtifactPrice(v.getArtifactPrice());
//        lySkuDTO.setPromoDTOList(createPromotion(v.getPromotionList()));

        lySkuDTO.setCartSettleprice(lySkuDTO.getSettleprice());
        lySkuDTO.setOrderId(v.getOrderId());
        lySkuDTO.setOrderDetailId(v.getOrderDetailId());

        List<PromotionDto> promotionList = v.getPromotionList();
        if(CollectionUtils.isNotEmpty(promotionList)){
            for(PromotionDto promotionDto:promotionList){
                if(Objects.equals(promotionDto.getPromotionCanSelect(), StatusEnums.YES.getCode())){
                    promotionDto.setDisabled(StatusEnums.NO.getCode());
                }else {
                    promotionDto.setDisabled(StatusEnums.YES.getCode());
                }
                promotionDto.setPromotionTypeDesc(PromotionTypeEnums.getDesc(promotionDto.getPromotionType()));
                if(Objects.equals(promotionDto.getPromotionType(), PromotionTypeEnums.SINGLE_SALE_PRICE.getType()) && Objects.equals(promotionDto.getSeckill(), StatusEnums.YES.getCode())){
                    promotionDto.setPromotionTypeDesc("秒杀");
                }
            }
        }
        promotionList = promotionList.stream().filter(p->Objects.equals(p.getDisabled(),StatusEnums.NO.getCode())).collect(Collectors.toList());
        lySkuDTO.setPromotionList(promotionList);
        //赠品信息不用显示促销
        if(CollectionUtils.isNotEmpty(v.getPromotionList()) && !isGift){
            List<String> selectPromotionIdList = v.getPromotionList().stream().filter(p->Objects.equals(p.getSelect(),StatusEnums.YES.getCode())).map(p->p.getPromotionId()).collect(Collectors.toList());
            lySkuDTO.setSelectPromotionIdList(selectPromotionIdList);
        }

        //单品促销信息
        if(!isGift){
            List<PromotionDto> selectPromotionList = v.getPromotionList().stream()
                .filter(p->Objects.equals(p.getSelect(),StatusEnums.YES.getCode())).collect(Collectors.toList());
            List<GoodsPromotionDTO> promotionDTOList = Lists.newArrayList();
            if(CollectionUtils.isNotEmpty(selectPromotionList)){
                for (PromotionDto promotionDto:selectPromotionList){
                    //不添加本组的促销信息
                    if(Objects.equals(promotionDto.getPromotionId(),groupDto.getPromotionId())){
                        continue;
                    }
                    GoodsPromotionDTO promotionDTO = new GoodsPromotionDTO();
                    promotionDTO.setId(Long.valueOf(promotionDto.getPromotionId()));
                    promotionDTO.setTitle(promotionDto.getPromotionTitle());
                    promotionDTO.setType(promotionDto.getPromotionType());
                    promotionDTO.setWay(promotionDto.getPromotionWay());
                    List<SkuDto> giftList = v.getGiftList();
                    if(CollectionUtils.isNotEmpty(giftList)){
                        promotionDTO.setGiftIds(giftList.stream().map(SkuDto::getGoodsNo).collect(Collectors.toList()));
                    }
                    promotionDTOList.add(promotionDTO);
                }
            }
            lySkuDTO.setPromotionInfo(promotionDTOList);
        }

        lySkuDTO.setAvailableStock(v.getSkuStock());
        lySkuDTO.setLockStockCount(v.getLockStockCount());
        lySkuDTO.setEditPrice(v.getEditPrice());
        int availableStock = 0;
        int lockStockCount = 0;
        if(StringUtils.isNotBlank(v.getSkuStock())){
            availableStock = new BigDecimal(v.getSkuStock()).intValue();
        }
        if(StringUtils.isNotBlank(v.getLockStockCount())){
            lockStockCount = new BigDecimal(v.getLockStockCount()).intValue();
        }

        int availableCount = availableStock + lockStockCount;
        lySkuDTO.setAvailableCount(availableCount+"");
        lySkuDTO.setRecentlyDate(v.getExpireDate());
        if(CollectionUtils.isNotEmpty(lySkuDTO.getPromotionList()) && !isGift){
            lySkuDTO.setAblePromo(Boolean.TRUE);
        }

        //多品规则的信息
        if(Objects.nonNull(groupDto) && CollectionUtils.isNotEmpty(groupDto.getMultiRules()) && !isGift){
            List<PromotionRuleDto> multiRules  = groupDto.getMultiRules();
            PromotionDto promotionDto = new PromotionDto();
            promotionDto.setPromotionRuleGroupList(multiRules);
            promotionDto.setPromotionId(groupDto.getPromotionId());
            promotionDto.setPromotionTypeOrg(groupDto.getPromotionTypeOrg());
            promotionDto.setPromotionType(groupDto.getPromotionType());
            promotionDto.setPromotionTypeDesc(PromotionTypeEnums.getDesc(promotionDto.getPromotionType()));
            promotionDto.setPromotionTypeDesc(PromotionTypeEnums.getDesc(promotionDto.getPromotionType()));
            promotionDto.setPromotionWay(groupDto.getPromotionWay());
            if(CollectionUtils.isEmpty(multiRules)){
                promotionDto.setPromotionTitle("");
            }else {
                String promotionTitle = multiRules.stream().map(item->item.getRuleDesc()).collect(Collectors.joining(","));
                promotionDto.setPromotionTitle(promotionTitle);
            }
            promotionDto.setSelect(groupDto.getSelect());
            promotionDto.setOuterLinkDesc(groupDto.getOuterLinkDesc());
            promotionDto.setIds(groupDto.getIds());
            promotionDto.setDisabled(StatusEnums.YES.getCode());
            lySkuDTO.setMultiPromotionList(Lists.newArrayList(promotionDto));
            if(Objects.equals(promotionDto.getSelect(),StatusEnums.YES.getCode())){
                lySkuDTO.setSelectMultiPromotionIdList(Lists.newArrayList(groupDto.getPromotionId()));
            }
        }

        if(Objects.nonNull(groupDto)  && isGift){

            List<PromotionDto> promotionDtoList = v.getPromotionList();
            PromotionDto promotionDto = promotionDtoList.get(0);
            lySkuDTO.setPromotionId(promotionDto.getPromotionId());
            lySkuDTO.setPromotionType(promotionDto.getPromotionType());
            lySkuDTO.setPromotionWay(promotionDto.getPromotionWay());
            PromotionGiftRuleDto promotionGiftRuleDto = promotionDto.getPromotionGiftRuleDto();
            if(Objects.nonNull(promotionGiftRuleDto)){
                lySkuDTO.setRuleId(promotionGiftRuleDto.getRuleId());
                lySkuDTO.setGiftId(promotionGiftRuleDto.getGiftId());
                lySkuDTO.setGiftType(promotionGiftRuleDto.getGiftType());
            }
            GiftInfoDTO giftInfoDTO = new GiftInfoDTO();
            giftInfoDTO.setItemId(Objects.isNull(lySkuDTO.getItemId())?"":lySkuDTO.getItemId().toString());
            giftInfoDTO.setGiftId(lySkuDTO.getGiftId());
            giftInfoDTO.setCount(lySkuDTO.getCount());
            giftInfoDTO.setRuleId(lySkuDTO.getRuleId());
            giftInfoDTO.setPromotionId(lySkuDTO.getPromotionId());
            giftInfoDTO.setGiftType(lySkuDTO.getGiftType());
            giftInfoDTO.setSkuId(lySkuDTO.getSkuId());
            giftInfoDTO.setIds(groupDto.getIds());

            lySkuDTO.setGiftInfo(JSON.toJSONString(Lists.newArrayList(giftInfoDTO)));
        }

        return lySkuDTO;
    }

    /**
     * 购物车商品级促销信息
     * @param promotionList
     * @return
     */
    private List<PortalLYPromoDTO> createPromotion(List<PromotionDto> promotionList) {
        if(CollectionUtils.isEmpty(promotionList)){
            return new ArrayList<>(1);
        }
        return promotionList.stream().map(v->{
            PortalLYPromoDTO lyPromoDTO = new PortalLYPromoDTO();
            NewPromotionLabelInfo labelInfo = new NewPromotionLabelInfo();
            labelInfo.setPromotionId(v.getPromotionId());
            labelInfo.setPromotionType(v.getPromotionType());
            labelInfo.setPromotionWay(v.getPromotionWay());
            lyPromoDTO.setLabelTitle(v.getPromotionTitle());
            lyPromoDTO.setNewPLabelInfo(labelInfo);
            return lyPromoDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 组织价格和金额
     * @param cartUnifyDTO
     */
    private void recoverCartList(CartUnifyDTO cartUnifyDTO,Long businessId, Long channelStoreId, Long storeId) {
        if(CollectionUtils.isEmpty(cartUnifyDTO.getCartListDtoList())){
            log.info("recoverCartList|数据为空！");
            return;
        }

        long start = System.currentTimeMillis();
//        String priceRedisKey = CommonUtil.createMiddleLineKey(ConstantPool.REDIS_PORTAL_B2B_CART_GOODS_KEY,String.valueOf(storeId),String.valueOf(channelStoreId));
//        Map<String, String> priceRMap = redissonCacheService.getRedisMap(priceRedisKey);

        cartUnifyDTO.getCartListDtoList().stream().forEach(v->{
//            String retailCodeKey = CommonUtil.createMiddleLineKey(ConstantPool.REDIS_PORTAL_B2B_CART_GOODS_KEY,String.valueOf(storeId),String.valueOf(channelStoreId), String.valueOf(v.getItemId()));
            v.setGoodsMold(getGoodsMold(v));
            v.setSettleprice(v.getSettleprice());
            v.setPayAmount(v.getPayAmount());
            v.setDefaulySettleprice(v.getSettleprice());//前端用于计算
            v.setReadSettleprice(v.getSettleprice());//只读价格（前端快捷键场景）
            v.setReadCount(v.getCount());//只读数量（前端快捷键场景）
            v.setCostAmount(computePayAmount(v.getCostPrice(), v.getCount()));
            v.setNoTaxAmount(computeNoTaxAmount(v.getSettleprice(), v.getCount(),v.getIncometaxrate()));
            v.setGrossAmount(computeGrossAmount(v.getSettleprice(), v.getCount(), v.getCostPrice()));
            BigDecimal rateDecimal = computeGrossRateDecimal(v.getSettleprice(), v.getCostPrice(), v.getCount());
            v.setGrossRate(BigDecimalUtils.covertDecimalToPercent(rateDecimal));
            v.setExtendMap(detailParamMap(v, rateDecimal));
        });
        //总价
        BigDecimal payAmountDecimal = cartUnifyDTO.getCartListDtoList().stream().filter(v->StringUtils.isNotBlank(v.getPayAmount())).map(v -> new BigDecimal(v.getPayAmount())).reduce(BigDecimal.ZERO, BigDecimal::add);
        cartUnifyDTO.setPayAmount(cartUnifyDTO.getTotalAmount());
//        cartUnifyDTO.setPayAmount(BigDecimalUtils.covertDecimalToString(payAmountDecimal, Constants.FEN_SCALE));
        //总成本价
        BigDecimal costPriceDecimal = cartUnifyDTO.getCartListDtoList().stream().filter(v->StringUtils.isNotBlank(v.getCostAmount())).map(v -> new BigDecimal(v.getCostAmount())).reduce(BigDecimal.ZERO, BigDecimal::add);
        String costPrice = BigDecimalUtils.covertDecimalToString(costPriceDecimal, Constants.FEN_SCALE);
        cartUnifyDTO.setCostAmount(costPrice);
        cartUnifyDTO.setGrossAmount(BigDecimalUtils.covertDecimalToString(payAmountDecimal.subtract(costPriceDecimal), Constants.FEN_SCALE));
        BigDecimal allRateDecimal = computeGrossRateDecimal(cartUnifyDTO.getPayAmount(), costPrice);
        cartUnifyDTO.setGrossRate(BigDecimalUtils.covertDecimalToPercent(allRateDecimal));

        //abk金额
        List<String> abkConfig = portalConfigService.findAbkConfig(businessId);
        BigDecimal abkAmount = cartUnifyDTO.getCartListDtoList().stream().filter(v->StringUtils.isNotBlank(v.getPayAmount()) && abkConfig.contains(v.getWholesalepushlevel())).map(v -> new BigDecimal(v.getPayAmount())).reduce(BigDecimal.ZERO, BigDecimal::add);
        cartUnifyDTO.setAbkAmount(BigDecimalUtils.covertDecimalToString(abkAmount, Constants.FEN_SCALE));

        cartUnifyDTO.setExtendParamMap(headParamMap(cartUnifyDTO.getGrossAmount(), allRateDecimal));
        log.info("recoverCartList|改价逻辑耗时:{}..", (System.currentTimeMillis()-start));
    }

    /**
     * 毛利率
     * @param settleprice
     * @param costPrice
     * @return
     */

    private static BigDecimal computeGrossRateDecimal(String settleprice, String costPrice) {
        if(StringUtils.isBlank(settleprice)){
            return BigDecimal.ZERO;
        }
        BigDecimal haveTaxPrice = new BigDecimal(settleprice);
        BigDecimal noTaxPrice = StringUtils.isBlank(costPrice)?BigDecimal.ZERO:new BigDecimal(costPrice);
        if(haveTaxPrice.compareTo(BigDecimal.ZERO)==0){
            return BigDecimal.ZERO;
        }
        BigDecimal chaDecimal = haveTaxPrice.subtract(noTaxPrice);
        BigDecimal simpleResult = chaDecimal.divide(haveTaxPrice, 4, BigDecimal.ROUND_HALF_UP);
        return simpleResult;
    }

    private static BigDecimal computeGrossRateDecimal(String settleprice, String costPrice, String count) {
        if(StringUtils.isBlank(settleprice)){
            return BigDecimal.ZERO;
        }
        SettleFelParam settleFelParam = new SettleFelParam();
        settleFelParam.setHaveTaxPrice(new BigDecimal(settleprice));
        settleFelParam.setNoTaxPrice(StringUtils.isBlank(costPrice)?BigDecimal.ZERO:new BigDecimal(costPrice));
        settleFelParam.setDeliveryCount(BigDecimalUtils.parseTsscDecimal(count));
        if(settleFelParam.getHaveTaxPrice().compareTo(BigDecimal.ZERO)==0){
            return BigDecimal.ZERO;
        }
//        BigDecimal rateDecimal = FelUtil.cPriceAndAmount(FelUtil.PORTAL_SKU_PRICE_GROSS_RATE, settleFelParam, 4);
        BigDecimal rateDecimal = (settleFelParam.getHaveTaxPrice().multiply(settleFelParam.getDeliveryCount()).subtract(settleFelParam.getNoTaxPrice().multiply(settleFelParam.getDeliveryCount()))).divide(settleFelParam.getHaveTaxPrice().multiply(settleFelParam.getDeliveryCount()), 4, BigDecimal.ROUND_HALF_UP);
//        log.info("computeGrossRateDecimal|计算耗时:{}.rateDecimal:{}.", (System.currentTimeMillis()-start),rateDecimal);
        return rateDecimal;
    }

    /**
     * 毛利额
     * @param settleprice
     * @param count
     * @param costPrice
     * @return
     */
    private String computeGrossAmount(String settleprice, String count, String costPrice) {
        if(StringUtils.isBlank(count) || StringUtils.isBlank(settleprice)){
            return StringUtils.EMPTY;
        }
        BigDecimal haveTaxxPrice = new BigDecimal(settleprice);
        BigDecimal noTaxPrice = Objects.isNull(costPrice)?BigDecimal.ZERO:new BigDecimal(costPrice);
        BigDecimal countDecimal = BigDecimalUtils.parseTsscDecimal(count);
        BigDecimal chaH = haveTaxxPrice.multiply(countDecimal);
        BigDecimal chaN = noTaxPrice.multiply(countDecimal);
        String simpleResult = BigDecimalUtils.covertDecimalToString(chaH.subtract(chaN).setScale(Constants.YUAN_SCALE, BigDecimal.ROUND_HALF_UP));
        return simpleResult;
    }


    /**
     * 无税金额
     * @param settleprice
     * @param count
     * @param incometaxrate
     * @return
     */
    private String computeNoTaxAmount(String settleprice, String count, String incometaxrate) {
        if(StringUtils.isBlank(count) || StringUtils.isBlank(incometaxrate) || StringUtils.isBlank(settleprice)){
            return StringUtils.EMPTY;
        }
        BigDecimal taxRate = BigDecimal.ZERO;
        try {
            taxRate = SrmTaxCodeEnum.getTaxRateByCode(incometaxrate);
        } catch (Exception e) {
            taxRate = BigDecimal.ZERO;
        }
        BigDecimal haveTaxPrice = new BigDecimal(settleprice);
        BigDecimal countDecimal = BigDecimalUtils.parseTsscDecimal(count);
        String simpleResult = BigDecimalUtils.covertDecimalToString(haveTaxPrice.multiply(countDecimal).divide(BigDecimal.ONE.add(taxRate), Constants.YUAN_SCALE, BigDecimal.ROUND_HALF_UP));
        return simpleResult;
    }


    /**
     * 支付金额
     * @param settleprice
     * @param count
     * @return
     */
    private String computePayAmount(String settleprice, String count) {
        if(StringUtils.isBlank(count) || StringUtils.isBlank(settleprice)){
            return StringUtils.EMPTY;
        }
        String simpleResult = BigDecimalUtils.covertDecimalToString(new BigDecimal(settleprice).multiply(BigDecimalUtils.parseTsscDecimal(count)).setScale(Constants.FEN_SCALE, BigDecimal.ROUND_HALF_UP));
        return simpleResult;
    }

    private String getSettleprice(PortalLYSkuDTO srmPermSpuDto) {
//        log.info("getSettleprice|1|srmPermSpuDto:{}.", JSONObject.toJSON(srmPermSpuDto));
//        if (srmPermSpuDto.getSelfGoodsType() == 1) {
//            return srmPermSpuDto.getSettleprice();
//        }
        String settlePrice = srmPermSpuDto.getSettleprice();
        if (Objects.equals(GoodsMoldEnum.LOCK_PRICE.getCode(), srmPermSpuDto.getGoodsMold())) {
            settlePrice = srmPermSpuDto.getWholesalePrice();
        }
        return settlePrice;
    }

    public static final String grossAmount = "grossAmount";
    public static final String grossRate = "grossRate";
    public static final String costPrice = "costPrice";
    public static final String apprdocno = "apprdocno";
    public static final String expireDate = "expireDate";
    public static final String wholesalepushlevel = "wholesalepushlevel";
    public static final String innerqty = "innerqty";

    private Map<String, String> headParamMap(String grossAmountStr, BigDecimal allRateDecimal){
        Map<String,String> extendParamMap = new HashMap<>();
        extendParamMap.put(grossAmount, grossAmountStr);
        extendParamMap.put(grossRate, BigDecimalUtils.covertDecimalToString(allRateDecimal));
        return extendParamMap;
    }

    private Map<String, String> detailParamMap(PortalLYSkuDTO v, BigDecimal rateDecimal){
        Map<String,String> extendMap = new HashMap<>();
        extendMap.put(costPrice, v.getCostPrice());
        extendMap.put(apprdocno, v.getApprdocno());
        extendMap.put(expireDate, v.getRecentlyDate());
        extendMap.put(wholesalepushlevel, v.getWholesalepushlevel());
        extendMap.put(innerqty, v.getInnerqty());
        extendMap.put(grossAmount, v.getGrossAmount());
        extendMap.put(grossRate, BigDecimalUtils.covertDecimalToString(rateDecimal));
        return extendMap;
    }
}
