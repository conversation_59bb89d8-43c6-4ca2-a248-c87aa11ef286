<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmSaleOrderDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmSaleOrderDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="published" jdbcType="TINYINT" property="published" />
    <result column="business_day_date" jdbcType="VARCHAR" property="businessDayDate" />
    <result column="tran_time" jdbcType="VARCHAR" property="tranTime" />
    <result column="ori_tran_time" jdbcType="VARCHAR" property="oriTranTime" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="retail_store_id" jdbcType="VARCHAR" property="retailStoreId" />
    <result column="retail_store_name" jdbcType="VARCHAR" property="retailStoreName" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_no_line" jdbcType="VARCHAR" property="orderNoLine" />
    <result column="ori_order_no" jdbcType="VARCHAR" property="oriOrderNo" />
    <result column="ori_order_no_line" jdbcType="VARCHAR" property="oriOrderNoLine" />
    <result column="temid" jdbcType="VARCHAR" property="temid" />
    <result column="temid_name" jdbcType="VARCHAR" property="temidName" />
    <result column="temid_spec" jdbcType="VARCHAR" property="temidSpec" />
    <result column="batch_id" jdbcType="VARCHAR" property="batchId" />
    <result column="retail_quantity" jdbcType="DECIMAL" property="retailQuantity" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="price_tax" jdbcType="DECIMAL" property="priceTax" />
    <result column="retail_amount" jdbcType="DECIMAL" property="retailAmount" />
    <result column="dmbtr" jdbcType="DECIMAL" property="dmbtr" />
    <result column="eff_date" jdbcType="VARCHAR" property="effDate" />
    <result column="member_id" jdbcType="BIGINT" property="memberId" />
    <result column="manu" jdbcType="VARCHAR" property="manu" />
    <result column="manu_code" jdbcType="VARCHAR" property="manuCode" />
    <result column="lifnr" jdbcType="VARCHAR" property="lifnr" />
    <result column="lifnr_name" jdbcType="VARCHAR" property="lifnrName" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="batch_code" jdbcType="VARCHAR" property="batchCode" />
    <result column="app_num" jdbcType="VARCHAR" property="appNum" />
    <result column="product_date" jdbcType="VARCHAR" property="productDate" />
    <result column="material_channel" jdbcType="VARCHAR" property="materialChannel" />
    <result column="bk01" jdbcType="VARCHAR" property="bk01" />
    <result column="bk02" jdbcType="VARCHAR" property="bk02" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, published, business_day_date, tran_time, ori_tran_time, company_code, company_name, 
    retail_store_id, retail_store_name, order_no, order_no_line, ori_order_no, ori_order_no_line, 
    temid, temid_name, temid_spec, batch_id, retail_quantity, unit, price_tax, retail_amount, 
    dmbtr, eff_date, member_id, manu, manu_code, lifnr, lifnr_name, bar_code, batch_code, 
    app_num, product_date, material_channel, bk01, bk02, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmSaleOrderDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_sale_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_sale_order_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from srm_sale_order_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmSaleOrderDetailExample">
    delete from srm_sale_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmSaleOrderDetail">
    insert into srm_sale_order_detail (id, published, business_day_date, 
      tran_time, ori_tran_time, company_code, 
      company_name, retail_store_id, retail_store_name, 
      order_no, order_no_line, ori_order_no, 
      ori_order_no_line, temid, temid_name, 
      temid_spec, batch_id, retail_quantity, 
      unit, price_tax, retail_amount, 
      dmbtr, eff_date, member_id, 
      manu, manu_code, lifnr, 
      lifnr_name, bar_code, batch_code, 
      app_num, product_date, material_channel, 
      bk01, bk02, gmt_create, 
      gmt_update)
    values (#{id,jdbcType=BIGINT}, #{published,jdbcType=TINYINT}, #{businessDayDate,jdbcType=VARCHAR}, 
      #{tranTime,jdbcType=VARCHAR}, #{oriTranTime,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR}, 
      #{companyName,jdbcType=VARCHAR}, #{retailStoreId,jdbcType=VARCHAR}, #{retailStoreName,jdbcType=VARCHAR}, 
      #{orderNo,jdbcType=VARCHAR}, #{orderNoLine,jdbcType=VARCHAR}, #{oriOrderNo,jdbcType=VARCHAR}, 
      #{oriOrderNoLine,jdbcType=VARCHAR}, #{temid,jdbcType=VARCHAR}, #{temidName,jdbcType=VARCHAR}, 
      #{temidSpec,jdbcType=VARCHAR}, #{batchId,jdbcType=VARCHAR}, #{retailQuantity,jdbcType=DECIMAL}, 
      #{unit,jdbcType=VARCHAR}, #{priceTax,jdbcType=DECIMAL}, #{retailAmount,jdbcType=DECIMAL}, 
      #{dmbtr,jdbcType=DECIMAL}, #{effDate,jdbcType=VARCHAR}, #{memberId,jdbcType=BIGINT}, 
      #{manu,jdbcType=VARCHAR}, #{manuCode,jdbcType=VARCHAR}, #{lifnr,jdbcType=VARCHAR}, 
      #{lifnrName,jdbcType=VARCHAR}, #{barCode,jdbcType=VARCHAR}, #{batchCode,jdbcType=VARCHAR}, 
      #{appNum,jdbcType=VARCHAR}, #{productDate,jdbcType=VARCHAR}, #{materialChannel,jdbcType=VARCHAR}, 
      #{bk01,jdbcType=VARCHAR}, #{bk02,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmSaleOrderDetail">
    insert into srm_sale_order_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="published != null">
        published,
      </if>
      <if test="businessDayDate != null">
        business_day_date,
      </if>
      <if test="tranTime != null">
        tran_time,
      </if>
      <if test="oriTranTime != null">
        ori_tran_time,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="retailStoreId != null">
        retail_store_id,
      </if>
      <if test="retailStoreName != null">
        retail_store_name,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="orderNoLine != null">
        order_no_line,
      </if>
      <if test="oriOrderNo != null">
        ori_order_no,
      </if>
      <if test="oriOrderNoLine != null">
        ori_order_no_line,
      </if>
      <if test="temid != null">
        temid,
      </if>
      <if test="temidName != null">
        temid_name,
      </if>
      <if test="temidSpec != null">
        temid_spec,
      </if>
      <if test="batchId != null">
        batch_id,
      </if>
      <if test="retailQuantity != null">
        retail_quantity,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="priceTax != null">
        price_tax,
      </if>
      <if test="retailAmount != null">
        retail_amount,
      </if>
      <if test="dmbtr != null">
        dmbtr,
      </if>
      <if test="effDate != null">
        eff_date,
      </if>
      <if test="memberId != null">
        member_id,
      </if>
      <if test="manu != null">
        manu,
      </if>
      <if test="manuCode != null">
        manu_code,
      </if>
      <if test="lifnr != null">
        lifnr,
      </if>
      <if test="lifnrName != null">
        lifnr_name,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="batchCode != null">
        batch_code,
      </if>
      <if test="appNum != null">
        app_num,
      </if>
      <if test="productDate != null">
        product_date,
      </if>
      <if test="materialChannel != null">
        material_channel,
      </if>
      <if test="bk01 != null">
        bk01,
      </if>
      <if test="bk02 != null">
        bk02,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="published != null">
        #{published,jdbcType=TINYINT},
      </if>
      <if test="businessDayDate != null">
        #{businessDayDate,jdbcType=VARCHAR},
      </if>
      <if test="tranTime != null">
        #{tranTime,jdbcType=VARCHAR},
      </if>
      <if test="oriTranTime != null">
        #{oriTranTime,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="retailStoreId != null">
        #{retailStoreId,jdbcType=VARCHAR},
      </if>
      <if test="retailStoreName != null">
        #{retailStoreName,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNoLine != null">
        #{orderNoLine,jdbcType=VARCHAR},
      </if>
      <if test="oriOrderNo != null">
        #{oriOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="oriOrderNoLine != null">
        #{oriOrderNoLine,jdbcType=VARCHAR},
      </if>
      <if test="temid != null">
        #{temid,jdbcType=VARCHAR},
      </if>
      <if test="temidName != null">
        #{temidName,jdbcType=VARCHAR},
      </if>
      <if test="temidSpec != null">
        #{temidSpec,jdbcType=VARCHAR},
      </if>
      <if test="batchId != null">
        #{batchId,jdbcType=VARCHAR},
      </if>
      <if test="retailQuantity != null">
        #{retailQuantity,jdbcType=DECIMAL},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="priceTax != null">
        #{priceTax,jdbcType=DECIMAL},
      </if>
      <if test="retailAmount != null">
        #{retailAmount,jdbcType=DECIMAL},
      </if>
      <if test="dmbtr != null">
        #{dmbtr,jdbcType=DECIMAL},
      </if>
      <if test="effDate != null">
        #{effDate,jdbcType=VARCHAR},
      </if>
      <if test="memberId != null">
        #{memberId,jdbcType=BIGINT},
      </if>
      <if test="manu != null">
        #{manu,jdbcType=VARCHAR},
      </if>
      <if test="manuCode != null">
        #{manuCode,jdbcType=VARCHAR},
      </if>
      <if test="lifnr != null">
        #{lifnr,jdbcType=VARCHAR},
      </if>
      <if test="lifnrName != null">
        #{lifnrName,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="batchCode != null">
        #{batchCode,jdbcType=VARCHAR},
      </if>
      <if test="appNum != null">
        #{appNum,jdbcType=VARCHAR},
      </if>
      <if test="productDate != null">
        #{productDate,jdbcType=VARCHAR},
      </if>
      <if test="materialChannel != null">
        #{materialChannel,jdbcType=VARCHAR},
      </if>
      <if test="bk01 != null">
        #{bk01,jdbcType=VARCHAR},
      </if>
      <if test="bk02 != null">
        #{bk02,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmSaleOrderDetailExample" resultType="java.lang.Long">
    select count(*) from srm_sale_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_sale_order_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.published != null">
        published = #{record.published,jdbcType=TINYINT},
      </if>
      <if test="record.businessDayDate != null">
        business_day_date = #{record.businessDayDate,jdbcType=VARCHAR},
      </if>
      <if test="record.tranTime != null">
        tran_time = #{record.tranTime,jdbcType=VARCHAR},
      </if>
      <if test="record.oriTranTime != null">
        ori_tran_time = #{record.oriTranTime,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.retailStoreId != null">
        retail_store_id = #{record.retailStoreId,jdbcType=VARCHAR},
      </if>
      <if test="record.retailStoreName != null">
        retail_store_name = #{record.retailStoreName,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNo != null">
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNoLine != null">
        order_no_line = #{record.orderNoLine,jdbcType=VARCHAR},
      </if>
      <if test="record.oriOrderNo != null">
        ori_order_no = #{record.oriOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.oriOrderNoLine != null">
        ori_order_no_line = #{record.oriOrderNoLine,jdbcType=VARCHAR},
      </if>
      <if test="record.temid != null">
        temid = #{record.temid,jdbcType=VARCHAR},
      </if>
      <if test="record.temidName != null">
        temid_name = #{record.temidName,jdbcType=VARCHAR},
      </if>
      <if test="record.temidSpec != null">
        temid_spec = #{record.temidSpec,jdbcType=VARCHAR},
      </if>
      <if test="record.batchId != null">
        batch_id = #{record.batchId,jdbcType=VARCHAR},
      </if>
      <if test="record.retailQuantity != null">
        retail_quantity = #{record.retailQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.priceTax != null">
        price_tax = #{record.priceTax,jdbcType=DECIMAL},
      </if>
      <if test="record.retailAmount != null">
        retail_amount = #{record.retailAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.dmbtr != null">
        dmbtr = #{record.dmbtr,jdbcType=DECIMAL},
      </if>
      <if test="record.effDate != null">
        eff_date = #{record.effDate,jdbcType=VARCHAR},
      </if>
      <if test="record.memberId != null">
        member_id = #{record.memberId,jdbcType=BIGINT},
      </if>
      <if test="record.manu != null">
        manu = #{record.manu,jdbcType=VARCHAR},
      </if>
      <if test="record.manuCode != null">
        manu_code = #{record.manuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.lifnr != null">
        lifnr = #{record.lifnr,jdbcType=VARCHAR},
      </if>
      <if test="record.lifnrName != null">
        lifnr_name = #{record.lifnrName,jdbcType=VARCHAR},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=VARCHAR},
      </if>
      <if test="record.batchCode != null">
        batch_code = #{record.batchCode,jdbcType=VARCHAR},
      </if>
      <if test="record.appNum != null">
        app_num = #{record.appNum,jdbcType=VARCHAR},
      </if>
      <if test="record.productDate != null">
        product_date = #{record.productDate,jdbcType=VARCHAR},
      </if>
      <if test="record.materialChannel != null">
        material_channel = #{record.materialChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.bk01 != null">
        bk01 = #{record.bk01,jdbcType=VARCHAR},
      </if>
      <if test="record.bk02 != null">
        bk02 = #{record.bk02,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_sale_order_detail
    set id = #{record.id,jdbcType=BIGINT},
      published = #{record.published,jdbcType=TINYINT},
      business_day_date = #{record.businessDayDate,jdbcType=VARCHAR},
      tran_time = #{record.tranTime,jdbcType=VARCHAR},
      ori_tran_time = #{record.oriTranTime,jdbcType=VARCHAR},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      retail_store_id = #{record.retailStoreId,jdbcType=VARCHAR},
      retail_store_name = #{record.retailStoreName,jdbcType=VARCHAR},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      order_no_line = #{record.orderNoLine,jdbcType=VARCHAR},
      ori_order_no = #{record.oriOrderNo,jdbcType=VARCHAR},
      ori_order_no_line = #{record.oriOrderNoLine,jdbcType=VARCHAR},
      temid = #{record.temid,jdbcType=VARCHAR},
      temid_name = #{record.temidName,jdbcType=VARCHAR},
      temid_spec = #{record.temidSpec,jdbcType=VARCHAR},
      batch_id = #{record.batchId,jdbcType=VARCHAR},
      retail_quantity = #{record.retailQuantity,jdbcType=DECIMAL},
      unit = #{record.unit,jdbcType=VARCHAR},
      price_tax = #{record.priceTax,jdbcType=DECIMAL},
      retail_amount = #{record.retailAmount,jdbcType=DECIMAL},
      dmbtr = #{record.dmbtr,jdbcType=DECIMAL},
      eff_date = #{record.effDate,jdbcType=VARCHAR},
      member_id = #{record.memberId,jdbcType=BIGINT},
      manu = #{record.manu,jdbcType=VARCHAR},
      manu_code = #{record.manuCode,jdbcType=VARCHAR},
      lifnr = #{record.lifnr,jdbcType=VARCHAR},
      lifnr_name = #{record.lifnrName,jdbcType=VARCHAR},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      batch_code = #{record.batchCode,jdbcType=VARCHAR},
      app_num = #{record.appNum,jdbcType=VARCHAR},
      product_date = #{record.productDate,jdbcType=VARCHAR},
      material_channel = #{record.materialChannel,jdbcType=VARCHAR},
      bk01 = #{record.bk01,jdbcType=VARCHAR},
      bk02 = #{record.bk02,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmSaleOrderDetail">
    update srm_sale_order_detail
    <set>
      <if test="published != null">
        published = #{published,jdbcType=TINYINT},
      </if>
      <if test="businessDayDate != null">
        business_day_date = #{businessDayDate,jdbcType=VARCHAR},
      </if>
      <if test="tranTime != null">
        tran_time = #{tranTime,jdbcType=VARCHAR},
      </if>
      <if test="oriTranTime != null">
        ori_tran_time = #{oriTranTime,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="retailStoreId != null">
        retail_store_id = #{retailStoreId,jdbcType=VARCHAR},
      </if>
      <if test="retailStoreName != null">
        retail_store_name = #{retailStoreName,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNoLine != null">
        order_no_line = #{orderNoLine,jdbcType=VARCHAR},
      </if>
      <if test="oriOrderNo != null">
        ori_order_no = #{oriOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="oriOrderNoLine != null">
        ori_order_no_line = #{oriOrderNoLine,jdbcType=VARCHAR},
      </if>
      <if test="temid != null">
        temid = #{temid,jdbcType=VARCHAR},
      </if>
      <if test="temidName != null">
        temid_name = #{temidName,jdbcType=VARCHAR},
      </if>
      <if test="temidSpec != null">
        temid_spec = #{temidSpec,jdbcType=VARCHAR},
      </if>
      <if test="batchId != null">
        batch_id = #{batchId,jdbcType=VARCHAR},
      </if>
      <if test="retailQuantity != null">
        retail_quantity = #{retailQuantity,jdbcType=DECIMAL},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="priceTax != null">
        price_tax = #{priceTax,jdbcType=DECIMAL},
      </if>
      <if test="retailAmount != null">
        retail_amount = #{retailAmount,jdbcType=DECIMAL},
      </if>
      <if test="dmbtr != null">
        dmbtr = #{dmbtr,jdbcType=DECIMAL},
      </if>
      <if test="effDate != null">
        eff_date = #{effDate,jdbcType=VARCHAR},
      </if>
      <if test="memberId != null">
        member_id = #{memberId,jdbcType=BIGINT},
      </if>
      <if test="manu != null">
        manu = #{manu,jdbcType=VARCHAR},
      </if>
      <if test="manuCode != null">
        manu_code = #{manuCode,jdbcType=VARCHAR},
      </if>
      <if test="lifnr != null">
        lifnr = #{lifnr,jdbcType=VARCHAR},
      </if>
      <if test="lifnrName != null">
        lifnr_name = #{lifnrName,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="batchCode != null">
        batch_code = #{batchCode,jdbcType=VARCHAR},
      </if>
      <if test="appNum != null">
        app_num = #{appNum,jdbcType=VARCHAR},
      </if>
      <if test="productDate != null">
        product_date = #{productDate,jdbcType=VARCHAR},
      </if>
      <if test="materialChannel != null">
        material_channel = #{materialChannel,jdbcType=VARCHAR},
      </if>
      <if test="bk01 != null">
        bk01 = #{bk01,jdbcType=VARCHAR},
      </if>
      <if test="bk02 != null">
        bk02 = #{bk02,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmSaleOrderDetail">
    update srm_sale_order_detail
    set published = #{published,jdbcType=TINYINT},
      business_day_date = #{businessDayDate,jdbcType=VARCHAR},
      tran_time = #{tranTime,jdbcType=VARCHAR},
      ori_tran_time = #{oriTranTime,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      retail_store_id = #{retailStoreId,jdbcType=VARCHAR},
      retail_store_name = #{retailStoreName,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      order_no_line = #{orderNoLine,jdbcType=VARCHAR},
      ori_order_no = #{oriOrderNo,jdbcType=VARCHAR},
      ori_order_no_line = #{oriOrderNoLine,jdbcType=VARCHAR},
      temid = #{temid,jdbcType=VARCHAR},
      temid_name = #{temidName,jdbcType=VARCHAR},
      temid_spec = #{temidSpec,jdbcType=VARCHAR},
      batch_id = #{batchId,jdbcType=VARCHAR},
      retail_quantity = #{retailQuantity,jdbcType=DECIMAL},
      unit = #{unit,jdbcType=VARCHAR},
      price_tax = #{priceTax,jdbcType=DECIMAL},
      retail_amount = #{retailAmount,jdbcType=DECIMAL},
      dmbtr = #{dmbtr,jdbcType=DECIMAL},
      eff_date = #{effDate,jdbcType=VARCHAR},
      member_id = #{memberId,jdbcType=BIGINT},
      manu = #{manu,jdbcType=VARCHAR},
      manu_code = #{manuCode,jdbcType=VARCHAR},
      lifnr = #{lifnr,jdbcType=VARCHAR},
      lifnr_name = #{lifnrName,jdbcType=VARCHAR},
      bar_code = #{barCode,jdbcType=VARCHAR},
      batch_code = #{batchCode,jdbcType=VARCHAR},
      app_num = #{appNum,jdbcType=VARCHAR},
      product_date = #{productDate,jdbcType=VARCHAR},
      material_channel = #{materialChannel,jdbcType=VARCHAR},
      bk01 = #{bk01,jdbcType=VARCHAR},
      bk02 = #{bk02,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>