<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.ExecuteOrderGoodsMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.ExecuteOrderGoods">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="execute_code" jdbcType="VARCHAR" property="executeCode" />
    <result column="execute_name" jdbcType="VARCHAR" property="executeName" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="purchase_type" jdbcType="TINYINT" property="purchaseType" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="bill_price" jdbcType="DECIMAL" property="billPrice" />
    <result column="rebate_price" jdbcType="DECIMAL" property="rebatePrice" />
    <result column="check_price" jdbcType="DECIMAL" property="checkPrice" />
    <result column="retail_price" jdbcType="DECIMAL" property="retailPrice" />
    <result column="push_level" jdbcType="VARCHAR" property="pushLevel" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="static_synthesis_gross_bate" jdbcType="DECIMAL" property="staticSynthesisGrossBate" />
    <result column="inspect_store" jdbcType="VARCHAR" property="inspectStore" />
    <result column="first_shop_goods_quantity" jdbcType="VARCHAR" property="firstShopGoodsQuantity" />
    <result column="check_able" jdbcType="VARCHAR" property="checkAble" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="creater_org_id" jdbcType="BIGINT" property="createrOrgId" />
    <result column="creater_org_name" jdbcType="VARCHAR" property="createrOrgName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="updater_org_id" jdbcType="BIGINT" property="updaterOrgId" />
    <result column="updater_org_name" jdbcType="VARCHAR" property="updaterOrgName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, execute_code, execute_name, org_id, org_name, purchase_type, goods_code, bar_code, 
    goods_common_name, goods_name, description, specifications, approval_number, dosage_form, 
    manufacturer, bill_price, rebate_price, check_price, retail_price, push_level, status, 
    static_synthesis_gross_bate, inspect_store, first_shop_goods_quantity, check_able, 
    gmt_create, gmt_update, extend, version, created_by, created_name, creater_org_id, 
    creater_org_name, updated_by, updated_name, updater_org_id, updater_org_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.ExecuteOrderGoodsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from execute_order_goods
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from execute_order_goods
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from execute_order_goods
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.ExecuteOrderGoodsExample">
    delete from execute_order_goods
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.ExecuteOrderGoods">
    insert into execute_order_goods (id, execute_code, execute_name, 
      org_id, org_name, purchase_type, 
      goods_code, bar_code, goods_common_name, 
      goods_name, description, specifications, 
      approval_number, dosage_form, manufacturer, 
      bill_price, rebate_price, check_price, 
      retail_price, push_level, status, 
      static_synthesis_gross_bate, inspect_store, 
      first_shop_goods_quantity, check_able, gmt_create, 
      gmt_update, extend, version, 
      created_by, created_name, creater_org_id, 
      creater_org_name, updated_by, updated_name, 
      updater_org_id, updater_org_name)
    values (#{id,jdbcType=INTEGER}, #{executeCode,jdbcType=VARCHAR}, #{executeName,jdbcType=VARCHAR}, 
      #{orgId,jdbcType=BIGINT}, #{orgName,jdbcType=VARCHAR}, #{purchaseType,jdbcType=TINYINT}, 
      #{goodsCode,jdbcType=VARCHAR}, #{barCode,jdbcType=VARCHAR}, #{goodsCommonName,jdbcType=VARCHAR}, 
      #{goodsName,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{specifications,jdbcType=VARCHAR}, 
      #{approvalNumber,jdbcType=VARCHAR}, #{dosageForm,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, 
      #{billPrice,jdbcType=DECIMAL}, #{rebatePrice,jdbcType=DECIMAL}, #{checkPrice,jdbcType=DECIMAL}, 
      #{retailPrice,jdbcType=DECIMAL}, #{pushLevel,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{staticSynthesisGrossBate,jdbcType=DECIMAL}, #{inspectStore,jdbcType=VARCHAR}, 
      #{firstShopGoodsQuantity,jdbcType=VARCHAR}, #{checkAble,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{createrOrgId,jdbcType=BIGINT}, 
      #{createrOrgName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR}, 
      #{updaterOrgId,jdbcType=BIGINT}, #{updaterOrgName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.ExecuteOrderGoods">
    insert into execute_order_goods
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="executeCode != null">
        execute_code,
      </if>
      <if test="executeName != null">
        execute_name,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="purchaseType != null">
        purchase_type,
      </if>
      <if test="goodsCode != null">
        goods_code,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="goodsCommonName != null">
        goods_common_name,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="approvalNumber != null">
        approval_number,
      </if>
      <if test="dosageForm != null">
        dosage_form,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="billPrice != null">
        bill_price,
      </if>
      <if test="rebatePrice != null">
        rebate_price,
      </if>
      <if test="checkPrice != null">
        check_price,
      </if>
      <if test="retailPrice != null">
        retail_price,
      </if>
      <if test="pushLevel != null">
        push_level,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="staticSynthesisGrossBate != null">
        static_synthesis_gross_bate,
      </if>
      <if test="inspectStore != null">
        inspect_store,
      </if>
      <if test="firstShopGoodsQuantity != null">
        first_shop_goods_quantity,
      </if>
      <if test="checkAble != null">
        check_able,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="createrOrgId != null">
        creater_org_id,
      </if>
      <if test="createrOrgName != null">
        creater_org_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
      <if test="updaterOrgId != null">
        updater_org_id,
      </if>
      <if test="updaterOrgName != null">
        updater_org_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="executeCode != null">
        #{executeCode,jdbcType=VARCHAR},
      </if>
      <if test="executeName != null">
        #{executeName,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="purchaseType != null">
        #{purchaseType,jdbcType=TINYINT},
      </if>
      <if test="goodsCode != null">
        #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="approvalNumber != null">
        #{approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="billPrice != null">
        #{billPrice,jdbcType=DECIMAL},
      </if>
      <if test="rebatePrice != null">
        #{rebatePrice,jdbcType=DECIMAL},
      </if>
      <if test="checkPrice != null">
        #{checkPrice,jdbcType=DECIMAL},
      </if>
      <if test="retailPrice != null">
        #{retailPrice,jdbcType=DECIMAL},
      </if>
      <if test="pushLevel != null">
        #{pushLevel,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="staticSynthesisGrossBate != null">
        #{staticSynthesisGrossBate,jdbcType=DECIMAL},
      </if>
      <if test="inspectStore != null">
        #{inspectStore,jdbcType=VARCHAR},
      </if>
      <if test="firstShopGoodsQuantity != null">
        #{firstShopGoodsQuantity,jdbcType=VARCHAR},
      </if>
      <if test="checkAble != null">
        #{checkAble,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="createrOrgId != null">
        #{createrOrgId,jdbcType=BIGINT},
      </if>
      <if test="createrOrgName != null">
        #{createrOrgName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="updaterOrgId != null">
        #{updaterOrgId,jdbcType=BIGINT},
      </if>
      <if test="updaterOrgName != null">
        #{updaterOrgName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.ExecuteOrderGoodsExample" resultType="java.lang.Long">
    select count(*) from execute_order_goods
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update execute_order_goods
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.executeCode != null">
        execute_code = #{record.executeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.executeName != null">
        execute_name = #{record.executeName,jdbcType=VARCHAR},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseType != null">
        purchase_type = #{record.purchaseType,jdbcType=TINYINT},
      </if>
      <if test="record.goodsCode != null">
        goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCommonName != null">
        goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.approvalNumber != null">
        approval_number = #{record.approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.dosageForm != null">
        dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.billPrice != null">
        bill_price = #{record.billPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.rebatePrice != null">
        rebate_price = #{record.rebatePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.checkPrice != null">
        check_price = #{record.checkPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.retailPrice != null">
        retail_price = #{record.retailPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.pushLevel != null">
        push_level = #{record.pushLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.staticSynthesisGrossBate != null">
        static_synthesis_gross_bate = #{record.staticSynthesisGrossBate,jdbcType=DECIMAL},
      </if>
      <if test="record.inspectStore != null">
        inspect_store = #{record.inspectStore,jdbcType=VARCHAR},
      </if>
      <if test="record.firstShopGoodsQuantity != null">
        first_shop_goods_quantity = #{record.firstShopGoodsQuantity,jdbcType=VARCHAR},
      </if>
      <if test="record.checkAble != null">
        check_able = #{record.checkAble,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.createrOrgId != null">
        creater_org_id = #{record.createrOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.createrOrgName != null">
        creater_org_name = #{record.createrOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
      <if test="record.updaterOrgId != null">
        updater_org_id = #{record.updaterOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.updaterOrgName != null">
        updater_org_name = #{record.updaterOrgName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update execute_order_goods
    set id = #{record.id,jdbcType=INTEGER},
      execute_code = #{record.executeCode,jdbcType=VARCHAR},
      execute_name = #{record.executeName,jdbcType=VARCHAR},
      org_id = #{record.orgId,jdbcType=BIGINT},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      purchase_type = #{record.purchaseType,jdbcType=TINYINT},
      goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      specifications = #{record.specifications,jdbcType=VARCHAR},
      approval_number = #{record.approvalNumber,jdbcType=VARCHAR},
      dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      bill_price = #{record.billPrice,jdbcType=DECIMAL},
      rebate_price = #{record.rebatePrice,jdbcType=DECIMAL},
      check_price = #{record.checkPrice,jdbcType=DECIMAL},
      retail_price = #{record.retailPrice,jdbcType=DECIMAL},
      push_level = #{record.pushLevel,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      static_synthesis_gross_bate = #{record.staticSynthesisGrossBate,jdbcType=DECIMAL},
      inspect_store = #{record.inspectStore,jdbcType=VARCHAR},
      first_shop_goods_quantity = #{record.firstShopGoodsQuantity,jdbcType=VARCHAR},
      check_able = #{record.checkAble,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      creater_org_id = #{record.createrOrgId,jdbcType=BIGINT},
      creater_org_name = #{record.createrOrgName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR},
      updater_org_id = #{record.updaterOrgId,jdbcType=BIGINT},
      updater_org_name = #{record.updaterOrgName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.ExecuteOrderGoods">
    update execute_order_goods
    <set>
      <if test="executeCode != null">
        execute_code = #{executeCode,jdbcType=VARCHAR},
      </if>
      <if test="executeName != null">
        execute_name = #{executeName,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="purchaseType != null">
        purchase_type = #{purchaseType,jdbcType=TINYINT},
      </if>
      <if test="goodsCode != null">
        goods_code = #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="approvalNumber != null">
        approval_number = #{approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        dosage_form = #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="billPrice != null">
        bill_price = #{billPrice,jdbcType=DECIMAL},
      </if>
      <if test="rebatePrice != null">
        rebate_price = #{rebatePrice,jdbcType=DECIMAL},
      </if>
      <if test="checkPrice != null">
        check_price = #{checkPrice,jdbcType=DECIMAL},
      </if>
      <if test="retailPrice != null">
        retail_price = #{retailPrice,jdbcType=DECIMAL},
      </if>
      <if test="pushLevel != null">
        push_level = #{pushLevel,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="staticSynthesisGrossBate != null">
        static_synthesis_gross_bate = #{staticSynthesisGrossBate,jdbcType=DECIMAL},
      </if>
      <if test="inspectStore != null">
        inspect_store = #{inspectStore,jdbcType=VARCHAR},
      </if>
      <if test="firstShopGoodsQuantity != null">
        first_shop_goods_quantity = #{firstShopGoodsQuantity,jdbcType=VARCHAR},
      </if>
      <if test="checkAble != null">
        check_able = #{checkAble,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="createrOrgId != null">
        creater_org_id = #{createrOrgId,jdbcType=BIGINT},
      </if>
      <if test="createrOrgName != null">
        creater_org_name = #{createrOrgName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="updaterOrgId != null">
        updater_org_id = #{updaterOrgId,jdbcType=BIGINT},
      </if>
      <if test="updaterOrgName != null">
        updater_org_name = #{updaterOrgName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.ExecuteOrderGoods">
    update execute_order_goods
    set execute_code = #{executeCode,jdbcType=VARCHAR},
      execute_name = #{executeName,jdbcType=VARCHAR},
      org_id = #{orgId,jdbcType=BIGINT},
      org_name = #{orgName,jdbcType=VARCHAR},
      purchase_type = #{purchaseType,jdbcType=TINYINT},
      goods_code = #{goodsCode,jdbcType=VARCHAR},
      bar_code = #{barCode,jdbcType=VARCHAR},
      goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      specifications = #{specifications,jdbcType=VARCHAR},
      approval_number = #{approvalNumber,jdbcType=VARCHAR},
      dosage_form = #{dosageForm,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      bill_price = #{billPrice,jdbcType=DECIMAL},
      rebate_price = #{rebatePrice,jdbcType=DECIMAL},
      check_price = #{checkPrice,jdbcType=DECIMAL},
      retail_price = #{retailPrice,jdbcType=DECIMAL},
      push_level = #{pushLevel,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      static_synthesis_gross_bate = #{staticSynthesisGrossBate,jdbcType=DECIMAL},
      inspect_store = #{inspectStore,jdbcType=VARCHAR},
      first_shop_goods_quantity = #{firstShopGoodsQuantity,jdbcType=VARCHAR},
      check_able = #{checkAble,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      creater_org_id = #{createrOrgId,jdbcType=BIGINT},
      creater_org_name = #{createrOrgName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR},
      updater_org_id = #{updaterOrgId,jdbcType=BIGINT},
      updater_org_name = #{updaterOrgName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>