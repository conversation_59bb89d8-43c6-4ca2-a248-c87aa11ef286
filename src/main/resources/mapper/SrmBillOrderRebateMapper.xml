<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmBillOrderRebateMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmBillOrderRebate">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="bill_order_no" jdbcType="VARCHAR" property="billOrderNo" />
    <result column="rebate_no" jdbcType="VARCHAR" property="rebateNo" />
    <result column="rebate_total_amount" jdbcType="DECIMAL" property="rebateTotalAmount" />
    <result column="may_deduct_from_amount" jdbcType="DECIMAL" property="mayDeductFromAmount" />
    <result column="deduct_from_amount" jdbcType="DECIMAL" property="deductFromAmount" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, bill_order_no, rebate_no, rebate_total_amount, may_deduct_from_amount, deduct_from_amount, 
    gmt_create
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmBillOrderRebateExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_bill_order_rebate
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_bill_order_rebate
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from srm_bill_order_rebate
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmBillOrderRebateExample">
    delete from srm_bill_order_rebate
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmBillOrderRebate">
    insert into srm_bill_order_rebate (id, bill_order_no, rebate_no, 
      rebate_total_amount, may_deduct_from_amount, 
      deduct_from_amount, gmt_create)
    values (#{id,jdbcType=INTEGER}, #{billOrderNo,jdbcType=VARCHAR}, #{rebateNo,jdbcType=VARCHAR}, 
      #{rebateTotalAmount,jdbcType=DECIMAL}, #{mayDeductFromAmount,jdbcType=DECIMAL}, 
      #{deductFromAmount,jdbcType=DECIMAL}, #{gmtCreate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmBillOrderRebate">
    insert into srm_bill_order_rebate
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="billOrderNo != null">
        bill_order_no,
      </if>
      <if test="rebateNo != null">
        rebate_no,
      </if>
      <if test="rebateTotalAmount != null">
        rebate_total_amount,
      </if>
      <if test="mayDeductFromAmount != null">
        may_deduct_from_amount,
      </if>
      <if test="deductFromAmount != null">
        deduct_from_amount,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="billOrderNo != null">
        #{billOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="rebateNo != null">
        #{rebateNo,jdbcType=VARCHAR},
      </if>
      <if test="rebateTotalAmount != null">
        #{rebateTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="mayDeductFromAmount != null">
        #{mayDeductFromAmount,jdbcType=DECIMAL},
      </if>
      <if test="deductFromAmount != null">
        #{deductFromAmount,jdbcType=DECIMAL},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmBillOrderRebateExample" resultType="java.lang.Long">
    select count(*) from srm_bill_order_rebate
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_bill_order_rebate
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.billOrderNo != null">
        bill_order_no = #{record.billOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.rebateNo != null">
        rebate_no = #{record.rebateNo,jdbcType=VARCHAR},
      </if>
      <if test="record.rebateTotalAmount != null">
        rebate_total_amount = #{record.rebateTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.mayDeductFromAmount != null">
        may_deduct_from_amount = #{record.mayDeductFromAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.deductFromAmount != null">
        deduct_from_amount = #{record.deductFromAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_bill_order_rebate
    set id = #{record.id,jdbcType=INTEGER},
      bill_order_no = #{record.billOrderNo,jdbcType=VARCHAR},
      rebate_no = #{record.rebateNo,jdbcType=VARCHAR},
      rebate_total_amount = #{record.rebateTotalAmount,jdbcType=DECIMAL},
      may_deduct_from_amount = #{record.mayDeductFromAmount,jdbcType=DECIMAL},
      deduct_from_amount = #{record.deductFromAmount,jdbcType=DECIMAL},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmBillOrderRebate">
    update srm_bill_order_rebate
    <set>
      <if test="billOrderNo != null">
        bill_order_no = #{billOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="rebateNo != null">
        rebate_no = #{rebateNo,jdbcType=VARCHAR},
      </if>
      <if test="rebateTotalAmount != null">
        rebate_total_amount = #{rebateTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="mayDeductFromAmount != null">
        may_deduct_from_amount = #{mayDeductFromAmount,jdbcType=DECIMAL},
      </if>
      <if test="deductFromAmount != null">
        deduct_from_amount = #{deductFromAmount,jdbcType=DECIMAL},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmBillOrderRebate">
    update srm_bill_order_rebate
    set bill_order_no = #{billOrderNo,jdbcType=VARCHAR},
      rebate_no = #{rebateNo,jdbcType=VARCHAR},
      rebate_total_amount = #{rebateTotalAmount,jdbcType=DECIMAL},
      may_deduct_from_amount = #{mayDeductFromAmount,jdbcType=DECIMAL},
      deduct_from_amount = #{deductFromAmount,jdbcType=DECIMAL},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>