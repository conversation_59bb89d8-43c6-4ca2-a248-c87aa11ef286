<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmPayPurchaseOrderMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmPayPurchaseOrder">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="pay_purchase_order_no" jdbcType="VARCHAR" property="payPurchaseOrderNo" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="bill_to_party" jdbcType="VARCHAR" property="billToParty" />
    <result column="pay_amount" jdbcType="DECIMAL" property="payAmount" />
    <result column="rebate_amount" jdbcType="DECIMAL" property="rebateAmount" />
    <result column="order_type" jdbcType="VARCHAR" property="orderType" />
    <result column="cost_type" jdbcType="VARCHAR" property="costType" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="pay_way" jdbcType="VARCHAR" property="payWay" />
    <result column="pay_condition" jdbcType="VARCHAR" property="payCondition" />
    <result column="bank_no" jdbcType="VARCHAR" property="bankNo" />
    <result column="bank_account" jdbcType="VARCHAR" property="bankAccount" />
    <result column="pay_certificates_no" jdbcType="VARCHAR" property="payCertificatesNo" />
    <result column="pay_certificates_year" jdbcType="VARCHAR" property="payCertificatesYear" />
    <result column="pay_delete_id" jdbcType="VARCHAR" property="payDeleteId" />
    <result column="pay_status" jdbcType="VARCHAR" property="payStatus" />
    <result column="cost_center" jdbcType="VARCHAR" property="costCenter" />
    <result column="environment" jdbcType="VARCHAR" property="environment" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, pay_purchase_order_no, company_code, org_id, org_name, supplier_code, supplier_name, 
    bill_to_party, pay_amount, rebate_amount, order_type, cost_type, create_date, create_time, 
    create_name, pay_way, pay_condition, bank_no, bank_account, pay_certificates_no, 
    pay_certificates_year, pay_delete_id, pay_status, cost_center, environment, gmt_create, 
    gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmPayPurchaseOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_pay_purchase_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_pay_purchase_order
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from srm_pay_purchase_order
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmPayPurchaseOrderExample">
    delete from srm_pay_purchase_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmPayPurchaseOrder">
    insert into srm_pay_purchase_order (id, pay_purchase_order_no, company_code, 
      org_id, org_name, supplier_code, 
      supplier_name, bill_to_party, pay_amount, 
      rebate_amount, order_type, cost_type, 
      create_date, create_time, create_name, 
      pay_way, pay_condition, bank_no, 
      bank_account, pay_certificates_no, pay_certificates_year, 
      pay_delete_id, pay_status, cost_center, 
      environment, gmt_create, gmt_update
      )
    values (#{id,jdbcType=INTEGER}, #{payPurchaseOrderNo,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR}, 
      #{orgId,jdbcType=BIGINT}, #{orgName,jdbcType=VARCHAR}, #{supplierCode,jdbcType=VARCHAR}, 
      #{supplierName,jdbcType=VARCHAR}, #{billToParty,jdbcType=VARCHAR}, #{payAmount,jdbcType=DECIMAL}, 
      #{rebateAmount,jdbcType=DECIMAL}, #{orderType,jdbcType=VARCHAR}, #{costType,jdbcType=VARCHAR}, 
      #{createDate,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{createName,jdbcType=VARCHAR}, 
      #{payWay,jdbcType=VARCHAR}, #{payCondition,jdbcType=VARCHAR}, #{bankNo,jdbcType=VARCHAR}, 
      #{bankAccount,jdbcType=VARCHAR}, #{payCertificatesNo,jdbcType=VARCHAR}, #{payCertificatesYear,jdbcType=VARCHAR}, 
      #{payDeleteId,jdbcType=VARCHAR}, #{payStatus,jdbcType=VARCHAR}, #{costCenter,jdbcType=VARCHAR}, 
      #{environment,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmPayPurchaseOrder">
    insert into srm_pay_purchase_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="payPurchaseOrderNo != null">
        pay_purchase_order_no,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="supplierCode != null">
        supplier_code,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="billToParty != null">
        bill_to_party,
      </if>
      <if test="payAmount != null">
        pay_amount,
      </if>
      <if test="rebateAmount != null">
        rebate_amount,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="costType != null">
        cost_type,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="payWay != null">
        pay_way,
      </if>
      <if test="payCondition != null">
        pay_condition,
      </if>
      <if test="bankNo != null">
        bank_no,
      </if>
      <if test="bankAccount != null">
        bank_account,
      </if>
      <if test="payCertificatesNo != null">
        pay_certificates_no,
      </if>
      <if test="payCertificatesYear != null">
        pay_certificates_year,
      </if>
      <if test="payDeleteId != null">
        pay_delete_id,
      </if>
      <if test="payStatus != null">
        pay_status,
      </if>
      <if test="costCenter != null">
        cost_center,
      </if>
      <if test="environment != null">
        environment,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="payPurchaseOrderNo != null">
        #{payPurchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="supplierCode != null">
        #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="billToParty != null">
        #{billToParty,jdbcType=VARCHAR},
      </if>
      <if test="payAmount != null">
        #{payAmount,jdbcType=DECIMAL},
      </if>
      <if test="rebateAmount != null">
        #{rebateAmount,jdbcType=DECIMAL},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="costType != null">
        #{costType,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="payWay != null">
        #{payWay,jdbcType=VARCHAR},
      </if>
      <if test="payCondition != null">
        #{payCondition,jdbcType=VARCHAR},
      </if>
      <if test="bankNo != null">
        #{bankNo,jdbcType=VARCHAR},
      </if>
      <if test="bankAccount != null">
        #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="payCertificatesNo != null">
        #{payCertificatesNo,jdbcType=VARCHAR},
      </if>
      <if test="payCertificatesYear != null">
        #{payCertificatesYear,jdbcType=VARCHAR},
      </if>
      <if test="payDeleteId != null">
        #{payDeleteId,jdbcType=VARCHAR},
      </if>
      <if test="payStatus != null">
        #{payStatus,jdbcType=VARCHAR},
      </if>
      <if test="costCenter != null">
        #{costCenter,jdbcType=VARCHAR},
      </if>
      <if test="environment != null">
        #{environment,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmPayPurchaseOrderExample" resultType="java.lang.Long">
    select count(*) from srm_pay_purchase_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_pay_purchase_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.payPurchaseOrderNo != null">
        pay_purchase_order_no = #{record.payPurchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierCode != null">
        supplier_code = #{record.supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierName != null">
        supplier_name = #{record.supplierName,jdbcType=VARCHAR},
      </if>
      <if test="record.billToParty != null">
        bill_to_party = #{record.billToParty,jdbcType=VARCHAR},
      </if>
      <if test="record.payAmount != null">
        pay_amount = #{record.payAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.rebateAmount != null">
        rebate_amount = #{record.rebateAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=VARCHAR},
      </if>
      <if test="record.costType != null">
        cost_type = #{record.costType,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null">
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createName != null">
        create_name = #{record.createName,jdbcType=VARCHAR},
      </if>
      <if test="record.payWay != null">
        pay_way = #{record.payWay,jdbcType=VARCHAR},
      </if>
      <if test="record.payCondition != null">
        pay_condition = #{record.payCondition,jdbcType=VARCHAR},
      </if>
      <if test="record.bankNo != null">
        bank_no = #{record.bankNo,jdbcType=VARCHAR},
      </if>
      <if test="record.bankAccount != null">
        bank_account = #{record.bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.payCertificatesNo != null">
        pay_certificates_no = #{record.payCertificatesNo,jdbcType=VARCHAR},
      </if>
      <if test="record.payCertificatesYear != null">
        pay_certificates_year = #{record.payCertificatesYear,jdbcType=VARCHAR},
      </if>
      <if test="record.payDeleteId != null">
        pay_delete_id = #{record.payDeleteId,jdbcType=VARCHAR},
      </if>
      <if test="record.payStatus != null">
        pay_status = #{record.payStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.costCenter != null">
        cost_center = #{record.costCenter,jdbcType=VARCHAR},
      </if>
      <if test="record.environment != null">
        environment = #{record.environment,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_pay_purchase_order
    set id = #{record.id,jdbcType=INTEGER},
      pay_purchase_order_no = #{record.payPurchaseOrderNo,jdbcType=VARCHAR},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      org_id = #{record.orgId,jdbcType=BIGINT},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      supplier_code = #{record.supplierCode,jdbcType=VARCHAR},
      supplier_name = #{record.supplierName,jdbcType=VARCHAR},
      bill_to_party = #{record.billToParty,jdbcType=VARCHAR},
      pay_amount = #{record.payAmount,jdbcType=DECIMAL},
      rebate_amount = #{record.rebateAmount,jdbcType=DECIMAL},
      order_type = #{record.orderType,jdbcType=VARCHAR},
      cost_type = #{record.costType,jdbcType=VARCHAR},
      create_date = #{record.createDate,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_name = #{record.createName,jdbcType=VARCHAR},
      pay_way = #{record.payWay,jdbcType=VARCHAR},
      pay_condition = #{record.payCondition,jdbcType=VARCHAR},
      bank_no = #{record.bankNo,jdbcType=VARCHAR},
      bank_account = #{record.bankAccount,jdbcType=VARCHAR},
      pay_certificates_no = #{record.payCertificatesNo,jdbcType=VARCHAR},
      pay_certificates_year = #{record.payCertificatesYear,jdbcType=VARCHAR},
      pay_delete_id = #{record.payDeleteId,jdbcType=VARCHAR},
      pay_status = #{record.payStatus,jdbcType=VARCHAR},
      cost_center = #{record.costCenter,jdbcType=VARCHAR},
      environment = #{record.environment,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmPayPurchaseOrder">
    update srm_pay_purchase_order
    <set>
      <if test="payPurchaseOrderNo != null">
        pay_purchase_order_no = #{payPurchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="supplierCode != null">
        supplier_code = #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="billToParty != null">
        bill_to_party = #{billToParty,jdbcType=VARCHAR},
      </if>
      <if test="payAmount != null">
        pay_amount = #{payAmount,jdbcType=DECIMAL},
      </if>
      <if test="rebateAmount != null">
        rebate_amount = #{rebateAmount,jdbcType=DECIMAL},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="costType != null">
        cost_type = #{costType,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="payWay != null">
        pay_way = #{payWay,jdbcType=VARCHAR},
      </if>
      <if test="payCondition != null">
        pay_condition = #{payCondition,jdbcType=VARCHAR},
      </if>
      <if test="bankNo != null">
        bank_no = #{bankNo,jdbcType=VARCHAR},
      </if>
      <if test="bankAccount != null">
        bank_account = #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="payCertificatesNo != null">
        pay_certificates_no = #{payCertificatesNo,jdbcType=VARCHAR},
      </if>
      <if test="payCertificatesYear != null">
        pay_certificates_year = #{payCertificatesYear,jdbcType=VARCHAR},
      </if>
      <if test="payDeleteId != null">
        pay_delete_id = #{payDeleteId,jdbcType=VARCHAR},
      </if>
      <if test="payStatus != null">
        pay_status = #{payStatus,jdbcType=VARCHAR},
      </if>
      <if test="costCenter != null">
        cost_center = #{costCenter,jdbcType=VARCHAR},
      </if>
      <if test="environment != null">
        environment = #{environment,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmPayPurchaseOrder">
    update srm_pay_purchase_order
    set pay_purchase_order_no = #{payPurchaseOrderNo,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      org_id = #{orgId,jdbcType=BIGINT},
      org_name = #{orgName,jdbcType=VARCHAR},
      supplier_code = #{supplierCode,jdbcType=VARCHAR},
      supplier_name = #{supplierName,jdbcType=VARCHAR},
      bill_to_party = #{billToParty,jdbcType=VARCHAR},
      pay_amount = #{payAmount,jdbcType=DECIMAL},
      rebate_amount = #{rebateAmount,jdbcType=DECIMAL},
      order_type = #{orderType,jdbcType=VARCHAR},
      cost_type = #{costType,jdbcType=VARCHAR},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_name = #{createName,jdbcType=VARCHAR},
      pay_way = #{payWay,jdbcType=VARCHAR},
      pay_condition = #{payCondition,jdbcType=VARCHAR},
      bank_no = #{bankNo,jdbcType=VARCHAR},
      bank_account = #{bankAccount,jdbcType=VARCHAR},
      pay_certificates_no = #{payCertificatesNo,jdbcType=VARCHAR},
      pay_certificates_year = #{payCertificatesYear,jdbcType=VARCHAR},
      pay_delete_id = #{payDeleteId,jdbcType=VARCHAR},
      pay_status = #{payStatus,jdbcType=VARCHAR},
      cost_center = #{costCenter,jdbcType=VARCHAR},
      environment = #{environment,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>