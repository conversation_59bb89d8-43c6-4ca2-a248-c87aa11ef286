<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmInnerPurchaseOrderDetailsMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmInnerPurchaseOrderDetails">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="purchase_order_no" jdbcType="VARCHAR" property="purchaseOrderNo" />
    <result column="purchase_order_id" jdbcType="VARCHAR" property="purchaseOrderId" />
    <result column="order_details_key" jdbcType="VARCHAR" property="orderDetailsKey" />
    <result column="purchase_delete_id" jdbcType="VARCHAR" property="purchaseDeleteId" />
    <result column="confirm_status" jdbcType="INTEGER" property="confirmStatus" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="supplier_warehouse_addr" jdbcType="VARCHAR" property="supplierWarehouseAddr" />
    <result column="expect_date" jdbcType="TIMESTAMP" property="expectDate" />
    <result column="purchase_count" jdbcType="DECIMAL" property="purchaseCount" />
    <result column="can_send_quantity" jdbcType="DECIMAL" property="canSendQuantity" />
    <result column="this_send_quantity" jdbcType="DECIMAL" property="thisSendQuantity" />
    <result column="purchase_unit" jdbcType="VARCHAR" property="purchaseUnit" />
    <result column="no_tax_price" jdbcType="DECIMAL" property="noTaxPrice" />
    <result column="have_tax_price" jdbcType="DECIMAL" property="haveTaxPrice" />
    <result column="price_unit" jdbcType="VARCHAR" property="priceUnit" />
    <result column="no_tax_amount" jdbcType="DECIMAL" property="noTaxAmount" />
    <result column="have_tax_amount" jdbcType="DECIMAL" property="haveTaxAmount" />
    <result column="tax_code" jdbcType="VARCHAR" property="taxCode" />
    <result column="tax_rate" jdbcType="VARCHAR" property="taxRate" />
    <result column="deliver_quantity" jdbcType="DECIMAL" property="deliverQuantity" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, purchase_order_no, purchase_order_id, order_details_key, purchase_delete_id, 
    confirm_status, goods_code, supplier_warehouse_addr, expect_date, purchase_count, 
    can_send_quantity, this_send_quantity, purchase_unit, no_tax_price, have_tax_price, 
    price_unit, no_tax_amount, have_tax_amount, tax_code, tax_rate, deliver_quantity, 
    gmt_create, gmt_update, `status`, extend, version, created_by, created_name, updated_by, 
    updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrderDetailsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_inner_purchase_order_details
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_inner_purchase_order_details
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from srm_inner_purchase_order_details
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrderDetailsExample">
    delete from srm_inner_purchase_order_details
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrderDetails">
    insert into srm_inner_purchase_order_details (id, purchase_order_no, purchase_order_id, 
      order_details_key, purchase_delete_id, confirm_status, 
      goods_code, supplier_warehouse_addr, expect_date, 
      purchase_count, can_send_quantity, this_send_quantity, 
      purchase_unit, no_tax_price, have_tax_price, 
      price_unit, no_tax_amount, have_tax_amount, 
      tax_code, tax_rate, deliver_quantity, 
      gmt_create, gmt_update, `status`, 
      extend, version, created_by, 
      created_name, updated_by, updated_name
      )
    values (#{id,jdbcType=BIGINT}, #{purchaseOrderNo,jdbcType=VARCHAR}, #{purchaseOrderId,jdbcType=VARCHAR}, 
      #{orderDetailsKey,jdbcType=VARCHAR}, #{purchaseDeleteId,jdbcType=VARCHAR}, #{confirmStatus,jdbcType=INTEGER}, 
      #{goodsCode,jdbcType=VARCHAR}, #{supplierWarehouseAddr,jdbcType=VARCHAR}, #{expectDate,jdbcType=TIMESTAMP}, 
      #{purchaseCount,jdbcType=DECIMAL}, #{canSendQuantity,jdbcType=DECIMAL}, #{thisSendQuantity,jdbcType=DECIMAL}, 
      #{purchaseUnit,jdbcType=VARCHAR}, #{noTaxPrice,jdbcType=DECIMAL}, #{haveTaxPrice,jdbcType=DECIMAL}, 
      #{priceUnit,jdbcType=VARCHAR}, #{noTaxAmount,jdbcType=DECIMAL}, #{haveTaxAmount,jdbcType=DECIMAL}, 
      #{taxCode,jdbcType=VARCHAR}, #{taxRate,jdbcType=VARCHAR}, #{deliverQuantity,jdbcType=DECIMAL}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}, 
      #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, 
      #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrderDetails">
    insert into srm_inner_purchase_order_details
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="purchaseOrderNo != null">
        purchase_order_no,
      </if>
      <if test="purchaseOrderId != null">
        purchase_order_id,
      </if>
      <if test="orderDetailsKey != null">
        order_details_key,
      </if>
      <if test="purchaseDeleteId != null">
        purchase_delete_id,
      </if>
      <if test="confirmStatus != null">
        confirm_status,
      </if>
      <if test="goodsCode != null">
        goods_code,
      </if>
      <if test="supplierWarehouseAddr != null">
        supplier_warehouse_addr,
      </if>
      <if test="expectDate != null">
        expect_date,
      </if>
      <if test="purchaseCount != null">
        purchase_count,
      </if>
      <if test="canSendQuantity != null">
        can_send_quantity,
      </if>
      <if test="thisSendQuantity != null">
        this_send_quantity,
      </if>
      <if test="purchaseUnit != null">
        purchase_unit,
      </if>
      <if test="noTaxPrice != null">
        no_tax_price,
      </if>
      <if test="haveTaxPrice != null">
        have_tax_price,
      </if>
      <if test="priceUnit != null">
        price_unit,
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount,
      </if>
      <if test="haveTaxAmount != null">
        have_tax_amount,
      </if>
      <if test="taxCode != null">
        tax_code,
      </if>
      <if test="taxRate != null">
        tax_rate,
      </if>
      <if test="deliverQuantity != null">
        deliver_quantity,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="purchaseOrderNo != null">
        #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderId != null">
        #{purchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderDetailsKey != null">
        #{orderDetailsKey,jdbcType=VARCHAR},
      </if>
      <if test="purchaseDeleteId != null">
        #{purchaseDeleteId,jdbcType=VARCHAR},
      </if>
      <if test="confirmStatus != null">
        #{confirmStatus,jdbcType=INTEGER},
      </if>
      <if test="goodsCode != null">
        #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierWarehouseAddr != null">
        #{supplierWarehouseAddr,jdbcType=VARCHAR},
      </if>
      <if test="expectDate != null">
        #{expectDate,jdbcType=TIMESTAMP},
      </if>
      <if test="purchaseCount != null">
        #{purchaseCount,jdbcType=DECIMAL},
      </if>
      <if test="canSendQuantity != null">
        #{canSendQuantity,jdbcType=DECIMAL},
      </if>
      <if test="thisSendQuantity != null">
        #{thisSendQuantity,jdbcType=DECIMAL},
      </if>
      <if test="purchaseUnit != null">
        #{purchaseUnit,jdbcType=VARCHAR},
      </if>
      <if test="noTaxPrice != null">
        #{noTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="haveTaxPrice != null">
        #{haveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="priceUnit != null">
        #{priceUnit,jdbcType=VARCHAR},
      </if>
      <if test="noTaxAmount != null">
        #{noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="haveTaxAmount != null">
        #{haveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxCode != null">
        #{taxCode,jdbcType=VARCHAR},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=VARCHAR},
      </if>
      <if test="deliverQuantity != null">
        #{deliverQuantity,jdbcType=DECIMAL},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrderDetailsExample" resultType="java.lang.Long">
    select count(*) from srm_inner_purchase_order_details
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_inner_purchase_order_details
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.purchaseOrderNo != null">
        purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrderId != null">
        purchase_order_id = #{record.purchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderDetailsKey != null">
        order_details_key = #{record.orderDetailsKey,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseDeleteId != null">
        purchase_delete_id = #{record.purchaseDeleteId,jdbcType=VARCHAR},
      </if>
      <if test="record.confirmStatus != null">
        confirm_status = #{record.confirmStatus,jdbcType=INTEGER},
      </if>
      <if test="record.goodsCode != null">
        goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierWarehouseAddr != null">
        supplier_warehouse_addr = #{record.supplierWarehouseAddr,jdbcType=VARCHAR},
      </if>
      <if test="record.expectDate != null">
        expect_date = #{record.expectDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.purchaseCount != null">
        purchase_count = #{record.purchaseCount,jdbcType=DECIMAL},
      </if>
      <if test="record.canSendQuantity != null">
        can_send_quantity = #{record.canSendQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.thisSendQuantity != null">
        this_send_quantity = #{record.thisSendQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.purchaseUnit != null">
        purchase_unit = #{record.purchaseUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.noTaxPrice != null">
        no_tax_price = #{record.noTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.haveTaxPrice != null">
        have_tax_price = #{record.haveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.priceUnit != null">
        price_unit = #{record.priceUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.noTaxAmount != null">
        no_tax_amount = #{record.noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.haveTaxAmount != null">
        have_tax_amount = #{record.haveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.taxCode != null">
        tax_code = #{record.taxCode,jdbcType=VARCHAR},
      </if>
      <if test="record.taxRate != null">
        tax_rate = #{record.taxRate,jdbcType=VARCHAR},
      </if>
      <if test="record.deliverQuantity != null">
        deliver_quantity = #{record.deliverQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_inner_purchase_order_details
    set id = #{record.id,jdbcType=BIGINT},
      purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      purchase_order_id = #{record.purchaseOrderId,jdbcType=VARCHAR},
      order_details_key = #{record.orderDetailsKey,jdbcType=VARCHAR},
      purchase_delete_id = #{record.purchaseDeleteId,jdbcType=VARCHAR},
      confirm_status = #{record.confirmStatus,jdbcType=INTEGER},
      goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      supplier_warehouse_addr = #{record.supplierWarehouseAddr,jdbcType=VARCHAR},
      expect_date = #{record.expectDate,jdbcType=TIMESTAMP},
      purchase_count = #{record.purchaseCount,jdbcType=DECIMAL},
      can_send_quantity = #{record.canSendQuantity,jdbcType=DECIMAL},
      this_send_quantity = #{record.thisSendQuantity,jdbcType=DECIMAL},
      purchase_unit = #{record.purchaseUnit,jdbcType=VARCHAR},
      no_tax_price = #{record.noTaxPrice,jdbcType=DECIMAL},
      have_tax_price = #{record.haveTaxPrice,jdbcType=DECIMAL},
      price_unit = #{record.priceUnit,jdbcType=VARCHAR},
      no_tax_amount = #{record.noTaxAmount,jdbcType=DECIMAL},
      have_tax_amount = #{record.haveTaxAmount,jdbcType=DECIMAL},
      tax_code = #{record.taxCode,jdbcType=VARCHAR},
      tax_rate = #{record.taxRate,jdbcType=VARCHAR},
      deliver_quantity = #{record.deliverQuantity,jdbcType=DECIMAL},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      `status` = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrderDetails">
    update srm_inner_purchase_order_details
    <set>
      <if test="purchaseOrderNo != null">
        purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderId != null">
        purchase_order_id = #{purchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderDetailsKey != null">
        order_details_key = #{orderDetailsKey,jdbcType=VARCHAR},
      </if>
      <if test="purchaseDeleteId != null">
        purchase_delete_id = #{purchaseDeleteId,jdbcType=VARCHAR},
      </if>
      <if test="confirmStatus != null">
        confirm_status = #{confirmStatus,jdbcType=INTEGER},
      </if>
      <if test="goodsCode != null">
        goods_code = #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierWarehouseAddr != null">
        supplier_warehouse_addr = #{supplierWarehouseAddr,jdbcType=VARCHAR},
      </if>
      <if test="expectDate != null">
        expect_date = #{expectDate,jdbcType=TIMESTAMP},
      </if>
      <if test="purchaseCount != null">
        purchase_count = #{purchaseCount,jdbcType=DECIMAL},
      </if>
      <if test="canSendQuantity != null">
        can_send_quantity = #{canSendQuantity,jdbcType=DECIMAL},
      </if>
      <if test="thisSendQuantity != null">
        this_send_quantity = #{thisSendQuantity,jdbcType=DECIMAL},
      </if>
      <if test="purchaseUnit != null">
        purchase_unit = #{purchaseUnit,jdbcType=VARCHAR},
      </if>
      <if test="noTaxPrice != null">
        no_tax_price = #{noTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="haveTaxPrice != null">
        have_tax_price = #{haveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="priceUnit != null">
        price_unit = #{priceUnit,jdbcType=VARCHAR},
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount = #{noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="haveTaxAmount != null">
        have_tax_amount = #{haveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxCode != null">
        tax_code = #{taxCode,jdbcType=VARCHAR},
      </if>
      <if test="taxRate != null">
        tax_rate = #{taxRate,jdbcType=VARCHAR},
      </if>
      <if test="deliverQuantity != null">
        deliver_quantity = #{deliverQuantity,jdbcType=DECIMAL},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrderDetails">
    update srm_inner_purchase_order_details
    set purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      purchase_order_id = #{purchaseOrderId,jdbcType=VARCHAR},
      order_details_key = #{orderDetailsKey,jdbcType=VARCHAR},
      purchase_delete_id = #{purchaseDeleteId,jdbcType=VARCHAR},
      confirm_status = #{confirmStatus,jdbcType=INTEGER},
      goods_code = #{goodsCode,jdbcType=VARCHAR},
      supplier_warehouse_addr = #{supplierWarehouseAddr,jdbcType=VARCHAR},
      expect_date = #{expectDate,jdbcType=TIMESTAMP},
      purchase_count = #{purchaseCount,jdbcType=DECIMAL},
      can_send_quantity = #{canSendQuantity,jdbcType=DECIMAL},
      this_send_quantity = #{thisSendQuantity,jdbcType=DECIMAL},
      purchase_unit = #{purchaseUnit,jdbcType=VARCHAR},
      no_tax_price = #{noTaxPrice,jdbcType=DECIMAL},
      have_tax_price = #{haveTaxPrice,jdbcType=DECIMAL},
      price_unit = #{priceUnit,jdbcType=VARCHAR},
      no_tax_amount = #{noTaxAmount,jdbcType=DECIMAL},
      have_tax_amount = #{haveTaxAmount,jdbcType=DECIMAL},
      tax_code = #{taxCode,jdbcType=VARCHAR},
      tax_rate = #{taxRate,jdbcType=VARCHAR},
      deliver_quantity = #{deliverQuantity,jdbcType=DECIMAL},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>