<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmMedicineReportMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmMedicineReport">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="compny_code" jdbcType="VARCHAR" property="compnyCode" />
    <result column="supplier_no" jdbcType="VARCHAR" property="supplierNo" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="manu_code" jdbcType="VARCHAR" property="manuCode" />
    <result column="manu_name" jdbcType="VARCHAR" property="manuName" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="goods_desc" jdbcType="VARCHAR" property="goodsDesc" />
    <result column="supplier_goods_code" jdbcType="VARCHAR" property="supplierGoodsCode" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="validity_date" jdbcType="VARCHAR" property="validityDate" />
    <result column="produce_date" jdbcType="VARCHAR" property="produceDate" />
    <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber" />
    <result column="check_report" jdbcType="VARCHAR" property="checkReport" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="message" jdbcType="VARCHAR" property="message" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, compny_code, supplier_no, warehouse_code, manu_code, manu_name, goods_code, goods_desc, 
    supplier_goods_code, batch_no, validity_date, produce_date, approval_number, check_report, 
    `status`, message, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmMedicineReportExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_medicine_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_medicine_report
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from srm_medicine_report
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmMedicineReportExample">
    delete from srm_medicine_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmMedicineReport">
    insert into srm_medicine_report (id, compny_code, supplier_no, 
      warehouse_code, manu_code, manu_name, 
      goods_code, goods_desc, supplier_goods_code, 
      batch_no, validity_date, produce_date, 
      approval_number, check_report, `status`, 
      message, gmt_create, gmt_update
      )
    values (#{id,jdbcType=BIGINT}, #{compnyCode,jdbcType=VARCHAR}, #{supplierNo,jdbcType=VARCHAR}, 
      #{warehouseCode,jdbcType=VARCHAR}, #{manuCode,jdbcType=VARCHAR}, #{manuName,jdbcType=VARCHAR}, 
      #{goodsCode,jdbcType=VARCHAR}, #{goodsDesc,jdbcType=VARCHAR}, #{supplierGoodsCode,jdbcType=VARCHAR}, 
      #{batchNo,jdbcType=VARCHAR}, #{validityDate,jdbcType=VARCHAR}, #{produceDate,jdbcType=VARCHAR}, 
      #{approvalNumber,jdbcType=VARCHAR}, #{checkReport,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{message,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmMedicineReport">
    insert into srm_medicine_report
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="compnyCode != null">
        compny_code,
      </if>
      <if test="supplierNo != null">
        supplier_no,
      </if>
      <if test="warehouseCode != null">
        warehouse_code,
      </if>
      <if test="manuCode != null">
        manu_code,
      </if>
      <if test="manuName != null">
        manu_name,
      </if>
      <if test="goodsCode != null">
        goods_code,
      </if>
      <if test="goodsDesc != null">
        goods_desc,
      </if>
      <if test="supplierGoodsCode != null">
        supplier_goods_code,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="validityDate != null">
        validity_date,
      </if>
      <if test="produceDate != null">
        produce_date,
      </if>
      <if test="approvalNumber != null">
        approval_number,
      </if>
      <if test="checkReport != null">
        check_report,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="message != null">
        message,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="compnyCode != null">
        #{compnyCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null">
        #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="manuCode != null">
        #{manuCode,jdbcType=VARCHAR},
      </if>
      <if test="manuName != null">
        #{manuName,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsDesc != null">
        #{goodsDesc,jdbcType=VARCHAR},
      </if>
      <if test="supplierGoodsCode != null">
        #{supplierGoodsCode,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        #{validityDate,jdbcType=VARCHAR},
      </if>
      <if test="produceDate != null">
        #{produceDate,jdbcType=VARCHAR},
      </if>
      <if test="approvalNumber != null">
        #{approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="checkReport != null">
        #{checkReport,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="message != null">
        #{message,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmMedicineReportExample" resultType="java.lang.Long">
    select count(*) from srm_medicine_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_medicine_report
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.compnyCode != null">
        compny_code = #{record.compnyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierNo != null">
        supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseCode != null">
        warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.manuCode != null">
        manu_code = #{record.manuCode,jdbcType=VARCHAR},
      </if>
      <if test="record.manuName != null">
        manu_name = #{record.manuName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCode != null">
        goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsDesc != null">
        goods_desc = #{record.goodsDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierGoodsCode != null">
        supplier_goods_code = #{record.supplierGoodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.validityDate != null">
        validity_date = #{record.validityDate,jdbcType=VARCHAR},
      </if>
      <if test="record.produceDate != null">
        produce_date = #{record.produceDate,jdbcType=VARCHAR},
      </if>
      <if test="record.approvalNumber != null">
        approval_number = #{record.approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.checkReport != null">
        check_report = #{record.checkReport,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.message != null">
        message = #{record.message,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_medicine_report
    set id = #{record.id,jdbcType=BIGINT},
      compny_code = #{record.compnyCode,jdbcType=VARCHAR},
      supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      manu_code = #{record.manuCode,jdbcType=VARCHAR},
      manu_name = #{record.manuName,jdbcType=VARCHAR},
      goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      goods_desc = #{record.goodsDesc,jdbcType=VARCHAR},
      supplier_goods_code = #{record.supplierGoodsCode,jdbcType=VARCHAR},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      validity_date = #{record.validityDate,jdbcType=VARCHAR},
      produce_date = #{record.produceDate,jdbcType=VARCHAR},
      approval_number = #{record.approvalNumber,jdbcType=VARCHAR},
      check_report = #{record.checkReport,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=TINYINT},
      message = #{record.message,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmMedicineReport">
    update srm_medicine_report
    <set>
      <if test="compnyCode != null">
        compny_code = #{compnyCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null">
        supplier_no = #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="manuCode != null">
        manu_code = #{manuCode,jdbcType=VARCHAR},
      </if>
      <if test="manuName != null">
        manu_name = #{manuName,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        goods_code = #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsDesc != null">
        goods_desc = #{goodsDesc,jdbcType=VARCHAR},
      </if>
      <if test="supplierGoodsCode != null">
        supplier_goods_code = #{supplierGoodsCode,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        validity_date = #{validityDate,jdbcType=VARCHAR},
      </if>
      <if test="produceDate != null">
        produce_date = #{produceDate,jdbcType=VARCHAR},
      </if>
      <if test="approvalNumber != null">
        approval_number = #{approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="checkReport != null">
        check_report = #{checkReport,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="message != null">
        message = #{message,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmMedicineReport">
    update srm_medicine_report
    set compny_code = #{compnyCode,jdbcType=VARCHAR},
      supplier_no = #{supplierNo,jdbcType=VARCHAR},
      warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      manu_code = #{manuCode,jdbcType=VARCHAR},
      manu_name = #{manuName,jdbcType=VARCHAR},
      goods_code = #{goodsCode,jdbcType=VARCHAR},
      goods_desc = #{goodsDesc,jdbcType=VARCHAR},
      supplier_goods_code = #{supplierGoodsCode,jdbcType=VARCHAR},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      validity_date = #{validityDate,jdbcType=VARCHAR},
      produce_date = #{produceDate,jdbcType=VARCHAR},
      approval_number = #{approvalNumber,jdbcType=VARCHAR},
      check_report = #{checkReport,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      message = #{message,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>