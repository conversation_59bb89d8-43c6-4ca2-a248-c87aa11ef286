<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmEnquiryOrderDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmEnquiryOrderDetail">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="enquiry_order_no" jdbcType="VARCHAR" property="enquiryOrderNo" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="habitat" jdbcType="VARCHAR" property="habitat" />
    <result column="basic_util" jdbcType="VARCHAR" property="basicUtil" />
    <result column="apprdocno" jdbcType="VARCHAR" property="apprdocno" />
    <result column="purchase_supplier_no" jdbcType="VARCHAR" property="purchaseSupplierNo" />
    <result column="purchase_supplier_name" jdbcType="VARCHAR" property="purchaseSupplierName" />
    <result column="purchase_bid_price" jdbcType="DECIMAL" property="purchaseBidPrice" />
    <result column="recent_supplier_name" jdbcType="VARCHAR" property="recentSupplierName" />
    <result column="recent_purchase_price" jdbcType="DECIMAL" property="recentPurchasePrice" />
    <result column="lowest_price" jdbcType="DECIMAL" property="lowestPrice" />
    <result column="order_quantity" jdbcType="DECIMAL" property="orderQuantity" />
    <result column="supplier_no" jdbcType="VARCHAR" property="supplierNo" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="enquiry_price" jdbcType="DECIMAL" property="enquiryPrice" />
    <result column="enquiry_quantity" jdbcType="DECIMAL" property="enquiryQuantity" />
    <result column="enquiry_status" jdbcType="TINYINT" property="enquiryStatus" />
    <result column="comments" jdbcType="VARCHAR" property="comments" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="contract_type" jdbcType="VARCHAR" property="contractType" />
    <result column="contract_supplier" jdbcType="VARCHAR" property="contractSupplier" />
    <result column="line_id" jdbcType="INTEGER" property="lineId" />
    <result column="validperiod" jdbcType="INTEGER" property="validperiod" />
    <result column="periodunit" jdbcType="VARCHAR" property="periodunit" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, enquiry_order_no, goods_code, bar_code, goods_common_name, goods_name, specifications, 
    manufacturer, habitat, basic_util, apprdocno, purchase_supplier_no, purchase_supplier_name, 
    purchase_bid_price, recent_supplier_name, recent_purchase_price, lowest_price, order_quantity, 
    supplier_no, supplier_name, enquiry_price, enquiry_quantity, enquiry_status, comments, 
    gmt_create, gmt_update, contract_type, contract_supplier, line_id, validperiod, periodunit
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmEnquiryOrderDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_enquiry_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_enquiry_order_detail
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from srm_enquiry_order_detail
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmEnquiryOrderDetailExample">
    delete from srm_enquiry_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmEnquiryOrderDetail">
    insert into srm_enquiry_order_detail (id, enquiry_order_no, goods_code, 
      bar_code, goods_common_name, goods_name, 
      specifications, manufacturer, habitat, 
      basic_util, apprdocno, purchase_supplier_no, 
      purchase_supplier_name, purchase_bid_price, 
      recent_supplier_name, recent_purchase_price, 
      lowest_price, order_quantity, supplier_no, 
      supplier_name, enquiry_price, enquiry_quantity, 
      enquiry_status, comments, gmt_create, 
      gmt_update, contract_type, contract_supplier, 
      line_id, validperiod, periodunit
      )
    values (#{id,jdbcType=INTEGER}, #{enquiryOrderNo,jdbcType=VARCHAR}, #{goodsCode,jdbcType=VARCHAR}, 
      #{barCode,jdbcType=VARCHAR}, #{goodsCommonName,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, 
      #{specifications,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, #{habitat,jdbcType=VARCHAR}, 
      #{basicUtil,jdbcType=VARCHAR}, #{apprdocno,jdbcType=VARCHAR}, #{purchaseSupplierNo,jdbcType=VARCHAR}, 
      #{purchaseSupplierName,jdbcType=VARCHAR}, #{purchaseBidPrice,jdbcType=DECIMAL}, 
      #{recentSupplierName,jdbcType=VARCHAR}, #{recentPurchasePrice,jdbcType=DECIMAL}, 
      #{lowestPrice,jdbcType=DECIMAL}, #{orderQuantity,jdbcType=DECIMAL}, #{supplierNo,jdbcType=VARCHAR}, 
      #{supplierName,jdbcType=VARCHAR}, #{enquiryPrice,jdbcType=DECIMAL}, #{enquiryQuantity,jdbcType=DECIMAL}, 
      #{enquiryStatus,jdbcType=TINYINT}, #{comments,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{contractType,jdbcType=VARCHAR}, #{contractSupplier,jdbcType=VARCHAR}, 
      #{lineId,jdbcType=INTEGER}, #{validperiod,jdbcType=INTEGER}, #{periodunit,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmEnquiryOrderDetail">
    insert into srm_enquiry_order_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="enquiryOrderNo != null">
        enquiry_order_no,
      </if>
      <if test="goodsCode != null">
        goods_code,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="goodsCommonName != null">
        goods_common_name,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="habitat != null">
        habitat,
      </if>
      <if test="basicUtil != null">
        basic_util,
      </if>
      <if test="apprdocno != null">
        apprdocno,
      </if>
      <if test="purchaseSupplierNo != null">
        purchase_supplier_no,
      </if>
      <if test="purchaseSupplierName != null">
        purchase_supplier_name,
      </if>
      <if test="purchaseBidPrice != null">
        purchase_bid_price,
      </if>
      <if test="recentSupplierName != null">
        recent_supplier_name,
      </if>
      <if test="recentPurchasePrice != null">
        recent_purchase_price,
      </if>
      <if test="lowestPrice != null">
        lowest_price,
      </if>
      <if test="orderQuantity != null">
        order_quantity,
      </if>
      <if test="supplierNo != null">
        supplier_no,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="enquiryPrice != null">
        enquiry_price,
      </if>
      <if test="enquiryQuantity != null">
        enquiry_quantity,
      </if>
      <if test="enquiryStatus != null">
        enquiry_status,
      </if>
      <if test="comments != null">
        comments,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="contractType != null">
        contract_type,
      </if>
      <if test="contractSupplier != null">
        contract_supplier,
      </if>
      <if test="lineId != null">
        line_id,
      </if>
      <if test="validperiod != null">
        validperiod,
      </if>
      <if test="periodunit != null">
        periodunit,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="enquiryOrderNo != null">
        #{enquiryOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="habitat != null">
        #{habitat,jdbcType=VARCHAR},
      </if>
      <if test="basicUtil != null">
        #{basicUtil,jdbcType=VARCHAR},
      </if>
      <if test="apprdocno != null">
        #{apprdocno,jdbcType=VARCHAR},
      </if>
      <if test="purchaseSupplierNo != null">
        #{purchaseSupplierNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseSupplierName != null">
        #{purchaseSupplierName,jdbcType=VARCHAR},
      </if>
      <if test="purchaseBidPrice != null">
        #{purchaseBidPrice,jdbcType=DECIMAL},
      </if>
      <if test="recentSupplierName != null">
        #{recentSupplierName,jdbcType=VARCHAR},
      </if>
      <if test="recentPurchasePrice != null">
        #{recentPurchasePrice,jdbcType=DECIMAL},
      </if>
      <if test="lowestPrice != null">
        #{lowestPrice,jdbcType=DECIMAL},
      </if>
      <if test="orderQuantity != null">
        #{orderQuantity,jdbcType=DECIMAL},
      </if>
      <if test="supplierNo != null">
        #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="enquiryPrice != null">
        #{enquiryPrice,jdbcType=DECIMAL},
      </if>
      <if test="enquiryQuantity != null">
        #{enquiryQuantity,jdbcType=DECIMAL},
      </if>
      <if test="enquiryStatus != null">
        #{enquiryStatus,jdbcType=TINYINT},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="contractType != null">
        #{contractType,jdbcType=VARCHAR},
      </if>
      <if test="contractSupplier != null">
        #{contractSupplier,jdbcType=VARCHAR},
      </if>
      <if test="lineId != null">
        #{lineId,jdbcType=INTEGER},
      </if>
      <if test="validperiod != null">
        #{validperiod,jdbcType=INTEGER},
      </if>
      <if test="periodunit != null">
        #{periodunit,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmEnquiryOrderDetailExample" resultType="java.lang.Long">
    select count(*) from srm_enquiry_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_enquiry_order_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.enquiryOrderNo != null">
        enquiry_order_no = #{record.enquiryOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCode != null">
        goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCommonName != null">
        goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.habitat != null">
        habitat = #{record.habitat,jdbcType=VARCHAR},
      </if>
      <if test="record.basicUtil != null">
        basic_util = #{record.basicUtil,jdbcType=VARCHAR},
      </if>
      <if test="record.apprdocno != null">
        apprdocno = #{record.apprdocno,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseSupplierNo != null">
        purchase_supplier_no = #{record.purchaseSupplierNo,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseSupplierName != null">
        purchase_supplier_name = #{record.purchaseSupplierName,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseBidPrice != null">
        purchase_bid_price = #{record.purchaseBidPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.recentSupplierName != null">
        recent_supplier_name = #{record.recentSupplierName,jdbcType=VARCHAR},
      </if>
      <if test="record.recentPurchasePrice != null">
        recent_purchase_price = #{record.recentPurchasePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.lowestPrice != null">
        lowest_price = #{record.lowestPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.orderQuantity != null">
        order_quantity = #{record.orderQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.supplierNo != null">
        supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierName != null">
        supplier_name = #{record.supplierName,jdbcType=VARCHAR},
      </if>
      <if test="record.enquiryPrice != null">
        enquiry_price = #{record.enquiryPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.enquiryQuantity != null">
        enquiry_quantity = #{record.enquiryQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.enquiryStatus != null">
        enquiry_status = #{record.enquiryStatus,jdbcType=TINYINT},
      </if>
      <if test="record.comments != null">
        comments = #{record.comments,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.contractType != null">
        contract_type = #{record.contractType,jdbcType=VARCHAR},
      </if>
      <if test="record.contractSupplier != null">
        contract_supplier = #{record.contractSupplier,jdbcType=VARCHAR},
      </if>
      <if test="record.lineId != null">
        line_id = #{record.lineId,jdbcType=INTEGER},
      </if>
      <if test="record.validperiod != null">
        validperiod = #{record.validperiod,jdbcType=INTEGER},
      </if>
      <if test="record.periodunit != null">
        periodunit = #{record.periodunit,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_enquiry_order_detail
    set id = #{record.id,jdbcType=INTEGER},
      enquiry_order_no = #{record.enquiryOrderNo,jdbcType=VARCHAR},
      goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      specifications = #{record.specifications,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      habitat = #{record.habitat,jdbcType=VARCHAR},
      basic_util = #{record.basicUtil,jdbcType=VARCHAR},
      apprdocno = #{record.apprdocno,jdbcType=VARCHAR},
      purchase_supplier_no = #{record.purchaseSupplierNo,jdbcType=VARCHAR},
      purchase_supplier_name = #{record.purchaseSupplierName,jdbcType=VARCHAR},
      purchase_bid_price = #{record.purchaseBidPrice,jdbcType=DECIMAL},
      recent_supplier_name = #{record.recentSupplierName,jdbcType=VARCHAR},
      recent_purchase_price = #{record.recentPurchasePrice,jdbcType=DECIMAL},
      lowest_price = #{record.lowestPrice,jdbcType=DECIMAL},
      order_quantity = #{record.orderQuantity,jdbcType=DECIMAL},
      supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      supplier_name = #{record.supplierName,jdbcType=VARCHAR},
      enquiry_price = #{record.enquiryPrice,jdbcType=DECIMAL},
      enquiry_quantity = #{record.enquiryQuantity,jdbcType=DECIMAL},
      enquiry_status = #{record.enquiryStatus,jdbcType=TINYINT},
      comments = #{record.comments,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      contract_type = #{record.contractType,jdbcType=VARCHAR},
      contract_supplier = #{record.contractSupplier,jdbcType=VARCHAR},
      line_id = #{record.lineId,jdbcType=INTEGER},
      validperiod = #{record.validperiod,jdbcType=INTEGER},
      periodunit = #{record.periodunit,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmEnquiryOrderDetail">
    update srm_enquiry_order_detail
    <set>
      <if test="enquiryOrderNo != null">
        enquiry_order_no = #{enquiryOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        goods_code = #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="habitat != null">
        habitat = #{habitat,jdbcType=VARCHAR},
      </if>
      <if test="basicUtil != null">
        basic_util = #{basicUtil,jdbcType=VARCHAR},
      </if>
      <if test="apprdocno != null">
        apprdocno = #{apprdocno,jdbcType=VARCHAR},
      </if>
      <if test="purchaseSupplierNo != null">
        purchase_supplier_no = #{purchaseSupplierNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseSupplierName != null">
        purchase_supplier_name = #{purchaseSupplierName,jdbcType=VARCHAR},
      </if>
      <if test="purchaseBidPrice != null">
        purchase_bid_price = #{purchaseBidPrice,jdbcType=DECIMAL},
      </if>
      <if test="recentSupplierName != null">
        recent_supplier_name = #{recentSupplierName,jdbcType=VARCHAR},
      </if>
      <if test="recentPurchasePrice != null">
        recent_purchase_price = #{recentPurchasePrice,jdbcType=DECIMAL},
      </if>
      <if test="lowestPrice != null">
        lowest_price = #{lowestPrice,jdbcType=DECIMAL},
      </if>
      <if test="orderQuantity != null">
        order_quantity = #{orderQuantity,jdbcType=DECIMAL},
      </if>
      <if test="supplierNo != null">
        supplier_no = #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="enquiryPrice != null">
        enquiry_price = #{enquiryPrice,jdbcType=DECIMAL},
      </if>
      <if test="enquiryQuantity != null">
        enquiry_quantity = #{enquiryQuantity,jdbcType=DECIMAL},
      </if>
      <if test="enquiryStatus != null">
        enquiry_status = #{enquiryStatus,jdbcType=TINYINT},
      </if>
      <if test="comments != null">
        comments = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="contractType != null">
        contract_type = #{contractType,jdbcType=VARCHAR},
      </if>
      <if test="contractSupplier != null">
        contract_supplier = #{contractSupplier,jdbcType=VARCHAR},
      </if>
      <if test="lineId != null">
        line_id = #{lineId,jdbcType=INTEGER},
      </if>
      <if test="validperiod != null">
        validperiod = #{validperiod,jdbcType=INTEGER},
      </if>
      <if test="periodunit != null">
        periodunit = #{periodunit,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmEnquiryOrderDetail">
    update srm_enquiry_order_detail
    set enquiry_order_no = #{enquiryOrderNo,jdbcType=VARCHAR},
      goods_code = #{goodsCode,jdbcType=VARCHAR},
      bar_code = #{barCode,jdbcType=VARCHAR},
      goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      specifications = #{specifications,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      habitat = #{habitat,jdbcType=VARCHAR},
      basic_util = #{basicUtil,jdbcType=VARCHAR},
      apprdocno = #{apprdocno,jdbcType=VARCHAR},
      purchase_supplier_no = #{purchaseSupplierNo,jdbcType=VARCHAR},
      purchase_supplier_name = #{purchaseSupplierName,jdbcType=VARCHAR},
      purchase_bid_price = #{purchaseBidPrice,jdbcType=DECIMAL},
      recent_supplier_name = #{recentSupplierName,jdbcType=VARCHAR},
      recent_purchase_price = #{recentPurchasePrice,jdbcType=DECIMAL},
      lowest_price = #{lowestPrice,jdbcType=DECIMAL},
      order_quantity = #{orderQuantity,jdbcType=DECIMAL},
      supplier_no = #{supplierNo,jdbcType=VARCHAR},
      supplier_name = #{supplierName,jdbcType=VARCHAR},
      enquiry_price = #{enquiryPrice,jdbcType=DECIMAL},
      enquiry_quantity = #{enquiryQuantity,jdbcType=DECIMAL},
      enquiry_status = #{enquiryStatus,jdbcType=TINYINT},
      comments = #{comments,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      contract_type = #{contractType,jdbcType=VARCHAR},
      contract_supplier = #{contractSupplier,jdbcType=VARCHAR},
      line_id = #{lineId,jdbcType=INTEGER},
      validperiod = #{validperiod,jdbcType=INTEGER},
      periodunit = #{periodunit,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>