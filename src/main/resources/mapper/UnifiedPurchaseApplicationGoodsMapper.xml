<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.UnifiedPurchaseApplicationGoodsMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.UnifiedPurchaseApplicationGoods">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="purchase_application_no" jdbcType="VARCHAR" property="purchaseApplicationNo" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="spu_id" jdbcType="BIGINT" property="spuId" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="row_no" jdbcType="BIGINT" property="rowNo" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="apply_count" jdbcType="INTEGER" property="applyCount" />
    <result column="approved_count" jdbcType="INTEGER" property="approvedCount" />
    <result column="receiving_count" jdbcType="INTEGER" property="receivingCount" />
    <result column="cold_chain" jdbcType="TINYINT" property="coldChain" />
    <result column="receiving_temperature" jdbcType="INTEGER" property="receivingTemperature" />
    <result column="habitat" jdbcType="VARCHAR" property="habitat" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="os_apply_count" jdbcType="INTEGER" property="osApplyCount" />
    <result column="box_specifications" jdbcType="VARCHAR" property="boxSpecifications" />
    <result column="move_mounth_sale_count" jdbcType="DECIMAL" property="moveMounthSaleCount" />
    <result column="store_stock_count" jdbcType="DECIMAL" property="storeStockCount" />
    <result column="dc_stock_count" jdbcType="DECIMAL" property="dcStockCount" />
    <result column="way_order_count" jdbcType="INTEGER" property="wayOrderCount" />
    <result column="stock_use_ratio" jdbcType="DECIMAL" property="stockUseRatio" />
    <result column="supplier_repertory_no" jdbcType="VARCHAR" property="supplierRepertoryNo" />
    <result column="supplier_repertory_name" jdbcType="VARCHAR" property="supplierRepertoryName" />
    <result column="supplier_no" jdbcType="VARCHAR" property="supplierNo" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="all_stock" jdbcType="DECIMAL" property="allStock" />
    <result column="buying_price" jdbcType="DECIMAL" property="buyingPrice" />
    <result column="supplier_lead_time" jdbcType="DECIMAL" property="supplierLeadTime" />
    <result column="dc_lead_time" jdbcType="DECIMAL" property="dcLeadTime" />
    <result column="now_sale_store_count" jdbcType="INTEGER" property="nowSaleStoreCount" />
    <result column="future_sale_store_count" jdbcType="INTEGER" property="futureSaleStoreCount" />
    <result column="reason_comments" jdbcType="VARCHAR" property="reasonComments" />
    <result column="unified_company_no" jdbcType="VARCHAR" property="unifiedCompanyNo" />
    <result column="unified_company_name" jdbcType="VARCHAR" property="unifiedCompanyName" />
    <result column="received_time" jdbcType="TIMESTAMP" property="receivedTime" />
    <result column="apprdocno" jdbcType="VARCHAR" property="apprdocno" />
    <result column="replenishment_num" jdbcType="DECIMAL" property="replenishmentNum" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, purchase_application_no, goods_code, bar_code, spu_id, goods_common_name, goods_name, 
    description, row_no, specifications, dosage_form, apply_count, approved_count, receiving_count, 
    cold_chain, receiving_temperature, habitat, manufacturer, gmt_create, gmt_update, 
    `status`, extend, version, created_by, update_by, os_apply_count, box_specifications, 
    move_mounth_sale_count, store_stock_count, dc_stock_count, way_order_count, stock_use_ratio, 
    supplier_repertory_no, supplier_repertory_name, supplier_no, supplier_name, all_stock, 
    buying_price, supplier_lead_time, dc_lead_time, now_sale_store_count, future_sale_store_count, 
    reason_comments, unified_company_no, unified_company_name, received_time, apprdocno, 
    replenishment_num, unit
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.UnifiedPurchaseApplicationGoodsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from unified_purchase_application_goods
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from unified_purchase_application_goods
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from unified_purchase_application_goods
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.UnifiedPurchaseApplicationGoodsExample">
    delete from unified_purchase_application_goods
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.UnifiedPurchaseApplicationGoods">
    insert into unified_purchase_application_goods (id, purchase_application_no, goods_code, 
      bar_code, spu_id, goods_common_name, 
      goods_name, description, row_no, 
      specifications, dosage_form, apply_count, 
      approved_count, receiving_count, cold_chain, 
      receiving_temperature, habitat, manufacturer, 
      gmt_create, gmt_update, `status`, 
      extend, version, created_by, 
      update_by, os_apply_count, box_specifications, 
      move_mounth_sale_count, store_stock_count, dc_stock_count, 
      way_order_count, stock_use_ratio, supplier_repertory_no, 
      supplier_repertory_name, supplier_no, supplier_name, 
      all_stock, buying_price, supplier_lead_time, 
      dc_lead_time, now_sale_store_count, future_sale_store_count, 
      reason_comments, unified_company_no, unified_company_name, 
      received_time, apprdocno, replenishment_num, 
      unit)
    values (#{id,jdbcType=INTEGER}, #{purchaseApplicationNo,jdbcType=VARCHAR}, #{goodsCode,jdbcType=VARCHAR}, 
      #{barCode,jdbcType=VARCHAR}, #{spuId,jdbcType=BIGINT}, #{goodsCommonName,jdbcType=VARCHAR}, 
      #{goodsName,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{rowNo,jdbcType=BIGINT}, 
      #{specifications,jdbcType=VARCHAR}, #{dosageForm,jdbcType=VARCHAR}, #{applyCount,jdbcType=INTEGER}, 
      #{approvedCount,jdbcType=INTEGER}, #{receivingCount,jdbcType=INTEGER}, #{coldChain,jdbcType=TINYINT}, 
      #{receivingTemperature,jdbcType=INTEGER}, #{habitat,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}, 
      #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, 
      #{updateBy,jdbcType=BIGINT}, #{osApplyCount,jdbcType=INTEGER}, #{boxSpecifications,jdbcType=VARCHAR}, 
      #{moveMounthSaleCount,jdbcType=DECIMAL}, #{storeStockCount,jdbcType=DECIMAL}, #{dcStockCount,jdbcType=DECIMAL}, 
      #{wayOrderCount,jdbcType=INTEGER}, #{stockUseRatio,jdbcType=DECIMAL}, #{supplierRepertoryNo,jdbcType=VARCHAR}, 
      #{supplierRepertoryName,jdbcType=VARCHAR}, #{supplierNo,jdbcType=VARCHAR}, #{supplierName,jdbcType=VARCHAR}, 
      #{allStock,jdbcType=DECIMAL}, #{buyingPrice,jdbcType=DECIMAL}, #{supplierLeadTime,jdbcType=DECIMAL}, 
      #{dcLeadTime,jdbcType=DECIMAL}, #{nowSaleStoreCount,jdbcType=INTEGER}, #{futureSaleStoreCount,jdbcType=INTEGER}, 
      #{reasonComments,jdbcType=VARCHAR}, #{unifiedCompanyNo,jdbcType=VARCHAR}, #{unifiedCompanyName,jdbcType=VARCHAR}, 
      #{receivedTime,jdbcType=TIMESTAMP}, #{apprdocno,jdbcType=VARCHAR}, #{replenishmentNum,jdbcType=DECIMAL}, 
      #{unit,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.UnifiedPurchaseApplicationGoods">
    insert into unified_purchase_application_goods
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="purchaseApplicationNo != null">
        purchase_application_no,
      </if>
      <if test="goodsCode != null">
        goods_code,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="spuId != null">
        spu_id,
      </if>
      <if test="goodsCommonName != null">
        goods_common_name,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="rowNo != null">
        row_no,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="dosageForm != null">
        dosage_form,
      </if>
      <if test="applyCount != null">
        apply_count,
      </if>
      <if test="approvedCount != null">
        approved_count,
      </if>
      <if test="receivingCount != null">
        receiving_count,
      </if>
      <if test="coldChain != null">
        cold_chain,
      </if>
      <if test="receivingTemperature != null">
        receiving_temperature,
      </if>
      <if test="habitat != null">
        habitat,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="osApplyCount != null">
        os_apply_count,
      </if>
      <if test="boxSpecifications != null">
        box_specifications,
      </if>
      <if test="moveMounthSaleCount != null">
        move_mounth_sale_count,
      </if>
      <if test="storeStockCount != null">
        store_stock_count,
      </if>
      <if test="dcStockCount != null">
        dc_stock_count,
      </if>
      <if test="wayOrderCount != null">
        way_order_count,
      </if>
      <if test="stockUseRatio != null">
        stock_use_ratio,
      </if>
      <if test="supplierRepertoryNo != null">
        supplier_repertory_no,
      </if>
      <if test="supplierRepertoryName != null">
        supplier_repertory_name,
      </if>
      <if test="supplierNo != null">
        supplier_no,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="allStock != null">
        all_stock,
      </if>
      <if test="buyingPrice != null">
        buying_price,
      </if>
      <if test="supplierLeadTime != null">
        supplier_lead_time,
      </if>
      <if test="dcLeadTime != null">
        dc_lead_time,
      </if>
      <if test="nowSaleStoreCount != null">
        now_sale_store_count,
      </if>
      <if test="futureSaleStoreCount != null">
        future_sale_store_count,
      </if>
      <if test="reasonComments != null">
        reason_comments,
      </if>
      <if test="unifiedCompanyNo != null">
        unified_company_no,
      </if>
      <if test="unifiedCompanyName != null">
        unified_company_name,
      </if>
      <if test="receivedTime != null">
        received_time,
      </if>
      <if test="apprdocno != null">
        apprdocno,
      </if>
      <if test="replenishmentNum != null">
        replenishment_num,
      </if>
      <if test="unit != null">
        unit,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="purchaseApplicationNo != null">
        #{purchaseApplicationNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="spuId != null">
        #{spuId,jdbcType=BIGINT},
      </if>
      <if test="goodsCommonName != null">
        #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="rowNo != null">
        #{rowNo,jdbcType=BIGINT},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="applyCount != null">
        #{applyCount,jdbcType=INTEGER},
      </if>
      <if test="approvedCount != null">
        #{approvedCount,jdbcType=INTEGER},
      </if>
      <if test="receivingCount != null">
        #{receivingCount,jdbcType=INTEGER},
      </if>
      <if test="coldChain != null">
        #{coldChain,jdbcType=TINYINT},
      </if>
      <if test="receivingTemperature != null">
        #{receivingTemperature,jdbcType=INTEGER},
      </if>
      <if test="habitat != null">
        #{habitat,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="osApplyCount != null">
        #{osApplyCount,jdbcType=INTEGER},
      </if>
      <if test="boxSpecifications != null">
        #{boxSpecifications,jdbcType=VARCHAR},
      </if>
      <if test="moveMounthSaleCount != null">
        #{moveMounthSaleCount,jdbcType=DECIMAL},
      </if>
      <if test="storeStockCount != null">
        #{storeStockCount,jdbcType=DECIMAL},
      </if>
      <if test="dcStockCount != null">
        #{dcStockCount,jdbcType=DECIMAL},
      </if>
      <if test="wayOrderCount != null">
        #{wayOrderCount,jdbcType=INTEGER},
      </if>
      <if test="stockUseRatio != null">
        #{stockUseRatio,jdbcType=DECIMAL},
      </if>
      <if test="supplierRepertoryNo != null">
        #{supplierRepertoryNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierRepertoryName != null">
        #{supplierRepertoryName,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null">
        #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="allStock != null">
        #{allStock,jdbcType=DECIMAL},
      </if>
      <if test="buyingPrice != null">
        #{buyingPrice,jdbcType=DECIMAL},
      </if>
      <if test="supplierLeadTime != null">
        #{supplierLeadTime,jdbcType=DECIMAL},
      </if>
      <if test="dcLeadTime != null">
        #{dcLeadTime,jdbcType=DECIMAL},
      </if>
      <if test="nowSaleStoreCount != null">
        #{nowSaleStoreCount,jdbcType=INTEGER},
      </if>
      <if test="futureSaleStoreCount != null">
        #{futureSaleStoreCount,jdbcType=INTEGER},
      </if>
      <if test="reasonComments != null">
        #{reasonComments,jdbcType=VARCHAR},
      </if>
      <if test="unifiedCompanyNo != null">
        #{unifiedCompanyNo,jdbcType=VARCHAR},
      </if>
      <if test="unifiedCompanyName != null">
        #{unifiedCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="receivedTime != null">
        #{receivedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="apprdocno != null">
        #{apprdocno,jdbcType=VARCHAR},
      </if>
      <if test="replenishmentNum != null">
        #{replenishmentNum,jdbcType=DECIMAL},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.UnifiedPurchaseApplicationGoodsExample" resultType="java.lang.Long">
    select count(*) from unified_purchase_application_goods
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update unified_purchase_application_goods
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.purchaseApplicationNo != null">
        purchase_application_no = #{record.purchaseApplicationNo,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCode != null">
        goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=VARCHAR},
      </if>
      <if test="record.spuId != null">
        spu_id = #{record.spuId,jdbcType=BIGINT},
      </if>
      <if test="record.goodsCommonName != null">
        goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.rowNo != null">
        row_no = #{record.rowNo,jdbcType=BIGINT},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.dosageForm != null">
        dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="record.applyCount != null">
        apply_count = #{record.applyCount,jdbcType=INTEGER},
      </if>
      <if test="record.approvedCount != null">
        approved_count = #{record.approvedCount,jdbcType=INTEGER},
      </if>
      <if test="record.receivingCount != null">
        receiving_count = #{record.receivingCount,jdbcType=INTEGER},
      </if>
      <if test="record.coldChain != null">
        cold_chain = #{record.coldChain,jdbcType=TINYINT},
      </if>
      <if test="record.receivingTemperature != null">
        receiving_temperature = #{record.receivingTemperature,jdbcType=INTEGER},
      </if>
      <if test="record.habitat != null">
        habitat = #{record.habitat,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=BIGINT},
      </if>
      <if test="record.osApplyCount != null">
        os_apply_count = #{record.osApplyCount,jdbcType=INTEGER},
      </if>
      <if test="record.boxSpecifications != null">
        box_specifications = #{record.boxSpecifications,jdbcType=VARCHAR},
      </if>
      <if test="record.moveMounthSaleCount != null">
        move_mounth_sale_count = #{record.moveMounthSaleCount,jdbcType=DECIMAL},
      </if>
      <if test="record.storeStockCount != null">
        store_stock_count = #{record.storeStockCount,jdbcType=DECIMAL},
      </if>
      <if test="record.dcStockCount != null">
        dc_stock_count = #{record.dcStockCount,jdbcType=DECIMAL},
      </if>
      <if test="record.wayOrderCount != null">
        way_order_count = #{record.wayOrderCount,jdbcType=INTEGER},
      </if>
      <if test="record.stockUseRatio != null">
        stock_use_ratio = #{record.stockUseRatio,jdbcType=DECIMAL},
      </if>
      <if test="record.supplierRepertoryNo != null">
        supplier_repertory_no = #{record.supplierRepertoryNo,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierRepertoryName != null">
        supplier_repertory_name = #{record.supplierRepertoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierNo != null">
        supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierName != null">
        supplier_name = #{record.supplierName,jdbcType=VARCHAR},
      </if>
      <if test="record.allStock != null">
        all_stock = #{record.allStock,jdbcType=DECIMAL},
      </if>
      <if test="record.buyingPrice != null">
        buying_price = #{record.buyingPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.supplierLeadTime != null">
        supplier_lead_time = #{record.supplierLeadTime,jdbcType=DECIMAL},
      </if>
      <if test="record.dcLeadTime != null">
        dc_lead_time = #{record.dcLeadTime,jdbcType=DECIMAL},
      </if>
      <if test="record.nowSaleStoreCount != null">
        now_sale_store_count = #{record.nowSaleStoreCount,jdbcType=INTEGER},
      </if>
      <if test="record.futureSaleStoreCount != null">
        future_sale_store_count = #{record.futureSaleStoreCount,jdbcType=INTEGER},
      </if>
      <if test="record.reasonComments != null">
        reason_comments = #{record.reasonComments,jdbcType=VARCHAR},
      </if>
      <if test="record.unifiedCompanyNo != null">
        unified_company_no = #{record.unifiedCompanyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.unifiedCompanyName != null">
        unified_company_name = #{record.unifiedCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="record.receivedTime != null">
        received_time = #{record.receivedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.apprdocno != null">
        apprdocno = #{record.apprdocno,jdbcType=VARCHAR},
      </if>
      <if test="record.replenishmentNum != null">
        replenishment_num = #{record.replenishmentNum,jdbcType=DECIMAL},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update unified_purchase_application_goods
    set id = #{record.id,jdbcType=INTEGER},
      purchase_application_no = #{record.purchaseApplicationNo,jdbcType=VARCHAR},
      goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      spu_id = #{record.spuId,jdbcType=BIGINT},
      goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      row_no = #{record.rowNo,jdbcType=BIGINT},
      specifications = #{record.specifications,jdbcType=VARCHAR},
      dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      apply_count = #{record.applyCount,jdbcType=INTEGER},
      approved_count = #{record.approvedCount,jdbcType=INTEGER},
      receiving_count = #{record.receivingCount,jdbcType=INTEGER},
      cold_chain = #{record.coldChain,jdbcType=TINYINT},
      receiving_temperature = #{record.receivingTemperature,jdbcType=INTEGER},
      habitat = #{record.habitat,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      `status` = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      update_by = #{record.updateBy,jdbcType=BIGINT},
      os_apply_count = #{record.osApplyCount,jdbcType=INTEGER},
      box_specifications = #{record.boxSpecifications,jdbcType=VARCHAR},
      move_mounth_sale_count = #{record.moveMounthSaleCount,jdbcType=DECIMAL},
      store_stock_count = #{record.storeStockCount,jdbcType=DECIMAL},
      dc_stock_count = #{record.dcStockCount,jdbcType=DECIMAL},
      way_order_count = #{record.wayOrderCount,jdbcType=INTEGER},
      stock_use_ratio = #{record.stockUseRatio,jdbcType=DECIMAL},
      supplier_repertory_no = #{record.supplierRepertoryNo,jdbcType=VARCHAR},
      supplier_repertory_name = #{record.supplierRepertoryName,jdbcType=VARCHAR},
      supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      supplier_name = #{record.supplierName,jdbcType=VARCHAR},
      all_stock = #{record.allStock,jdbcType=DECIMAL},
      buying_price = #{record.buyingPrice,jdbcType=DECIMAL},
      supplier_lead_time = #{record.supplierLeadTime,jdbcType=DECIMAL},
      dc_lead_time = #{record.dcLeadTime,jdbcType=DECIMAL},
      now_sale_store_count = #{record.nowSaleStoreCount,jdbcType=INTEGER},
      future_sale_store_count = #{record.futureSaleStoreCount,jdbcType=INTEGER},
      reason_comments = #{record.reasonComments,jdbcType=VARCHAR},
      unified_company_no = #{record.unifiedCompanyNo,jdbcType=VARCHAR},
      unified_company_name = #{record.unifiedCompanyName,jdbcType=VARCHAR},
      received_time = #{record.receivedTime,jdbcType=TIMESTAMP},
      apprdocno = #{record.apprdocno,jdbcType=VARCHAR},
      replenishment_num = #{record.replenishmentNum,jdbcType=DECIMAL},
      unit = #{record.unit,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.UnifiedPurchaseApplicationGoods">
    update unified_purchase_application_goods
    <set>
      <if test="purchaseApplicationNo != null">
        purchase_application_no = #{purchaseApplicationNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        goods_code = #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="spuId != null">
        spu_id = #{spuId,jdbcType=BIGINT},
      </if>
      <if test="goodsCommonName != null">
        goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="rowNo != null">
        row_no = #{rowNo,jdbcType=BIGINT},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        dosage_form = #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="applyCount != null">
        apply_count = #{applyCount,jdbcType=INTEGER},
      </if>
      <if test="approvedCount != null">
        approved_count = #{approvedCount,jdbcType=INTEGER},
      </if>
      <if test="receivingCount != null">
        receiving_count = #{receivingCount,jdbcType=INTEGER},
      </if>
      <if test="coldChain != null">
        cold_chain = #{coldChain,jdbcType=TINYINT},
      </if>
      <if test="receivingTemperature != null">
        receiving_temperature = #{receivingTemperature,jdbcType=INTEGER},
      </if>
      <if test="habitat != null">
        habitat = #{habitat,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="osApplyCount != null">
        os_apply_count = #{osApplyCount,jdbcType=INTEGER},
      </if>
      <if test="boxSpecifications != null">
        box_specifications = #{boxSpecifications,jdbcType=VARCHAR},
      </if>
      <if test="moveMounthSaleCount != null">
        move_mounth_sale_count = #{moveMounthSaleCount,jdbcType=DECIMAL},
      </if>
      <if test="storeStockCount != null">
        store_stock_count = #{storeStockCount,jdbcType=DECIMAL},
      </if>
      <if test="dcStockCount != null">
        dc_stock_count = #{dcStockCount,jdbcType=DECIMAL},
      </if>
      <if test="wayOrderCount != null">
        way_order_count = #{wayOrderCount,jdbcType=INTEGER},
      </if>
      <if test="stockUseRatio != null">
        stock_use_ratio = #{stockUseRatio,jdbcType=DECIMAL},
      </if>
      <if test="supplierRepertoryNo != null">
        supplier_repertory_no = #{supplierRepertoryNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierRepertoryName != null">
        supplier_repertory_name = #{supplierRepertoryName,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null">
        supplier_no = #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="allStock != null">
        all_stock = #{allStock,jdbcType=DECIMAL},
      </if>
      <if test="buyingPrice != null">
        buying_price = #{buyingPrice,jdbcType=DECIMAL},
      </if>
      <if test="supplierLeadTime != null">
        supplier_lead_time = #{supplierLeadTime,jdbcType=DECIMAL},
      </if>
      <if test="dcLeadTime != null">
        dc_lead_time = #{dcLeadTime,jdbcType=DECIMAL},
      </if>
      <if test="nowSaleStoreCount != null">
        now_sale_store_count = #{nowSaleStoreCount,jdbcType=INTEGER},
      </if>
      <if test="futureSaleStoreCount != null">
        future_sale_store_count = #{futureSaleStoreCount,jdbcType=INTEGER},
      </if>
      <if test="reasonComments != null">
        reason_comments = #{reasonComments,jdbcType=VARCHAR},
      </if>
      <if test="unifiedCompanyNo != null">
        unified_company_no = #{unifiedCompanyNo,jdbcType=VARCHAR},
      </if>
      <if test="unifiedCompanyName != null">
        unified_company_name = #{unifiedCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="receivedTime != null">
        received_time = #{receivedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="apprdocno != null">
        apprdocno = #{apprdocno,jdbcType=VARCHAR},
      </if>
      <if test="replenishmentNum != null">
        replenishment_num = #{replenishmentNum,jdbcType=DECIMAL},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.UnifiedPurchaseApplicationGoods">
    update unified_purchase_application_goods
    set purchase_application_no = #{purchaseApplicationNo,jdbcType=VARCHAR},
      goods_code = #{goodsCode,jdbcType=VARCHAR},
      bar_code = #{barCode,jdbcType=VARCHAR},
      spu_id = #{spuId,jdbcType=BIGINT},
      goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      row_no = #{rowNo,jdbcType=BIGINT},
      specifications = #{specifications,jdbcType=VARCHAR},
      dosage_form = #{dosageForm,jdbcType=VARCHAR},
      apply_count = #{applyCount,jdbcType=INTEGER},
      approved_count = #{approvedCount,jdbcType=INTEGER},
      receiving_count = #{receivingCount,jdbcType=INTEGER},
      cold_chain = #{coldChain,jdbcType=TINYINT},
      receiving_temperature = #{receivingTemperature,jdbcType=INTEGER},
      habitat = #{habitat,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      os_apply_count = #{osApplyCount,jdbcType=INTEGER},
      box_specifications = #{boxSpecifications,jdbcType=VARCHAR},
      move_mounth_sale_count = #{moveMounthSaleCount,jdbcType=DECIMAL},
      store_stock_count = #{storeStockCount,jdbcType=DECIMAL},
      dc_stock_count = #{dcStockCount,jdbcType=DECIMAL},
      way_order_count = #{wayOrderCount,jdbcType=INTEGER},
      stock_use_ratio = #{stockUseRatio,jdbcType=DECIMAL},
      supplier_repertory_no = #{supplierRepertoryNo,jdbcType=VARCHAR},
      supplier_repertory_name = #{supplierRepertoryName,jdbcType=VARCHAR},
      supplier_no = #{supplierNo,jdbcType=VARCHAR},
      supplier_name = #{supplierName,jdbcType=VARCHAR},
      all_stock = #{allStock,jdbcType=DECIMAL},
      buying_price = #{buyingPrice,jdbcType=DECIMAL},
      supplier_lead_time = #{supplierLeadTime,jdbcType=DECIMAL},
      dc_lead_time = #{dcLeadTime,jdbcType=DECIMAL},
      now_sale_store_count = #{nowSaleStoreCount,jdbcType=INTEGER},
      future_sale_store_count = #{futureSaleStoreCount,jdbcType=INTEGER},
      reason_comments = #{reasonComments,jdbcType=VARCHAR},
      unified_company_no = #{unifiedCompanyNo,jdbcType=VARCHAR},
      unified_company_name = #{unifiedCompanyName,jdbcType=VARCHAR},
      received_time = #{receivedTime,jdbcType=TIMESTAMP},
      apprdocno = #{apprdocno,jdbcType=VARCHAR},
      replenishment_num = #{replenishmentNum,jdbcType=DECIMAL},
      unit = #{unit,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>