<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SettleReviewRecordMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SettleReviewRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="settle_order_no" jdbcType="VARCHAR" property="settleOrderNo" />
    <result column="review_step" jdbcType="TINYINT" property="reviewStep" />
    <result column="review_result" jdbcType="TINYINT" property="reviewResult" />
    <result column="comments" jdbcType="VARCHAR" property="comments" />
    <result column="operation_time" jdbcType="TIMESTAMP" property="operationTime" />
    <result column="bsource" jdbcType="TINYINT" property="bsource" />
    <result column="bdestination" jdbcType="TINYINT" property="bdestination" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, settle_order_no, review_step, review_result, comments, operation_time, bsource, 
    bdestination, gmt_create, gmt_update, `status`, version, created_by, created_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SettleReviewRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from settle_review_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from settle_review_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from settle_review_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SettleReviewRecordExample">
    delete from settle_review_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SettleReviewRecord">
    insert into settle_review_record (id, settle_order_no, review_step, 
      review_result, comments, operation_time, 
      bsource, bdestination, gmt_create, 
      gmt_update, `status`, version, 
      created_by, created_name)
    values (#{id,jdbcType=INTEGER}, #{settleOrderNo,jdbcType=VARCHAR}, #{reviewStep,jdbcType=TINYINT}, 
      #{reviewResult,jdbcType=TINYINT}, #{comments,jdbcType=VARCHAR}, #{operationTime,jdbcType=TIMESTAMP}, 
      #{bsource,jdbcType=TINYINT}, #{bdestination,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SettleReviewRecord">
    insert into settle_review_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="settleOrderNo != null">
        settle_order_no,
      </if>
      <if test="reviewStep != null">
        review_step,
      </if>
      <if test="reviewResult != null">
        review_result,
      </if>
      <if test="comments != null">
        comments,
      </if>
      <if test="operationTime != null">
        operation_time,
      </if>
      <if test="bsource != null">
        bsource,
      </if>
      <if test="bdestination != null">
        bdestination,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="settleOrderNo != null">
        #{settleOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="reviewStep != null">
        #{reviewStep,jdbcType=TINYINT},
      </if>
      <if test="reviewResult != null">
        #{reviewResult,jdbcType=TINYINT},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="operationTime != null">
        #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bsource != null">
        #{bsource,jdbcType=TINYINT},
      </if>
      <if test="bdestination != null">
        #{bdestination,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SettleReviewRecordExample" resultType="java.lang.Long">
    select count(*) from settle_review_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update settle_review_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.settleOrderNo != null">
        settle_order_no = #{record.settleOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reviewStep != null">
        review_step = #{record.reviewStep,jdbcType=TINYINT},
      </if>
      <if test="record.reviewResult != null">
        review_result = #{record.reviewResult,jdbcType=TINYINT},
      </if>
      <if test="record.comments != null">
        comments = #{record.comments,jdbcType=VARCHAR},
      </if>
      <if test="record.operationTime != null">
        operation_time = #{record.operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.bsource != null">
        bsource = #{record.bsource,jdbcType=TINYINT},
      </if>
      <if test="record.bdestination != null">
        bdestination = #{record.bdestination,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update settle_review_record
    set id = #{record.id,jdbcType=INTEGER},
      settle_order_no = #{record.settleOrderNo,jdbcType=VARCHAR},
      review_step = #{record.reviewStep,jdbcType=TINYINT},
      review_result = #{record.reviewResult,jdbcType=TINYINT},
      comments = #{record.comments,jdbcType=VARCHAR},
      operation_time = #{record.operationTime,jdbcType=TIMESTAMP},
      bsource = #{record.bsource,jdbcType=TINYINT},
      bdestination = #{record.bdestination,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      `status` = #{record.status,jdbcType=TINYINT},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SettleReviewRecord">
    update settle_review_record
    <set>
      <if test="settleOrderNo != null">
        settle_order_no = #{settleOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="reviewStep != null">
        review_step = #{reviewStep,jdbcType=TINYINT},
      </if>
      <if test="reviewResult != null">
        review_result = #{reviewResult,jdbcType=TINYINT},
      </if>
      <if test="comments != null">
        comments = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="operationTime != null">
        operation_time = #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bsource != null">
        bsource = #{bsource,jdbcType=TINYINT},
      </if>
      <if test="bdestination != null">
        bdestination = #{bdestination,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SettleReviewRecord">
    update settle_review_record
    set settle_order_no = #{settleOrderNo,jdbcType=VARCHAR},
      review_step = #{reviewStep,jdbcType=TINYINT},
      review_result = #{reviewResult,jdbcType=TINYINT},
      comments = #{comments,jdbcType=VARCHAR},
      operation_time = #{operationTime,jdbcType=TIMESTAMP},
      bsource = #{bsource,jdbcType=TINYINT},
      bdestination = #{bdestination,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=TINYINT},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>