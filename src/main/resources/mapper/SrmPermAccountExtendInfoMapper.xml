<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmPermAccountExtendInfoMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmPermAccountExtendInfo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="login_name" jdbcType="VARCHAR" property="loginName" />
    <result column="account_no" jdbcType="VARCHAR" property="accountNo" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="parent_account_no" jdbcType="VARCHAR" property="parentAccountNo" />
    <result column="account_phone" jdbcType="VARCHAR" property="accountPhone" />
    <result column="account_type" jdbcType="INTEGER" property="accountType" />
    <result column="account_business_type" jdbcType="INTEGER" property="accountBusinessType" />
    <result column="account_register_status" jdbcType="INTEGER" property="accountRegisterStatus" />
    <result column="account_use_status" jdbcType="INTEGER" property="accountUseStatus" />
    <result column="account_expiration_type" jdbcType="INTEGER" property="accountExpirationType" />
    <result column="account_expiration_begin" jdbcType="TIMESTAMP" property="accountExpirationBegin" />
    <result column="account_expiration_end" jdbcType="TIMESTAMP" property="accountExpirationEnd" />
    <result column="account_email" jdbcType="VARCHAR" property="accountEmail" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="business_contacts" jdbcType="VARCHAR" property="businessContacts" />
    <result column="inner_contacts" jdbcType="VARCHAR" property="innerContacts" />
    <result column="purchase_type_scope" jdbcType="VARCHAR" property="purchaseTypeScope" />
    <result column="account_class" jdbcType="INTEGER" property="accountClass" />
    <result column="account_response" jdbcType="VARCHAR" property="accountResponse" />
    <result column="response_phone" jdbcType="VARCHAR" property="responsePhone" />
    <result column="response_user_id" jdbcType="BIGINT" property="responseUserId" />
    <result column="activate_status" jdbcType="INTEGER" property="activateStatus" />
    <result column="activate_code" jdbcType="VARCHAR" property="activateCode" />
    <result column="activate_date_start" jdbcType="TIMESTAMP" property="activateDateStart" />
    <result column="activate_date_end" jdbcType="TIMESTAMP" property="activateDateEnd" />
    <result column="max_download_time" jdbcType="INTEGER" property="maxDownloadTime" />
    <result column="activate_limit" jdbcType="INTEGER" property="activateLimit" />
    <result column="freeze_limit" jdbcType="INTEGER" property="freezeLimit" />
    <result column="permission_id" jdbcType="BIGINT" property="permissionId" />
    <result column="level_org_type" jdbcType="INTEGER" property="levelOrgType" />
    <result column="level_org_id" jdbcType="BIGINT" property="levelOrgId" />
    <result column="cooperate_code" jdbcType="VARCHAR" property="cooperateCode" />
    <result column="cooperate_name" jdbcType="VARCHAR" property="cooperateName" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="goods_variety" jdbcType="VARCHAR" property="goodsVariety" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="business_inner_code" jdbcType="VARCHAR" property="businessInnerCode" />
    <result column="business_inner_name" jdbcType="VARCHAR" property="businessInnerName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, login_name, account_no, account_name, parent_account_no, account_phone, account_type, 
    account_business_type, account_register_status, account_use_status, account_expiration_type, 
    account_expiration_begin, account_expiration_end, account_email, business_name, business_contacts, 
    inner_contacts, purchase_type_scope, account_class, account_response, response_phone, 
    response_user_id, activate_status, activate_code, activate_date_start, activate_date_end, 
    max_download_time, activate_limit, freeze_limit, permission_id, level_org_type, level_org_id, 
    cooperate_code, cooperate_name, created_by, created_name, goods_variety, updated_by, 
    updated_name, gmt_create, gmt_update, `status`, extend, version, business_inner_code, 
    business_inner_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmPermAccountExtendInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_perm_account_extend_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_perm_account_extend_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from srm_perm_account_extend_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmPermAccountExtendInfoExample">
    delete from srm_perm_account_extend_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmPermAccountExtendInfo">
    insert into srm_perm_account_extend_info (id, login_name, account_no, 
      account_name, parent_account_no, account_phone, 
      account_type, account_business_type, account_register_status, 
      account_use_status, account_expiration_type, 
      account_expiration_begin, account_expiration_end, 
      account_email, business_name, business_contacts, 
      inner_contacts, purchase_type_scope, account_class, 
      account_response, response_phone, response_user_id, 
      activate_status, activate_code, activate_date_start, 
      activate_date_end, max_download_time, activate_limit, 
      freeze_limit, permission_id, level_org_type, 
      level_org_id, cooperate_code, cooperate_name, 
      created_by, created_name, goods_variety, 
      updated_by, updated_name, gmt_create, 
      gmt_update, `status`, extend, 
      version, business_inner_code, business_inner_name
      )
    values (#{id,jdbcType=INTEGER}, #{loginName,jdbcType=VARCHAR}, #{accountNo,jdbcType=VARCHAR}, 
      #{accountName,jdbcType=VARCHAR}, #{parentAccountNo,jdbcType=VARCHAR}, #{accountPhone,jdbcType=VARCHAR}, 
      #{accountType,jdbcType=INTEGER}, #{accountBusinessType,jdbcType=INTEGER}, #{accountRegisterStatus,jdbcType=INTEGER}, 
      #{accountUseStatus,jdbcType=INTEGER}, #{accountExpirationType,jdbcType=INTEGER}, 
      #{accountExpirationBegin,jdbcType=TIMESTAMP}, #{accountExpirationEnd,jdbcType=TIMESTAMP}, 
      #{accountEmail,jdbcType=VARCHAR}, #{businessName,jdbcType=VARCHAR}, #{businessContacts,jdbcType=VARCHAR}, 
      #{innerContacts,jdbcType=VARCHAR}, #{purchaseTypeScope,jdbcType=VARCHAR}, #{accountClass,jdbcType=INTEGER}, 
      #{accountResponse,jdbcType=VARCHAR}, #{responsePhone,jdbcType=VARCHAR}, #{responseUserId,jdbcType=BIGINT}, 
      #{activateStatus,jdbcType=INTEGER}, #{activateCode,jdbcType=VARCHAR}, #{activateDateStart,jdbcType=TIMESTAMP}, 
      #{activateDateEnd,jdbcType=TIMESTAMP}, #{maxDownloadTime,jdbcType=INTEGER}, #{activateLimit,jdbcType=INTEGER}, 
      #{freezeLimit,jdbcType=INTEGER}, #{permissionId,jdbcType=BIGINT}, #{levelOrgType,jdbcType=INTEGER}, 
      #{levelOrgId,jdbcType=BIGINT}, #{cooperateCode,jdbcType=VARCHAR}, #{cooperateName,jdbcType=VARCHAR}, 
      #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{goodsVariety,jdbcType=VARCHAR}, 
      #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{businessInnerCode,jdbcType=VARCHAR}, #{businessInnerName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmPermAccountExtendInfo">
    insert into srm_perm_account_extend_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="loginName != null">
        login_name,
      </if>
      <if test="accountNo != null">
        account_no,
      </if>
      <if test="accountName != null">
        account_name,
      </if>
      <if test="parentAccountNo != null">
        parent_account_no,
      </if>
      <if test="accountPhone != null">
        account_phone,
      </if>
      <if test="accountType != null">
        account_type,
      </if>
      <if test="accountBusinessType != null">
        account_business_type,
      </if>
      <if test="accountRegisterStatus != null">
        account_register_status,
      </if>
      <if test="accountUseStatus != null">
        account_use_status,
      </if>
      <if test="accountExpirationType != null">
        account_expiration_type,
      </if>
      <if test="accountExpirationBegin != null">
        account_expiration_begin,
      </if>
      <if test="accountExpirationEnd != null">
        account_expiration_end,
      </if>
      <if test="accountEmail != null">
        account_email,
      </if>
      <if test="businessName != null">
        business_name,
      </if>
      <if test="businessContacts != null">
        business_contacts,
      </if>
      <if test="innerContacts != null">
        inner_contacts,
      </if>
      <if test="purchaseTypeScope != null">
        purchase_type_scope,
      </if>
      <if test="accountClass != null">
        account_class,
      </if>
      <if test="accountResponse != null">
        account_response,
      </if>
      <if test="responsePhone != null">
        response_phone,
      </if>
      <if test="responseUserId != null">
        response_user_id,
      </if>
      <if test="activateStatus != null">
        activate_status,
      </if>
      <if test="activateCode != null">
        activate_code,
      </if>
      <if test="activateDateStart != null">
        activate_date_start,
      </if>
      <if test="activateDateEnd != null">
        activate_date_end,
      </if>
      <if test="maxDownloadTime != null">
        max_download_time,
      </if>
      <if test="activateLimit != null">
        activate_limit,
      </if>
      <if test="freezeLimit != null">
        freeze_limit,
      </if>
      <if test="permissionId != null">
        permission_id,
      </if>
      <if test="levelOrgType != null">
        level_org_type,
      </if>
      <if test="levelOrgId != null">
        level_org_id,
      </if>
      <if test="cooperateCode != null">
        cooperate_code,
      </if>
      <if test="cooperateName != null">
        cooperate_name,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="goodsVariety != null">
        goods_variety,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="businessInnerCode != null">
        business_inner_code,
      </if>
      <if test="businessInnerName != null">
        business_inner_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="loginName != null">
        #{loginName,jdbcType=VARCHAR},
      </if>
      <if test="accountNo != null">
        #{accountNo,jdbcType=VARCHAR},
      </if>
      <if test="accountName != null">
        #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="parentAccountNo != null">
        #{parentAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="accountPhone != null">
        #{accountPhone,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=INTEGER},
      </if>
      <if test="accountBusinessType != null">
        #{accountBusinessType,jdbcType=INTEGER},
      </if>
      <if test="accountRegisterStatus != null">
        #{accountRegisterStatus,jdbcType=INTEGER},
      </if>
      <if test="accountUseStatus != null">
        #{accountUseStatus,jdbcType=INTEGER},
      </if>
      <if test="accountExpirationType != null">
        #{accountExpirationType,jdbcType=INTEGER},
      </if>
      <if test="accountExpirationBegin != null">
        #{accountExpirationBegin,jdbcType=TIMESTAMP},
      </if>
      <if test="accountExpirationEnd != null">
        #{accountExpirationEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="accountEmail != null">
        #{accountEmail,jdbcType=VARCHAR},
      </if>
      <if test="businessName != null">
        #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="businessContacts != null">
        #{businessContacts,jdbcType=VARCHAR},
      </if>
      <if test="innerContacts != null">
        #{innerContacts,jdbcType=VARCHAR},
      </if>
      <if test="purchaseTypeScope != null">
        #{purchaseTypeScope,jdbcType=VARCHAR},
      </if>
      <if test="accountClass != null">
        #{accountClass,jdbcType=INTEGER},
      </if>
      <if test="accountResponse != null">
        #{accountResponse,jdbcType=VARCHAR},
      </if>
      <if test="responsePhone != null">
        #{responsePhone,jdbcType=VARCHAR},
      </if>
      <if test="responseUserId != null">
        #{responseUserId,jdbcType=BIGINT},
      </if>
      <if test="activateStatus != null">
        #{activateStatus,jdbcType=INTEGER},
      </if>
      <if test="activateCode != null">
        #{activateCode,jdbcType=VARCHAR},
      </if>
      <if test="activateDateStart != null">
        #{activateDateStart,jdbcType=TIMESTAMP},
      </if>
      <if test="activateDateEnd != null">
        #{activateDateEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="maxDownloadTime != null">
        #{maxDownloadTime,jdbcType=INTEGER},
      </if>
      <if test="activateLimit != null">
        #{activateLimit,jdbcType=INTEGER},
      </if>
      <if test="freezeLimit != null">
        #{freezeLimit,jdbcType=INTEGER},
      </if>
      <if test="permissionId != null">
        #{permissionId,jdbcType=BIGINT},
      </if>
      <if test="levelOrgType != null">
        #{levelOrgType,jdbcType=INTEGER},
      </if>
      <if test="levelOrgId != null">
        #{levelOrgId,jdbcType=BIGINT},
      </if>
      <if test="cooperateCode != null">
        #{cooperateCode,jdbcType=VARCHAR},
      </if>
      <if test="cooperateName != null">
        #{cooperateName,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="goodsVariety != null">
        #{goodsVariety,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="businessInnerCode != null">
        #{businessInnerCode,jdbcType=VARCHAR},
      </if>
      <if test="businessInnerName != null">
        #{businessInnerName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmPermAccountExtendInfoExample" resultType="java.lang.Long">
    select count(*) from srm_perm_account_extend_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_perm_account_extend_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.loginName != null">
        login_name = #{record.loginName,jdbcType=VARCHAR},
      </if>
      <if test="record.accountNo != null">
        account_no = #{record.accountNo,jdbcType=VARCHAR},
      </if>
      <if test="record.accountName != null">
        account_name = #{record.accountName,jdbcType=VARCHAR},
      </if>
      <if test="record.parentAccountNo != null">
        parent_account_no = #{record.parentAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="record.accountPhone != null">
        account_phone = #{record.accountPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.accountType != null">
        account_type = #{record.accountType,jdbcType=INTEGER},
      </if>
      <if test="record.accountBusinessType != null">
        account_business_type = #{record.accountBusinessType,jdbcType=INTEGER},
      </if>
      <if test="record.accountRegisterStatus != null">
        account_register_status = #{record.accountRegisterStatus,jdbcType=INTEGER},
      </if>
      <if test="record.accountUseStatus != null">
        account_use_status = #{record.accountUseStatus,jdbcType=INTEGER},
      </if>
      <if test="record.accountExpirationType != null">
        account_expiration_type = #{record.accountExpirationType,jdbcType=INTEGER},
      </if>
      <if test="record.accountExpirationBegin != null">
        account_expiration_begin = #{record.accountExpirationBegin,jdbcType=TIMESTAMP},
      </if>
      <if test="record.accountExpirationEnd != null">
        account_expiration_end = #{record.accountExpirationEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="record.accountEmail != null">
        account_email = #{record.accountEmail,jdbcType=VARCHAR},
      </if>
      <if test="record.businessName != null">
        business_name = #{record.businessName,jdbcType=VARCHAR},
      </if>
      <if test="record.businessContacts != null">
        business_contacts = #{record.businessContacts,jdbcType=VARCHAR},
      </if>
      <if test="record.innerContacts != null">
        inner_contacts = #{record.innerContacts,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseTypeScope != null">
        purchase_type_scope = #{record.purchaseTypeScope,jdbcType=VARCHAR},
      </if>
      <if test="record.accountClass != null">
        account_class = #{record.accountClass,jdbcType=INTEGER},
      </if>
      <if test="record.accountResponse != null">
        account_response = #{record.accountResponse,jdbcType=VARCHAR},
      </if>
      <if test="record.responsePhone != null">
        response_phone = #{record.responsePhone,jdbcType=VARCHAR},
      </if>
      <if test="record.responseUserId != null">
        response_user_id = #{record.responseUserId,jdbcType=BIGINT},
      </if>
      <if test="record.activateStatus != null">
        activate_status = #{record.activateStatus,jdbcType=INTEGER},
      </if>
      <if test="record.activateCode != null">
        activate_code = #{record.activateCode,jdbcType=VARCHAR},
      </if>
      <if test="record.activateDateStart != null">
        activate_date_start = #{record.activateDateStart,jdbcType=TIMESTAMP},
      </if>
      <if test="record.activateDateEnd != null">
        activate_date_end = #{record.activateDateEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="record.maxDownloadTime != null">
        max_download_time = #{record.maxDownloadTime,jdbcType=INTEGER},
      </if>
      <if test="record.activateLimit != null">
        activate_limit = #{record.activateLimit,jdbcType=INTEGER},
      </if>
      <if test="record.freezeLimit != null">
        freeze_limit = #{record.freezeLimit,jdbcType=INTEGER},
      </if>
      <if test="record.permissionId != null">
        permission_id = #{record.permissionId,jdbcType=BIGINT},
      </if>
      <if test="record.levelOrgType != null">
        level_org_type = #{record.levelOrgType,jdbcType=INTEGER},
      </if>
      <if test="record.levelOrgId != null">
        level_org_id = #{record.levelOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.cooperateCode != null">
        cooperate_code = #{record.cooperateCode,jdbcType=VARCHAR},
      </if>
      <if test="record.cooperateName != null">
        cooperate_name = #{record.cooperateName,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsVariety != null">
        goods_variety = #{record.goodsVariety,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.businessInnerCode != null">
        business_inner_code = #{record.businessInnerCode,jdbcType=VARCHAR},
      </if>
      <if test="record.businessInnerName != null">
        business_inner_name = #{record.businessInnerName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_perm_account_extend_info
    set id = #{record.id,jdbcType=INTEGER},
      login_name = #{record.loginName,jdbcType=VARCHAR},
      account_no = #{record.accountNo,jdbcType=VARCHAR},
      account_name = #{record.accountName,jdbcType=VARCHAR},
      parent_account_no = #{record.parentAccountNo,jdbcType=VARCHAR},
      account_phone = #{record.accountPhone,jdbcType=VARCHAR},
      account_type = #{record.accountType,jdbcType=INTEGER},
      account_business_type = #{record.accountBusinessType,jdbcType=INTEGER},
      account_register_status = #{record.accountRegisterStatus,jdbcType=INTEGER},
      account_use_status = #{record.accountUseStatus,jdbcType=INTEGER},
      account_expiration_type = #{record.accountExpirationType,jdbcType=INTEGER},
      account_expiration_begin = #{record.accountExpirationBegin,jdbcType=TIMESTAMP},
      account_expiration_end = #{record.accountExpirationEnd,jdbcType=TIMESTAMP},
      account_email = #{record.accountEmail,jdbcType=VARCHAR},
      business_name = #{record.businessName,jdbcType=VARCHAR},
      business_contacts = #{record.businessContacts,jdbcType=VARCHAR},
      inner_contacts = #{record.innerContacts,jdbcType=VARCHAR},
      purchase_type_scope = #{record.purchaseTypeScope,jdbcType=VARCHAR},
      account_class = #{record.accountClass,jdbcType=INTEGER},
      account_response = #{record.accountResponse,jdbcType=VARCHAR},
      response_phone = #{record.responsePhone,jdbcType=VARCHAR},
      response_user_id = #{record.responseUserId,jdbcType=BIGINT},
      activate_status = #{record.activateStatus,jdbcType=INTEGER},
      activate_code = #{record.activateCode,jdbcType=VARCHAR},
      activate_date_start = #{record.activateDateStart,jdbcType=TIMESTAMP},
      activate_date_end = #{record.activateDateEnd,jdbcType=TIMESTAMP},
      max_download_time = #{record.maxDownloadTime,jdbcType=INTEGER},
      activate_limit = #{record.activateLimit,jdbcType=INTEGER},
      freeze_limit = #{record.freezeLimit,jdbcType=INTEGER},
      permission_id = #{record.permissionId,jdbcType=BIGINT},
      level_org_type = #{record.levelOrgType,jdbcType=INTEGER},
      level_org_id = #{record.levelOrgId,jdbcType=BIGINT},
      cooperate_code = #{record.cooperateCode,jdbcType=VARCHAR},
      cooperate_name = #{record.cooperateName,jdbcType=VARCHAR},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      goods_variety = #{record.goodsVariety,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      `status` = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      business_inner_code = #{record.businessInnerCode,jdbcType=VARCHAR},
      business_inner_name = #{record.businessInnerName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmPermAccountExtendInfo">
    update srm_perm_account_extend_info
    <set>
      <if test="loginName != null">
        login_name = #{loginName,jdbcType=VARCHAR},
      </if>
      <if test="accountNo != null">
        account_no = #{accountNo,jdbcType=VARCHAR},
      </if>
      <if test="accountName != null">
        account_name = #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="parentAccountNo != null">
        parent_account_no = #{parentAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="accountPhone != null">
        account_phone = #{accountPhone,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        account_type = #{accountType,jdbcType=INTEGER},
      </if>
      <if test="accountBusinessType != null">
        account_business_type = #{accountBusinessType,jdbcType=INTEGER},
      </if>
      <if test="accountRegisterStatus != null">
        account_register_status = #{accountRegisterStatus,jdbcType=INTEGER},
      </if>
      <if test="accountUseStatus != null">
        account_use_status = #{accountUseStatus,jdbcType=INTEGER},
      </if>
      <if test="accountExpirationType != null">
        account_expiration_type = #{accountExpirationType,jdbcType=INTEGER},
      </if>
      <if test="accountExpirationBegin != null">
        account_expiration_begin = #{accountExpirationBegin,jdbcType=TIMESTAMP},
      </if>
      <if test="accountExpirationEnd != null">
        account_expiration_end = #{accountExpirationEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="accountEmail != null">
        account_email = #{accountEmail,jdbcType=VARCHAR},
      </if>
      <if test="businessName != null">
        business_name = #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="businessContacts != null">
        business_contacts = #{businessContacts,jdbcType=VARCHAR},
      </if>
      <if test="innerContacts != null">
        inner_contacts = #{innerContacts,jdbcType=VARCHAR},
      </if>
      <if test="purchaseTypeScope != null">
        purchase_type_scope = #{purchaseTypeScope,jdbcType=VARCHAR},
      </if>
      <if test="accountClass != null">
        account_class = #{accountClass,jdbcType=INTEGER},
      </if>
      <if test="accountResponse != null">
        account_response = #{accountResponse,jdbcType=VARCHAR},
      </if>
      <if test="responsePhone != null">
        response_phone = #{responsePhone,jdbcType=VARCHAR},
      </if>
      <if test="responseUserId != null">
        response_user_id = #{responseUserId,jdbcType=BIGINT},
      </if>
      <if test="activateStatus != null">
        activate_status = #{activateStatus,jdbcType=INTEGER},
      </if>
      <if test="activateCode != null">
        activate_code = #{activateCode,jdbcType=VARCHAR},
      </if>
      <if test="activateDateStart != null">
        activate_date_start = #{activateDateStart,jdbcType=TIMESTAMP},
      </if>
      <if test="activateDateEnd != null">
        activate_date_end = #{activateDateEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="maxDownloadTime != null">
        max_download_time = #{maxDownloadTime,jdbcType=INTEGER},
      </if>
      <if test="activateLimit != null">
        activate_limit = #{activateLimit,jdbcType=INTEGER},
      </if>
      <if test="freezeLimit != null">
        freeze_limit = #{freezeLimit,jdbcType=INTEGER},
      </if>
      <if test="permissionId != null">
        permission_id = #{permissionId,jdbcType=BIGINT},
      </if>
      <if test="levelOrgType != null">
        level_org_type = #{levelOrgType,jdbcType=INTEGER},
      </if>
      <if test="levelOrgId != null">
        level_org_id = #{levelOrgId,jdbcType=BIGINT},
      </if>
      <if test="cooperateCode != null">
        cooperate_code = #{cooperateCode,jdbcType=VARCHAR},
      </if>
      <if test="cooperateName != null">
        cooperate_name = #{cooperateName,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="goodsVariety != null">
        goods_variety = #{goodsVariety,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="businessInnerCode != null">
        business_inner_code = #{businessInnerCode,jdbcType=VARCHAR},
      </if>
      <if test="businessInnerName != null">
        business_inner_name = #{businessInnerName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmPermAccountExtendInfo">
    update srm_perm_account_extend_info
    set login_name = #{loginName,jdbcType=VARCHAR},
      account_no = #{accountNo,jdbcType=VARCHAR},
      account_name = #{accountName,jdbcType=VARCHAR},
      parent_account_no = #{parentAccountNo,jdbcType=VARCHAR},
      account_phone = #{accountPhone,jdbcType=VARCHAR},
      account_type = #{accountType,jdbcType=INTEGER},
      account_business_type = #{accountBusinessType,jdbcType=INTEGER},
      account_register_status = #{accountRegisterStatus,jdbcType=INTEGER},
      account_use_status = #{accountUseStatus,jdbcType=INTEGER},
      account_expiration_type = #{accountExpirationType,jdbcType=INTEGER},
      account_expiration_begin = #{accountExpirationBegin,jdbcType=TIMESTAMP},
      account_expiration_end = #{accountExpirationEnd,jdbcType=TIMESTAMP},
      account_email = #{accountEmail,jdbcType=VARCHAR},
      business_name = #{businessName,jdbcType=VARCHAR},
      business_contacts = #{businessContacts,jdbcType=VARCHAR},
      inner_contacts = #{innerContacts,jdbcType=VARCHAR},
      purchase_type_scope = #{purchaseTypeScope,jdbcType=VARCHAR},
      account_class = #{accountClass,jdbcType=INTEGER},
      account_response = #{accountResponse,jdbcType=VARCHAR},
      response_phone = #{responsePhone,jdbcType=VARCHAR},
      response_user_id = #{responseUserId,jdbcType=BIGINT},
      activate_status = #{activateStatus,jdbcType=INTEGER},
      activate_code = #{activateCode,jdbcType=VARCHAR},
      activate_date_start = #{activateDateStart,jdbcType=TIMESTAMP},
      activate_date_end = #{activateDateEnd,jdbcType=TIMESTAMP},
      max_download_time = #{maxDownloadTime,jdbcType=INTEGER},
      activate_limit = #{activateLimit,jdbcType=INTEGER},
      freeze_limit = #{freezeLimit,jdbcType=INTEGER},
      permission_id = #{permissionId,jdbcType=BIGINT},
      level_org_type = #{levelOrgType,jdbcType=INTEGER},
      level_org_id = #{levelOrgId,jdbcType=BIGINT},
      cooperate_code = #{cooperateCode,jdbcType=VARCHAR},
      cooperate_name = #{cooperateName,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      goods_variety = #{goodsVariety,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      business_inner_code = #{businessInnerCode,jdbcType=VARCHAR},
      business_inner_name = #{businessInnerName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>