<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmEnquiryConfigValidityMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmEnquiryConfigValidity">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="bguid" jdbcType="VARCHAR" property="bguid" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="purchase_org_code" jdbcType="VARCHAR" property="purchaseOrgCode" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="validity_unit" jdbcType="VARCHAR" property="validityUnit" />
    <result column="validity_date" jdbcType="INTEGER" property="validityDate" />
    <result column="validity_level" jdbcType="INTEGER" property="validityLevel" />
    <result column="validity_lower" jdbcType="INTEGER" property="validityLower" />
    <result column="validity_upper" jdbcType="INTEGER" property="validityUpper" />
    <result column="validity_desc" jdbcType="VARCHAR" property="validityDesc" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, bguid, warehouse_code, purchase_org_code, company_code, validity_unit, validity_date, 
    validity_level, validity_lower, validity_upper, validity_desc, created_name, `status`, 
    create_date, create_time, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmEnquiryConfigValidityExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_enquiry_config_validity
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_enquiry_config_validity
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from srm_enquiry_config_validity
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmEnquiryConfigValidityExample">
    delete from srm_enquiry_config_validity
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmEnquiryConfigValidity">
    insert into srm_enquiry_config_validity (id, bguid, warehouse_code, 
      purchase_org_code, company_code, validity_unit, 
      validity_date, validity_level, validity_lower, 
      validity_upper, validity_desc, created_name, 
      `status`, create_date, create_time, 
      gmt_create, gmt_update)
    values (#{id,jdbcType=INTEGER}, #{bguid,jdbcType=VARCHAR}, #{warehouseCode,jdbcType=VARCHAR}, 
      #{purchaseOrgCode,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR}, #{validityUnit,jdbcType=VARCHAR}, 
      #{validityDate,jdbcType=INTEGER}, #{validityLevel,jdbcType=INTEGER}, #{validityLower,jdbcType=INTEGER}, 
      #{validityUpper,jdbcType=INTEGER}, #{validityDesc,jdbcType=VARCHAR}, #{createdName,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{createDate,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmEnquiryConfigValidity">
    insert into srm_enquiry_config_validity
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="bguid != null">
        bguid,
      </if>
      <if test="warehouseCode != null">
        warehouse_code,
      </if>
      <if test="purchaseOrgCode != null">
        purchase_org_code,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="validityUnit != null">
        validity_unit,
      </if>
      <if test="validityDate != null">
        validity_date,
      </if>
      <if test="validityLevel != null">
        validity_level,
      </if>
      <if test="validityLower != null">
        validity_lower,
      </if>
      <if test="validityUpper != null">
        validity_upper,
      </if>
      <if test="validityDesc != null">
        validity_desc,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="bguid != null">
        #{bguid,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrgCode != null">
        #{purchaseOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="validityUnit != null">
        #{validityUnit,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        #{validityDate,jdbcType=INTEGER},
      </if>
      <if test="validityLevel != null">
        #{validityLevel,jdbcType=INTEGER},
      </if>
      <if test="validityLower != null">
        #{validityLower,jdbcType=INTEGER},
      </if>
      <if test="validityUpper != null">
        #{validityUpper,jdbcType=INTEGER},
      </if>
      <if test="validityDesc != null">
        #{validityDesc,jdbcType=VARCHAR},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmEnquiryConfigValidityExample" resultType="java.lang.Long">
    select count(*) from srm_enquiry_config_validity
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_enquiry_config_validity
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.bguid != null">
        bguid = #{record.bguid,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseCode != null">
        warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrgCode != null">
        purchase_org_code = #{record.purchaseOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.validityUnit != null">
        validity_unit = #{record.validityUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.validityDate != null">
        validity_date = #{record.validityDate,jdbcType=INTEGER},
      </if>
      <if test="record.validityLevel != null">
        validity_level = #{record.validityLevel,jdbcType=INTEGER},
      </if>
      <if test="record.validityLower != null">
        validity_lower = #{record.validityLower,jdbcType=INTEGER},
      </if>
      <if test="record.validityUpper != null">
        validity_upper = #{record.validityUpper,jdbcType=INTEGER},
      </if>
      <if test="record.validityDesc != null">
        validity_desc = #{record.validityDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.createDate != null">
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_enquiry_config_validity
    set id = #{record.id,jdbcType=INTEGER},
      bguid = #{record.bguid,jdbcType=VARCHAR},
      warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      purchase_org_code = #{record.purchaseOrgCode,jdbcType=VARCHAR},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      validity_unit = #{record.validityUnit,jdbcType=VARCHAR},
      validity_date = #{record.validityDate,jdbcType=INTEGER},
      validity_level = #{record.validityLevel,jdbcType=INTEGER},
      validity_lower = #{record.validityLower,jdbcType=INTEGER},
      validity_upper = #{record.validityUpper,jdbcType=INTEGER},
      validity_desc = #{record.validityDesc,jdbcType=VARCHAR},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=TINYINT},
      create_date = #{record.createDate,jdbcType=TIMESTAMP},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmEnquiryConfigValidity">
    update srm_enquiry_config_validity
    <set>
      <if test="bguid != null">
        bguid = #{bguid,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrgCode != null">
        purchase_org_code = #{purchaseOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="validityUnit != null">
        validity_unit = #{validityUnit,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        validity_date = #{validityDate,jdbcType=INTEGER},
      </if>
      <if test="validityLevel != null">
        validity_level = #{validityLevel,jdbcType=INTEGER},
      </if>
      <if test="validityLower != null">
        validity_lower = #{validityLower,jdbcType=INTEGER},
      </if>
      <if test="validityUpper != null">
        validity_upper = #{validityUpper,jdbcType=INTEGER},
      </if>
      <if test="validityDesc != null">
        validity_desc = #{validityDesc,jdbcType=VARCHAR},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmEnquiryConfigValidity">
    update srm_enquiry_config_validity
    set bguid = #{bguid,jdbcType=VARCHAR},
      warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      purchase_org_code = #{purchaseOrgCode,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      validity_unit = #{validityUnit,jdbcType=VARCHAR},
      validity_date = #{validityDate,jdbcType=INTEGER},
      validity_level = #{validityLevel,jdbcType=INTEGER},
      validity_lower = #{validityLower,jdbcType=INTEGER},
      validity_upper = #{validityUpper,jdbcType=INTEGER},
      validity_desc = #{validityDesc,jdbcType=VARCHAR},
      created_name = #{createdName,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>