<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmPurchaseOrderDetailsMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmPurchaseOrderDetails">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="purchase_order_no" jdbcType="VARCHAR" property="purchaseOrderNo" />
    <result column="purchase_order_id" jdbcType="VARCHAR" property="purchaseOrderId" />
    <result column="order_details_key" jdbcType="VARCHAR" property="orderDetailsKey" />
    <result column="purchase_plan_no" jdbcType="VARCHAR" property="purchasePlanNo" />
    <result column="purchase_plan_id" jdbcType="VARCHAR" property="purchasePlanId" />
    <result column="purchase_plan_key" jdbcType="VARCHAR" property="purchasePlanKey" />
    <result column="purchase_delete_id" jdbcType="VARCHAR" property="purchaseDeleteId" />
    <result column="confirm_status" jdbcType="INTEGER" property="confirmStatus" />
    <result column="deliver_status" jdbcType="INTEGER" property="deliverStatus" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="goods_desc" jdbcType="VARCHAR" property="goodsDesc" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="warehouse" jdbcType="VARCHAR" property="warehouse" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="warehouse_addr" jdbcType="VARCHAR" property="warehouseAddr" />
    <result column="expect_date" jdbcType="TIMESTAMP" property="expectDate" />
    <result column="purchase_count" jdbcType="DECIMAL" property="purchaseCount" />
    <result column="can_send_quantity" jdbcType="DECIMAL" property="canSendQuantity" />
    <result column="this_send_quantity" jdbcType="DECIMAL" property="thisSendQuantity" />
    <result column="purchase_unit" jdbcType="VARCHAR" property="purchaseUnit" />
    <result column="no_tax_price" jdbcType="VARCHAR" property="noTaxPrice" />
    <result column="have_tax_price" jdbcType="VARCHAR" property="haveTaxPrice" />
    <result column="price_unit" jdbcType="VARCHAR" property="priceUnit" />
    <result column="no_tax_amount" jdbcType="VARCHAR" property="noTaxAmount" />
    <result column="tax_amount" jdbcType="VARCHAR" property="taxAmount" />
    <result column="tax_code" jdbcType="VARCHAR" property="taxCode" />
    <result column="tax_rate" jdbcType="VARCHAR" property="taxRate" />
    <result column="plan_no_tax_amount" jdbcType="DECIMAL" property="planNoTaxAmount" />
    <result column="plan_tax_amount" jdbcType="DECIMAL" property="planTaxAmount" />
    <result column="plan_have_tax_amount" jdbcType="DECIMAL" property="planHaveTaxAmount" />
    <result column="plan_no_tax_price" jdbcType="DECIMAL" property="planNoTaxPrice" />
    <result column="plan_have_tax_price" jdbcType="DECIMAL" property="planHaveTaxPrice" />
    <result column="plan_price_unit" jdbcType="VARCHAR" property="planPriceUnit" />
    <result column="plan_count" jdbcType="DECIMAL" property="planCount" />
    <result column="plan_tax_rate" jdbcType="VARCHAR" property="planTaxRate" />
    <result column="plan_expect_date" jdbcType="TIMESTAMP" property="planExpectDate" />
    <result column="confirm_count" jdbcType="DECIMAL" property="confirmCount" />
    <result column="deliver_quantity" jdbcType="DECIMAL" property="deliverQuantity" />
    <result column="confirm_have_tax_price" jdbcType="DECIMAL" property="confirmHaveTaxPrice" />
    <result column="confirm_no_tax_price" jdbcType="DECIMAL" property="confirmNoTaxPrice" />
    <result column="confirm_no_tax_amount" jdbcType="DECIMAL" property="confirmNoTaxAmount" />
    <result column="confirm_tax_amount" jdbcType="DECIMAL" property="confirmTaxAmount" />
    <result column="confirm_have_tax_amount" jdbcType="DECIMAL" property="confirmHaveTaxAmount" />
    <result column="confirm_tax_rate" jdbcType="VARCHAR" property="confirmTaxRate" />
    <result column="confirm_expect_date" jdbcType="TIMESTAMP" property="confirmExpectDate" />
    <result column="base_unit" jdbcType="VARCHAR" property="baseUnit" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="batch_code" jdbcType="VARCHAR" property="batchCode" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="comments" jdbcType="VARCHAR" property="comments" />
    <result column="bak1" jdbcType="VARCHAR" property="bak1" />
    <result column="bak2" jdbcType="VARCHAR" property="bak2" />
    <result column="bak3" jdbcType="VARCHAR" property="bak3" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, purchase_order_no, purchase_order_id, order_details_key, purchase_plan_no, purchase_plan_id, 
    purchase_plan_key, purchase_delete_id, confirm_status, deliver_status, org_id, org_name, 
    goods_code, goods_desc, bar_code, goods_common_name, goods_name, specifications, 
    dosage_form, manufacturer, warehouse, warehouse_name, warehouse_addr, expect_date, 
    purchase_count, can_send_quantity, this_send_quantity, purchase_unit, no_tax_price, 
    have_tax_price, price_unit, no_tax_amount, tax_amount, tax_code, tax_rate, plan_no_tax_amount, 
    plan_tax_amount, plan_have_tax_amount, plan_no_tax_price, plan_have_tax_price, plan_price_unit, 
    plan_count, plan_tax_rate, plan_expect_date, confirm_count, deliver_quantity, confirm_have_tax_price, 
    confirm_no_tax_price, confirm_no_tax_amount, confirm_tax_amount, confirm_have_tax_amount, 
    confirm_tax_rate, confirm_expect_date, base_unit, batch_no, batch_code, gmt_create, 
    gmt_update, `operator`, comments, bak1, bak2, bak3
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmPurchaseOrderDetailsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_purchase_order_details
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_purchase_order_details
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from srm_purchase_order_details
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmPurchaseOrderDetailsExample">
    delete from srm_purchase_order_details
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmPurchaseOrderDetails">
    insert into srm_purchase_order_details (id, purchase_order_no, purchase_order_id, 
      order_details_key, purchase_plan_no, purchase_plan_id, 
      purchase_plan_key, purchase_delete_id, confirm_status, 
      deliver_status, org_id, org_name, 
      goods_code, goods_desc, bar_code, 
      goods_common_name, goods_name, specifications, 
      dosage_form, manufacturer, warehouse, 
      warehouse_name, warehouse_addr, expect_date, 
      purchase_count, can_send_quantity, this_send_quantity, 
      purchase_unit, no_tax_price, have_tax_price, 
      price_unit, no_tax_amount, tax_amount, 
      tax_code, tax_rate, plan_no_tax_amount, 
      plan_tax_amount, plan_have_tax_amount, plan_no_tax_price, 
      plan_have_tax_price, plan_price_unit, plan_count, 
      plan_tax_rate, plan_expect_date, confirm_count, 
      deliver_quantity, confirm_have_tax_price, confirm_no_tax_price, 
      confirm_no_tax_amount, confirm_tax_amount, confirm_have_tax_amount, 
      confirm_tax_rate, confirm_expect_date, base_unit, 
      batch_no, batch_code, gmt_create, 
      gmt_update, `operator`, comments, 
      bak1, bak2, bak3)
    values (#{id,jdbcType=INTEGER}, #{purchaseOrderNo,jdbcType=VARCHAR}, #{purchaseOrderId,jdbcType=VARCHAR}, 
      #{orderDetailsKey,jdbcType=VARCHAR}, #{purchasePlanNo,jdbcType=VARCHAR}, #{purchasePlanId,jdbcType=VARCHAR}, 
      #{purchasePlanKey,jdbcType=VARCHAR}, #{purchaseDeleteId,jdbcType=VARCHAR}, #{confirmStatus,jdbcType=INTEGER}, 
      #{deliverStatus,jdbcType=INTEGER}, #{orgId,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR}, 
      #{goodsCode,jdbcType=VARCHAR}, #{goodsDesc,jdbcType=VARCHAR}, #{barCode,jdbcType=VARCHAR}, 
      #{goodsCommonName,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, #{specifications,jdbcType=VARCHAR}, 
      #{dosageForm,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, #{warehouse,jdbcType=VARCHAR}, 
      #{warehouseName,jdbcType=VARCHAR}, #{warehouseAddr,jdbcType=VARCHAR}, #{expectDate,jdbcType=TIMESTAMP}, 
      #{purchaseCount,jdbcType=DECIMAL}, #{canSendQuantity,jdbcType=DECIMAL}, #{thisSendQuantity,jdbcType=DECIMAL}, 
      #{purchaseUnit,jdbcType=VARCHAR}, #{noTaxPrice,jdbcType=VARCHAR}, #{haveTaxPrice,jdbcType=VARCHAR}, 
      #{priceUnit,jdbcType=VARCHAR}, #{noTaxAmount,jdbcType=VARCHAR}, #{taxAmount,jdbcType=VARCHAR}, 
      #{taxCode,jdbcType=VARCHAR}, #{taxRate,jdbcType=VARCHAR}, #{planNoTaxAmount,jdbcType=DECIMAL}, 
      #{planTaxAmount,jdbcType=DECIMAL}, #{planHaveTaxAmount,jdbcType=DECIMAL}, #{planNoTaxPrice,jdbcType=DECIMAL}, 
      #{planHaveTaxPrice,jdbcType=DECIMAL}, #{planPriceUnit,jdbcType=VARCHAR}, #{planCount,jdbcType=DECIMAL}, 
      #{planTaxRate,jdbcType=VARCHAR}, #{planExpectDate,jdbcType=TIMESTAMP}, #{confirmCount,jdbcType=DECIMAL}, 
      #{deliverQuantity,jdbcType=DECIMAL}, #{confirmHaveTaxPrice,jdbcType=DECIMAL}, #{confirmNoTaxPrice,jdbcType=DECIMAL}, 
      #{confirmNoTaxAmount,jdbcType=DECIMAL}, #{confirmTaxAmount,jdbcType=DECIMAL}, #{confirmHaveTaxAmount,jdbcType=DECIMAL}, 
      #{confirmTaxRate,jdbcType=VARCHAR}, #{confirmExpectDate,jdbcType=TIMESTAMP}, #{baseUnit,jdbcType=VARCHAR}, 
      #{batchNo,jdbcType=VARCHAR}, #{batchCode,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{operator,jdbcType=VARCHAR}, #{comments,jdbcType=VARCHAR}, 
      #{bak1,jdbcType=VARCHAR}, #{bak2,jdbcType=VARCHAR}, #{bak3,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmPurchaseOrderDetails">
    insert into srm_purchase_order_details
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="purchaseOrderNo != null">
        purchase_order_no,
      </if>
      <if test="purchaseOrderId != null">
        purchase_order_id,
      </if>
      <if test="orderDetailsKey != null">
        order_details_key,
      </if>
      <if test="purchasePlanNo != null">
        purchase_plan_no,
      </if>
      <if test="purchasePlanId != null">
        purchase_plan_id,
      </if>
      <if test="purchasePlanKey != null">
        purchase_plan_key,
      </if>
      <if test="purchaseDeleteId != null">
        purchase_delete_id,
      </if>
      <if test="confirmStatus != null">
        confirm_status,
      </if>
      <if test="deliverStatus != null">
        deliver_status,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="goodsCode != null">
        goods_code,
      </if>
      <if test="goodsDesc != null">
        goods_desc,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="goodsCommonName != null">
        goods_common_name,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="dosageForm != null">
        dosage_form,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="warehouse != null">
        warehouse,
      </if>
      <if test="warehouseName != null">
        warehouse_name,
      </if>
      <if test="warehouseAddr != null">
        warehouse_addr,
      </if>
      <if test="expectDate != null">
        expect_date,
      </if>
      <if test="purchaseCount != null">
        purchase_count,
      </if>
      <if test="canSendQuantity != null">
        can_send_quantity,
      </if>
      <if test="thisSendQuantity != null">
        this_send_quantity,
      </if>
      <if test="purchaseUnit != null">
        purchase_unit,
      </if>
      <if test="noTaxPrice != null">
        no_tax_price,
      </if>
      <if test="haveTaxPrice != null">
        have_tax_price,
      </if>
      <if test="priceUnit != null">
        price_unit,
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount,
      </if>
      <if test="taxAmount != null">
        tax_amount,
      </if>
      <if test="taxCode != null">
        tax_code,
      </if>
      <if test="taxRate != null">
        tax_rate,
      </if>
      <if test="planNoTaxAmount != null">
        plan_no_tax_amount,
      </if>
      <if test="planTaxAmount != null">
        plan_tax_amount,
      </if>
      <if test="planHaveTaxAmount != null">
        plan_have_tax_amount,
      </if>
      <if test="planNoTaxPrice != null">
        plan_no_tax_price,
      </if>
      <if test="planHaveTaxPrice != null">
        plan_have_tax_price,
      </if>
      <if test="planPriceUnit != null">
        plan_price_unit,
      </if>
      <if test="planCount != null">
        plan_count,
      </if>
      <if test="planTaxRate != null">
        plan_tax_rate,
      </if>
      <if test="planExpectDate != null">
        plan_expect_date,
      </if>
      <if test="confirmCount != null">
        confirm_count,
      </if>
      <if test="deliverQuantity != null">
        deliver_quantity,
      </if>
      <if test="confirmHaveTaxPrice != null">
        confirm_have_tax_price,
      </if>
      <if test="confirmNoTaxPrice != null">
        confirm_no_tax_price,
      </if>
      <if test="confirmNoTaxAmount != null">
        confirm_no_tax_amount,
      </if>
      <if test="confirmTaxAmount != null">
        confirm_tax_amount,
      </if>
      <if test="confirmHaveTaxAmount != null">
        confirm_have_tax_amount,
      </if>
      <if test="confirmTaxRate != null">
        confirm_tax_rate,
      </if>
      <if test="confirmExpectDate != null">
        confirm_expect_date,
      </if>
      <if test="baseUnit != null">
        base_unit,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="batchCode != null">
        batch_code,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="operator != null">
        `operator`,
      </if>
      <if test="comments != null">
        comments,
      </if>
      <if test="bak1 != null">
        bak1,
      </if>
      <if test="bak2 != null">
        bak2,
      </if>
      <if test="bak3 != null">
        bak3,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="purchaseOrderNo != null">
        #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderId != null">
        #{purchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderDetailsKey != null">
        #{orderDetailsKey,jdbcType=VARCHAR},
      </if>
      <if test="purchasePlanNo != null">
        #{purchasePlanNo,jdbcType=VARCHAR},
      </if>
      <if test="purchasePlanId != null">
        #{purchasePlanId,jdbcType=VARCHAR},
      </if>
      <if test="purchasePlanKey != null">
        #{purchasePlanKey,jdbcType=VARCHAR},
      </if>
      <if test="purchaseDeleteId != null">
        #{purchaseDeleteId,jdbcType=VARCHAR},
      </if>
      <if test="confirmStatus != null">
        #{confirmStatus,jdbcType=INTEGER},
      </if>
      <if test="deliverStatus != null">
        #{deliverStatus,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsDesc != null">
        #{goodsDesc,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="warehouse != null">
        #{warehouse,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseAddr != null">
        #{warehouseAddr,jdbcType=VARCHAR},
      </if>
      <if test="expectDate != null">
        #{expectDate,jdbcType=TIMESTAMP},
      </if>
      <if test="purchaseCount != null">
        #{purchaseCount,jdbcType=DECIMAL},
      </if>
      <if test="canSendQuantity != null">
        #{canSendQuantity,jdbcType=DECIMAL},
      </if>
      <if test="thisSendQuantity != null">
        #{thisSendQuantity,jdbcType=DECIMAL},
      </if>
      <if test="purchaseUnit != null">
        #{purchaseUnit,jdbcType=VARCHAR},
      </if>
      <if test="noTaxPrice != null">
        #{noTaxPrice,jdbcType=VARCHAR},
      </if>
      <if test="haveTaxPrice != null">
        #{haveTaxPrice,jdbcType=VARCHAR},
      </if>
      <if test="priceUnit != null">
        #{priceUnit,jdbcType=VARCHAR},
      </if>
      <if test="noTaxAmount != null">
        #{noTaxAmount,jdbcType=VARCHAR},
      </if>
      <if test="taxAmount != null">
        #{taxAmount,jdbcType=VARCHAR},
      </if>
      <if test="taxCode != null">
        #{taxCode,jdbcType=VARCHAR},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=VARCHAR},
      </if>
      <if test="planNoTaxAmount != null">
        #{planNoTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="planTaxAmount != null">
        #{planTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="planHaveTaxAmount != null">
        #{planHaveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="planNoTaxPrice != null">
        #{planNoTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="planHaveTaxPrice != null">
        #{planHaveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="planPriceUnit != null">
        #{planPriceUnit,jdbcType=VARCHAR},
      </if>
      <if test="planCount != null">
        #{planCount,jdbcType=DECIMAL},
      </if>
      <if test="planTaxRate != null">
        #{planTaxRate,jdbcType=VARCHAR},
      </if>
      <if test="planExpectDate != null">
        #{planExpectDate,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmCount != null">
        #{confirmCount,jdbcType=DECIMAL},
      </if>
      <if test="deliverQuantity != null">
        #{deliverQuantity,jdbcType=DECIMAL},
      </if>
      <if test="confirmHaveTaxPrice != null">
        #{confirmHaveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="confirmNoTaxPrice != null">
        #{confirmNoTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="confirmNoTaxAmount != null">
        #{confirmNoTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="confirmTaxAmount != null">
        #{confirmTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="confirmHaveTaxAmount != null">
        #{confirmHaveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="confirmTaxRate != null">
        #{confirmTaxRate,jdbcType=VARCHAR},
      </if>
      <if test="confirmExpectDate != null">
        #{confirmExpectDate,jdbcType=TIMESTAMP},
      </if>
      <if test="baseUnit != null">
        #{baseUnit,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="batchCode != null">
        #{batchCode,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="bak1 != null">
        #{bak1,jdbcType=VARCHAR},
      </if>
      <if test="bak2 != null">
        #{bak2,jdbcType=VARCHAR},
      </if>
      <if test="bak3 != null">
        #{bak3,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmPurchaseOrderDetailsExample" resultType="java.lang.Long">
    select count(*) from srm_purchase_order_details
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_purchase_order_details
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.purchaseOrderNo != null">
        purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrderId != null">
        purchase_order_id = #{record.purchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderDetailsKey != null">
        order_details_key = #{record.orderDetailsKey,jdbcType=VARCHAR},
      </if>
      <if test="record.purchasePlanNo != null">
        purchase_plan_no = #{record.purchasePlanNo,jdbcType=VARCHAR},
      </if>
      <if test="record.purchasePlanId != null">
        purchase_plan_id = #{record.purchasePlanId,jdbcType=VARCHAR},
      </if>
      <if test="record.purchasePlanKey != null">
        purchase_plan_key = #{record.purchasePlanKey,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseDeleteId != null">
        purchase_delete_id = #{record.purchaseDeleteId,jdbcType=VARCHAR},
      </if>
      <if test="record.confirmStatus != null">
        confirm_status = #{record.confirmStatus,jdbcType=INTEGER},
      </if>
      <if test="record.deliverStatus != null">
        deliver_status = #{record.deliverStatus,jdbcType=INTEGER},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=VARCHAR},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCode != null">
        goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsDesc != null">
        goods_desc = #{record.goodsDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCommonName != null">
        goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.dosageForm != null">
        dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouse != null">
        warehouse = #{record.warehouse,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseName != null">
        warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseAddr != null">
        warehouse_addr = #{record.warehouseAddr,jdbcType=VARCHAR},
      </if>
      <if test="record.expectDate != null">
        expect_date = #{record.expectDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.purchaseCount != null">
        purchase_count = #{record.purchaseCount,jdbcType=DECIMAL},
      </if>
      <if test="record.canSendQuantity != null">
        can_send_quantity = #{record.canSendQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.thisSendQuantity != null">
        this_send_quantity = #{record.thisSendQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.purchaseUnit != null">
        purchase_unit = #{record.purchaseUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.noTaxPrice != null">
        no_tax_price = #{record.noTaxPrice,jdbcType=VARCHAR},
      </if>
      <if test="record.haveTaxPrice != null">
        have_tax_price = #{record.haveTaxPrice,jdbcType=VARCHAR},
      </if>
      <if test="record.priceUnit != null">
        price_unit = #{record.priceUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.noTaxAmount != null">
        no_tax_amount = #{record.noTaxAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.taxAmount != null">
        tax_amount = #{record.taxAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.taxCode != null">
        tax_code = #{record.taxCode,jdbcType=VARCHAR},
      </if>
      <if test="record.taxRate != null">
        tax_rate = #{record.taxRate,jdbcType=VARCHAR},
      </if>
      <if test="record.planNoTaxAmount != null">
        plan_no_tax_amount = #{record.planNoTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.planTaxAmount != null">
        plan_tax_amount = #{record.planTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.planHaveTaxAmount != null">
        plan_have_tax_amount = #{record.planHaveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.planNoTaxPrice != null">
        plan_no_tax_price = #{record.planNoTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.planHaveTaxPrice != null">
        plan_have_tax_price = #{record.planHaveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.planPriceUnit != null">
        plan_price_unit = #{record.planPriceUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.planCount != null">
        plan_count = #{record.planCount,jdbcType=DECIMAL},
      </if>
      <if test="record.planTaxRate != null">
        plan_tax_rate = #{record.planTaxRate,jdbcType=VARCHAR},
      </if>
      <if test="record.planExpectDate != null">
        plan_expect_date = #{record.planExpectDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.confirmCount != null">
        confirm_count = #{record.confirmCount,jdbcType=DECIMAL},
      </if>
      <if test="record.deliverQuantity != null">
        deliver_quantity = #{record.deliverQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.confirmHaveTaxPrice != null">
        confirm_have_tax_price = #{record.confirmHaveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.confirmNoTaxPrice != null">
        confirm_no_tax_price = #{record.confirmNoTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.confirmNoTaxAmount != null">
        confirm_no_tax_amount = #{record.confirmNoTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.confirmTaxAmount != null">
        confirm_tax_amount = #{record.confirmTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.confirmHaveTaxAmount != null">
        confirm_have_tax_amount = #{record.confirmHaveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.confirmTaxRate != null">
        confirm_tax_rate = #{record.confirmTaxRate,jdbcType=VARCHAR},
      </if>
      <if test="record.confirmExpectDate != null">
        confirm_expect_date = #{record.confirmExpectDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.baseUnit != null">
        base_unit = #{record.baseUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.batchCode != null">
        batch_code = #{record.batchCode,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operator != null">
        `operator` = #{record.operator,jdbcType=VARCHAR},
      </if>
      <if test="record.comments != null">
        comments = #{record.comments,jdbcType=VARCHAR},
      </if>
      <if test="record.bak1 != null">
        bak1 = #{record.bak1,jdbcType=VARCHAR},
      </if>
      <if test="record.bak2 != null">
        bak2 = #{record.bak2,jdbcType=VARCHAR},
      </if>
      <if test="record.bak3 != null">
        bak3 = #{record.bak3,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_purchase_order_details
    set id = #{record.id,jdbcType=INTEGER},
      purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      purchase_order_id = #{record.purchaseOrderId,jdbcType=VARCHAR},
      order_details_key = #{record.orderDetailsKey,jdbcType=VARCHAR},
      purchase_plan_no = #{record.purchasePlanNo,jdbcType=VARCHAR},
      purchase_plan_id = #{record.purchasePlanId,jdbcType=VARCHAR},
      purchase_plan_key = #{record.purchasePlanKey,jdbcType=VARCHAR},
      purchase_delete_id = #{record.purchaseDeleteId,jdbcType=VARCHAR},
      confirm_status = #{record.confirmStatus,jdbcType=INTEGER},
      deliver_status = #{record.deliverStatus,jdbcType=INTEGER},
      org_id = #{record.orgId,jdbcType=VARCHAR},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      goods_desc = #{record.goodsDesc,jdbcType=VARCHAR},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      specifications = #{record.specifications,jdbcType=VARCHAR},
      dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      warehouse = #{record.warehouse,jdbcType=VARCHAR},
      warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      warehouse_addr = #{record.warehouseAddr,jdbcType=VARCHAR},
      expect_date = #{record.expectDate,jdbcType=TIMESTAMP},
      purchase_count = #{record.purchaseCount,jdbcType=DECIMAL},
      can_send_quantity = #{record.canSendQuantity,jdbcType=DECIMAL},
      this_send_quantity = #{record.thisSendQuantity,jdbcType=DECIMAL},
      purchase_unit = #{record.purchaseUnit,jdbcType=VARCHAR},
      no_tax_price = #{record.noTaxPrice,jdbcType=VARCHAR},
      have_tax_price = #{record.haveTaxPrice,jdbcType=VARCHAR},
      price_unit = #{record.priceUnit,jdbcType=VARCHAR},
      no_tax_amount = #{record.noTaxAmount,jdbcType=VARCHAR},
      tax_amount = #{record.taxAmount,jdbcType=VARCHAR},
      tax_code = #{record.taxCode,jdbcType=VARCHAR},
      tax_rate = #{record.taxRate,jdbcType=VARCHAR},
      plan_no_tax_amount = #{record.planNoTaxAmount,jdbcType=DECIMAL},
      plan_tax_amount = #{record.planTaxAmount,jdbcType=DECIMAL},
      plan_have_tax_amount = #{record.planHaveTaxAmount,jdbcType=DECIMAL},
      plan_no_tax_price = #{record.planNoTaxPrice,jdbcType=DECIMAL},
      plan_have_tax_price = #{record.planHaveTaxPrice,jdbcType=DECIMAL},
      plan_price_unit = #{record.planPriceUnit,jdbcType=VARCHAR},
      plan_count = #{record.planCount,jdbcType=DECIMAL},
      plan_tax_rate = #{record.planTaxRate,jdbcType=VARCHAR},
      plan_expect_date = #{record.planExpectDate,jdbcType=TIMESTAMP},
      confirm_count = #{record.confirmCount,jdbcType=DECIMAL},
      deliver_quantity = #{record.deliverQuantity,jdbcType=DECIMAL},
      confirm_have_tax_price = #{record.confirmHaveTaxPrice,jdbcType=DECIMAL},
      confirm_no_tax_price = #{record.confirmNoTaxPrice,jdbcType=DECIMAL},
      confirm_no_tax_amount = #{record.confirmNoTaxAmount,jdbcType=DECIMAL},
      confirm_tax_amount = #{record.confirmTaxAmount,jdbcType=DECIMAL},
      confirm_have_tax_amount = #{record.confirmHaveTaxAmount,jdbcType=DECIMAL},
      confirm_tax_rate = #{record.confirmTaxRate,jdbcType=VARCHAR},
      confirm_expect_date = #{record.confirmExpectDate,jdbcType=TIMESTAMP},
      base_unit = #{record.baseUnit,jdbcType=VARCHAR},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      batch_code = #{record.batchCode,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      `operator` = #{record.operator,jdbcType=VARCHAR},
      comments = #{record.comments,jdbcType=VARCHAR},
      bak1 = #{record.bak1,jdbcType=VARCHAR},
      bak2 = #{record.bak2,jdbcType=VARCHAR},
      bak3 = #{record.bak3,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmPurchaseOrderDetails">
    update srm_purchase_order_details
    <set>
      <if test="purchaseOrderNo != null">
        purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderId != null">
        purchase_order_id = #{purchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderDetailsKey != null">
        order_details_key = #{orderDetailsKey,jdbcType=VARCHAR},
      </if>
      <if test="purchasePlanNo != null">
        purchase_plan_no = #{purchasePlanNo,jdbcType=VARCHAR},
      </if>
      <if test="purchasePlanId != null">
        purchase_plan_id = #{purchasePlanId,jdbcType=VARCHAR},
      </if>
      <if test="purchasePlanKey != null">
        purchase_plan_key = #{purchasePlanKey,jdbcType=VARCHAR},
      </if>
      <if test="purchaseDeleteId != null">
        purchase_delete_id = #{purchaseDeleteId,jdbcType=VARCHAR},
      </if>
      <if test="confirmStatus != null">
        confirm_status = #{confirmStatus,jdbcType=INTEGER},
      </if>
      <if test="deliverStatus != null">
        deliver_status = #{deliverStatus,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        goods_code = #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsDesc != null">
        goods_desc = #{goodsDesc,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        dosage_form = #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="warehouse != null">
        warehouse = #{warehouse,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseAddr != null">
        warehouse_addr = #{warehouseAddr,jdbcType=VARCHAR},
      </if>
      <if test="expectDate != null">
        expect_date = #{expectDate,jdbcType=TIMESTAMP},
      </if>
      <if test="purchaseCount != null">
        purchase_count = #{purchaseCount,jdbcType=DECIMAL},
      </if>
      <if test="canSendQuantity != null">
        can_send_quantity = #{canSendQuantity,jdbcType=DECIMAL},
      </if>
      <if test="thisSendQuantity != null">
        this_send_quantity = #{thisSendQuantity,jdbcType=DECIMAL},
      </if>
      <if test="purchaseUnit != null">
        purchase_unit = #{purchaseUnit,jdbcType=VARCHAR},
      </if>
      <if test="noTaxPrice != null">
        no_tax_price = #{noTaxPrice,jdbcType=VARCHAR},
      </if>
      <if test="haveTaxPrice != null">
        have_tax_price = #{haveTaxPrice,jdbcType=VARCHAR},
      </if>
      <if test="priceUnit != null">
        price_unit = #{priceUnit,jdbcType=VARCHAR},
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount = #{noTaxAmount,jdbcType=VARCHAR},
      </if>
      <if test="taxAmount != null">
        tax_amount = #{taxAmount,jdbcType=VARCHAR},
      </if>
      <if test="taxCode != null">
        tax_code = #{taxCode,jdbcType=VARCHAR},
      </if>
      <if test="taxRate != null">
        tax_rate = #{taxRate,jdbcType=VARCHAR},
      </if>
      <if test="planNoTaxAmount != null">
        plan_no_tax_amount = #{planNoTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="planTaxAmount != null">
        plan_tax_amount = #{planTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="planHaveTaxAmount != null">
        plan_have_tax_amount = #{planHaveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="planNoTaxPrice != null">
        plan_no_tax_price = #{planNoTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="planHaveTaxPrice != null">
        plan_have_tax_price = #{planHaveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="planPriceUnit != null">
        plan_price_unit = #{planPriceUnit,jdbcType=VARCHAR},
      </if>
      <if test="planCount != null">
        plan_count = #{planCount,jdbcType=DECIMAL},
      </if>
      <if test="planTaxRate != null">
        plan_tax_rate = #{planTaxRate,jdbcType=VARCHAR},
      </if>
      <if test="planExpectDate != null">
        plan_expect_date = #{planExpectDate,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmCount != null">
        confirm_count = #{confirmCount,jdbcType=DECIMAL},
      </if>
      <if test="deliverQuantity != null">
        deliver_quantity = #{deliverQuantity,jdbcType=DECIMAL},
      </if>
      <if test="confirmHaveTaxPrice != null">
        confirm_have_tax_price = #{confirmHaveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="confirmNoTaxPrice != null">
        confirm_no_tax_price = #{confirmNoTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="confirmNoTaxAmount != null">
        confirm_no_tax_amount = #{confirmNoTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="confirmTaxAmount != null">
        confirm_tax_amount = #{confirmTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="confirmHaveTaxAmount != null">
        confirm_have_tax_amount = #{confirmHaveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="confirmTaxRate != null">
        confirm_tax_rate = #{confirmTaxRate,jdbcType=VARCHAR},
      </if>
      <if test="confirmExpectDate != null">
        confirm_expect_date = #{confirmExpectDate,jdbcType=TIMESTAMP},
      </if>
      <if test="baseUnit != null">
        base_unit = #{baseUnit,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="batchCode != null">
        batch_code = #{batchCode,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="operator != null">
        `operator` = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        comments = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="bak1 != null">
        bak1 = #{bak1,jdbcType=VARCHAR},
      </if>
      <if test="bak2 != null">
        bak2 = #{bak2,jdbcType=VARCHAR},
      </if>
      <if test="bak3 != null">
        bak3 = #{bak3,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmPurchaseOrderDetails">
    update srm_purchase_order_details
    set purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      purchase_order_id = #{purchaseOrderId,jdbcType=VARCHAR},
      order_details_key = #{orderDetailsKey,jdbcType=VARCHAR},
      purchase_plan_no = #{purchasePlanNo,jdbcType=VARCHAR},
      purchase_plan_id = #{purchasePlanId,jdbcType=VARCHAR},
      purchase_plan_key = #{purchasePlanKey,jdbcType=VARCHAR},
      purchase_delete_id = #{purchaseDeleteId,jdbcType=VARCHAR},
      confirm_status = #{confirmStatus,jdbcType=INTEGER},
      deliver_status = #{deliverStatus,jdbcType=INTEGER},
      org_id = #{orgId,jdbcType=VARCHAR},
      org_name = #{orgName,jdbcType=VARCHAR},
      goods_code = #{goodsCode,jdbcType=VARCHAR},
      goods_desc = #{goodsDesc,jdbcType=VARCHAR},
      bar_code = #{barCode,jdbcType=VARCHAR},
      goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      specifications = #{specifications,jdbcType=VARCHAR},
      dosage_form = #{dosageForm,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      warehouse = #{warehouse,jdbcType=VARCHAR},
      warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      warehouse_addr = #{warehouseAddr,jdbcType=VARCHAR},
      expect_date = #{expectDate,jdbcType=TIMESTAMP},
      purchase_count = #{purchaseCount,jdbcType=DECIMAL},
      can_send_quantity = #{canSendQuantity,jdbcType=DECIMAL},
      this_send_quantity = #{thisSendQuantity,jdbcType=DECIMAL},
      purchase_unit = #{purchaseUnit,jdbcType=VARCHAR},
      no_tax_price = #{noTaxPrice,jdbcType=VARCHAR},
      have_tax_price = #{haveTaxPrice,jdbcType=VARCHAR},
      price_unit = #{priceUnit,jdbcType=VARCHAR},
      no_tax_amount = #{noTaxAmount,jdbcType=VARCHAR},
      tax_amount = #{taxAmount,jdbcType=VARCHAR},
      tax_code = #{taxCode,jdbcType=VARCHAR},
      tax_rate = #{taxRate,jdbcType=VARCHAR},
      plan_no_tax_amount = #{planNoTaxAmount,jdbcType=DECIMAL},
      plan_tax_amount = #{planTaxAmount,jdbcType=DECIMAL},
      plan_have_tax_amount = #{planHaveTaxAmount,jdbcType=DECIMAL},
      plan_no_tax_price = #{planNoTaxPrice,jdbcType=DECIMAL},
      plan_have_tax_price = #{planHaveTaxPrice,jdbcType=DECIMAL},
      plan_price_unit = #{planPriceUnit,jdbcType=VARCHAR},
      plan_count = #{planCount,jdbcType=DECIMAL},
      plan_tax_rate = #{planTaxRate,jdbcType=VARCHAR},
      plan_expect_date = #{planExpectDate,jdbcType=TIMESTAMP},
      confirm_count = #{confirmCount,jdbcType=DECIMAL},
      deliver_quantity = #{deliverQuantity,jdbcType=DECIMAL},
      confirm_have_tax_price = #{confirmHaveTaxPrice,jdbcType=DECIMAL},
      confirm_no_tax_price = #{confirmNoTaxPrice,jdbcType=DECIMAL},
      confirm_no_tax_amount = #{confirmNoTaxAmount,jdbcType=DECIMAL},
      confirm_tax_amount = #{confirmTaxAmount,jdbcType=DECIMAL},
      confirm_have_tax_amount = #{confirmHaveTaxAmount,jdbcType=DECIMAL},
      confirm_tax_rate = #{confirmTaxRate,jdbcType=VARCHAR},
      confirm_expect_date = #{confirmExpectDate,jdbcType=TIMESTAMP},
      base_unit = #{baseUnit,jdbcType=VARCHAR},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      batch_code = #{batchCode,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      `operator` = #{operator,jdbcType=VARCHAR},
      comments = #{comments,jdbcType=VARCHAR},
      bak1 = #{bak1,jdbcType=VARCHAR},
      bak2 = #{bak2,jdbcType=VARCHAR},
      bak3 = #{bak3,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>