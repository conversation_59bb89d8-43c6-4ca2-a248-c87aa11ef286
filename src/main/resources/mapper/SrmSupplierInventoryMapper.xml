<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmSupplierInventoryMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmSupplierInventory">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="supplier_no" jdbcType="VARCHAR" property="supplierNo" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="supplier_goods_code" jdbcType="VARCHAR" property="supplierGoodsCode" />
    <result column="supplier_goods_key" jdbcType="VARCHAR" property="supplierGoodsKey" />
    <result column="supplier_goods_name" jdbcType="VARCHAR" property="supplierGoodsName" />
    <result column="supplier_bar_code" jdbcType="VARCHAR" property="supplierBarCode" />
    <result column="supplier_manufacturer" jdbcType="VARCHAR" property="supplierManufacturer" />
    <result column="supplier_habitat" jdbcType="VARCHAR" property="supplierHabitat" />
    <result column="supplier_specifications" jdbcType="VARCHAR" property="supplierSpecifications" />
    <result column="supplier_unit" jdbcType="VARCHAR" property="supplierUnit" />
    <result column="supplier_mid_package_num" jdbcType="INTEGER" property="supplierMidPackageNum" />
    <result column="supplier_big_package_num" jdbcType="INTEGER" property="supplierBigPackageNum" />
    <result column="is_piece" jdbcType="VARCHAR" property="isPiece" />
    <result column="supplier_inventory_num" jdbcType="DECIMAL" property="supplierInventoryNum" />
    <result column="mdm_inventory_num" jdbcType="DECIMAL" property="mdmInventoryNum" />
    <result column="supplier_quote_price" jdbcType="DECIMAL" property="supplierQuotePrice" />
    <result column="mdm_quote_price" jdbcType="DECIMAL" property="mdmQuotePrice" />
    <result column="inventory_date" jdbcType="TIMESTAMP" property="inventoryDate" />
    <result column="validity_date" jdbcType="TIMESTAMP" property="validityDate" />
    <result column="produce_date" jdbcType="TIMESTAMP" property="produceDate" />
    <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, supplier_no, goods_code, supplier_goods_code, supplier_goods_key, supplier_goods_name, 
    supplier_bar_code, supplier_manufacturer, supplier_habitat, supplier_specifications, 
    supplier_unit, supplier_mid_package_num, supplier_big_package_num, is_piece, supplier_inventory_num, 
    mdm_inventory_num, supplier_quote_price, mdm_quote_price, inventory_date, validity_date, 
    produce_date, approval_number, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmSupplierInventoryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_supplier_inventory
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_supplier_inventory
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from srm_supplier_inventory
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmSupplierInventoryExample">
    delete from srm_supplier_inventory
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmSupplierInventory">
    insert into srm_supplier_inventory (id, supplier_no, goods_code, 
      supplier_goods_code, supplier_goods_key, supplier_goods_name, 
      supplier_bar_code, supplier_manufacturer, 
      supplier_habitat, supplier_specifications, 
      supplier_unit, supplier_mid_package_num, supplier_big_package_num, 
      is_piece, supplier_inventory_num, mdm_inventory_num, 
      supplier_quote_price, mdm_quote_price, inventory_date, 
      validity_date, produce_date, approval_number, 
      gmt_create, gmt_update)
    values (#{id,jdbcType=BIGINT}, #{supplierNo,jdbcType=VARCHAR}, #{goodsCode,jdbcType=VARCHAR}, 
      #{supplierGoodsCode,jdbcType=VARCHAR}, #{supplierGoodsKey,jdbcType=VARCHAR}, #{supplierGoodsName,jdbcType=VARCHAR}, 
      #{supplierBarCode,jdbcType=VARCHAR}, #{supplierManufacturer,jdbcType=VARCHAR}, 
      #{supplierHabitat,jdbcType=VARCHAR}, #{supplierSpecifications,jdbcType=VARCHAR}, 
      #{supplierUnit,jdbcType=VARCHAR}, #{supplierMidPackageNum,jdbcType=INTEGER}, #{supplierBigPackageNum,jdbcType=INTEGER}, 
      #{isPiece,jdbcType=VARCHAR}, #{supplierInventoryNum,jdbcType=DECIMAL}, #{mdmInventoryNum,jdbcType=DECIMAL}, 
      #{supplierQuotePrice,jdbcType=DECIMAL}, #{mdmQuotePrice,jdbcType=DECIMAL}, #{inventoryDate,jdbcType=TIMESTAMP}, 
      #{validityDate,jdbcType=TIMESTAMP}, #{produceDate,jdbcType=TIMESTAMP}, #{approvalNumber,jdbcType=VARCHAR}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmSupplierInventory">
    insert into srm_supplier_inventory
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="supplierNo != null">
        supplier_no,
      </if>
      <if test="goodsCode != null">
        goods_code,
      </if>
      <if test="supplierGoodsCode != null">
        supplier_goods_code,
      </if>
      <if test="supplierGoodsKey != null">
        supplier_goods_key,
      </if>
      <if test="supplierGoodsName != null">
        supplier_goods_name,
      </if>
      <if test="supplierBarCode != null">
        supplier_bar_code,
      </if>
      <if test="supplierManufacturer != null">
        supplier_manufacturer,
      </if>
      <if test="supplierHabitat != null">
        supplier_habitat,
      </if>
      <if test="supplierSpecifications != null">
        supplier_specifications,
      </if>
      <if test="supplierUnit != null">
        supplier_unit,
      </if>
      <if test="supplierMidPackageNum != null">
        supplier_mid_package_num,
      </if>
      <if test="supplierBigPackageNum != null">
        supplier_big_package_num,
      </if>
      <if test="isPiece != null">
        is_piece,
      </if>
      <if test="supplierInventoryNum != null">
        supplier_inventory_num,
      </if>
      <if test="mdmInventoryNum != null">
        mdm_inventory_num,
      </if>
      <if test="supplierQuotePrice != null">
        supplier_quote_price,
      </if>
      <if test="mdmQuotePrice != null">
        mdm_quote_price,
      </if>
      <if test="inventoryDate != null">
        inventory_date,
      </if>
      <if test="validityDate != null">
        validity_date,
      </if>
      <if test="produceDate != null">
        produce_date,
      </if>
      <if test="approvalNumber != null">
        approval_number,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="supplierNo != null">
        #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierGoodsCode != null">
        #{supplierGoodsCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierGoodsKey != null">
        #{supplierGoodsKey,jdbcType=VARCHAR},
      </if>
      <if test="supplierGoodsName != null">
        #{supplierGoodsName,jdbcType=VARCHAR},
      </if>
      <if test="supplierBarCode != null">
        #{supplierBarCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierManufacturer != null">
        #{supplierManufacturer,jdbcType=VARCHAR},
      </if>
      <if test="supplierHabitat != null">
        #{supplierHabitat,jdbcType=VARCHAR},
      </if>
      <if test="supplierSpecifications != null">
        #{supplierSpecifications,jdbcType=VARCHAR},
      </if>
      <if test="supplierUnit != null">
        #{supplierUnit,jdbcType=VARCHAR},
      </if>
      <if test="supplierMidPackageNum != null">
        #{supplierMidPackageNum,jdbcType=INTEGER},
      </if>
      <if test="supplierBigPackageNum != null">
        #{supplierBigPackageNum,jdbcType=INTEGER},
      </if>
      <if test="isPiece != null">
        #{isPiece,jdbcType=VARCHAR},
      </if>
      <if test="supplierInventoryNum != null">
        #{supplierInventoryNum,jdbcType=DECIMAL},
      </if>
      <if test="mdmInventoryNum != null">
        #{mdmInventoryNum,jdbcType=DECIMAL},
      </if>
      <if test="supplierQuotePrice != null">
        #{supplierQuotePrice,jdbcType=DECIMAL},
      </if>
      <if test="mdmQuotePrice != null">
        #{mdmQuotePrice,jdbcType=DECIMAL},
      </if>
      <if test="inventoryDate != null">
        #{inventoryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="validityDate != null">
        #{validityDate,jdbcType=TIMESTAMP},
      </if>
      <if test="produceDate != null">
        #{produceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="approvalNumber != null">
        #{approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmSupplierInventoryExample" resultType="java.lang.Long">
    select count(*) from srm_supplier_inventory
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_supplier_inventory
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.supplierNo != null">
        supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCode != null">
        goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierGoodsCode != null">
        supplier_goods_code = #{record.supplierGoodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierGoodsKey != null">
        supplier_goods_key = #{record.supplierGoodsKey,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierGoodsName != null">
        supplier_goods_name = #{record.supplierGoodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierBarCode != null">
        supplier_bar_code = #{record.supplierBarCode,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierManufacturer != null">
        supplier_manufacturer = #{record.supplierManufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierHabitat != null">
        supplier_habitat = #{record.supplierHabitat,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierSpecifications != null">
        supplier_specifications = #{record.supplierSpecifications,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierUnit != null">
        supplier_unit = #{record.supplierUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierMidPackageNum != null">
        supplier_mid_package_num = #{record.supplierMidPackageNum,jdbcType=INTEGER},
      </if>
      <if test="record.supplierBigPackageNum != null">
        supplier_big_package_num = #{record.supplierBigPackageNum,jdbcType=INTEGER},
      </if>
      <if test="record.isPiece != null">
        is_piece = #{record.isPiece,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierInventoryNum != null">
        supplier_inventory_num = #{record.supplierInventoryNum,jdbcType=DECIMAL},
      </if>
      <if test="record.mdmInventoryNum != null">
        mdm_inventory_num = #{record.mdmInventoryNum,jdbcType=DECIMAL},
      </if>
      <if test="record.supplierQuotePrice != null">
        supplier_quote_price = #{record.supplierQuotePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.mdmQuotePrice != null">
        mdm_quote_price = #{record.mdmQuotePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.inventoryDate != null">
        inventory_date = #{record.inventoryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.validityDate != null">
        validity_date = #{record.validityDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.produceDate != null">
        produce_date = #{record.produceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.approvalNumber != null">
        approval_number = #{record.approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_supplier_inventory
    set id = #{record.id,jdbcType=BIGINT},
      supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      supplier_goods_code = #{record.supplierGoodsCode,jdbcType=VARCHAR},
      supplier_goods_key = #{record.supplierGoodsKey,jdbcType=VARCHAR},
      supplier_goods_name = #{record.supplierGoodsName,jdbcType=VARCHAR},
      supplier_bar_code = #{record.supplierBarCode,jdbcType=VARCHAR},
      supplier_manufacturer = #{record.supplierManufacturer,jdbcType=VARCHAR},
      supplier_habitat = #{record.supplierHabitat,jdbcType=VARCHAR},
      supplier_specifications = #{record.supplierSpecifications,jdbcType=VARCHAR},
      supplier_unit = #{record.supplierUnit,jdbcType=VARCHAR},
      supplier_mid_package_num = #{record.supplierMidPackageNum,jdbcType=INTEGER},
      supplier_big_package_num = #{record.supplierBigPackageNum,jdbcType=INTEGER},
      is_piece = #{record.isPiece,jdbcType=VARCHAR},
      supplier_inventory_num = #{record.supplierInventoryNum,jdbcType=DECIMAL},
      mdm_inventory_num = #{record.mdmInventoryNum,jdbcType=DECIMAL},
      supplier_quote_price = #{record.supplierQuotePrice,jdbcType=DECIMAL},
      mdm_quote_price = #{record.mdmQuotePrice,jdbcType=DECIMAL},
      inventory_date = #{record.inventoryDate,jdbcType=TIMESTAMP},
      validity_date = #{record.validityDate,jdbcType=TIMESTAMP},
      produce_date = #{record.produceDate,jdbcType=TIMESTAMP},
      approval_number = #{record.approvalNumber,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmSupplierInventory">
    update srm_supplier_inventory
    <set>
      <if test="supplierNo != null">
        supplier_no = #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        goods_code = #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierGoodsCode != null">
        supplier_goods_code = #{supplierGoodsCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierGoodsKey != null">
        supplier_goods_key = #{supplierGoodsKey,jdbcType=VARCHAR},
      </if>
      <if test="supplierGoodsName != null">
        supplier_goods_name = #{supplierGoodsName,jdbcType=VARCHAR},
      </if>
      <if test="supplierBarCode != null">
        supplier_bar_code = #{supplierBarCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierManufacturer != null">
        supplier_manufacturer = #{supplierManufacturer,jdbcType=VARCHAR},
      </if>
      <if test="supplierHabitat != null">
        supplier_habitat = #{supplierHabitat,jdbcType=VARCHAR},
      </if>
      <if test="supplierSpecifications != null">
        supplier_specifications = #{supplierSpecifications,jdbcType=VARCHAR},
      </if>
      <if test="supplierUnit != null">
        supplier_unit = #{supplierUnit,jdbcType=VARCHAR},
      </if>
      <if test="supplierMidPackageNum != null">
        supplier_mid_package_num = #{supplierMidPackageNum,jdbcType=INTEGER},
      </if>
      <if test="supplierBigPackageNum != null">
        supplier_big_package_num = #{supplierBigPackageNum,jdbcType=INTEGER},
      </if>
      <if test="isPiece != null">
        is_piece = #{isPiece,jdbcType=VARCHAR},
      </if>
      <if test="supplierInventoryNum != null">
        supplier_inventory_num = #{supplierInventoryNum,jdbcType=DECIMAL},
      </if>
      <if test="mdmInventoryNum != null">
        mdm_inventory_num = #{mdmInventoryNum,jdbcType=DECIMAL},
      </if>
      <if test="supplierQuotePrice != null">
        supplier_quote_price = #{supplierQuotePrice,jdbcType=DECIMAL},
      </if>
      <if test="mdmQuotePrice != null">
        mdm_quote_price = #{mdmQuotePrice,jdbcType=DECIMAL},
      </if>
      <if test="inventoryDate != null">
        inventory_date = #{inventoryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="validityDate != null">
        validity_date = #{validityDate,jdbcType=TIMESTAMP},
      </if>
      <if test="produceDate != null">
        produce_date = #{produceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="approvalNumber != null">
        approval_number = #{approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmSupplierInventory">
    update srm_supplier_inventory
    set supplier_no = #{supplierNo,jdbcType=VARCHAR},
      goods_code = #{goodsCode,jdbcType=VARCHAR},
      supplier_goods_code = #{supplierGoodsCode,jdbcType=VARCHAR},
      supplier_goods_key = #{supplierGoodsKey,jdbcType=VARCHAR},
      supplier_goods_name = #{supplierGoodsName,jdbcType=VARCHAR},
      supplier_bar_code = #{supplierBarCode,jdbcType=VARCHAR},
      supplier_manufacturer = #{supplierManufacturer,jdbcType=VARCHAR},
      supplier_habitat = #{supplierHabitat,jdbcType=VARCHAR},
      supplier_specifications = #{supplierSpecifications,jdbcType=VARCHAR},
      supplier_unit = #{supplierUnit,jdbcType=VARCHAR},
      supplier_mid_package_num = #{supplierMidPackageNum,jdbcType=INTEGER},
      supplier_big_package_num = #{supplierBigPackageNum,jdbcType=INTEGER},
      is_piece = #{isPiece,jdbcType=VARCHAR},
      supplier_inventory_num = #{supplierInventoryNum,jdbcType=DECIMAL},
      mdm_inventory_num = #{mdmInventoryNum,jdbcType=DECIMAL},
      supplier_quote_price = #{supplierQuotePrice,jdbcType=DECIMAL},
      mdm_quote_price = #{mdmQuotePrice,jdbcType=DECIMAL},
      inventory_date = #{inventoryDate,jdbcType=TIMESTAMP},
      validity_date = #{validityDate,jdbcType=TIMESTAMP},
      produce_date = #{produceDate,jdbcType=TIMESTAMP},
      approval_number = #{approvalNumber,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>