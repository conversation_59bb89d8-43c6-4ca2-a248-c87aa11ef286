<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmEnquiryResultMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmEnquiryResult">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="enquiry_order_no" jdbcType="VARCHAR" property="enquiryOrderNo" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="supplier_no" jdbcType="VARCHAR" property="supplierNo" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="enquiry_price" jdbcType="DECIMAL" property="enquiryPrice" />
    <result column="enquiry_quantity" jdbcType="DECIMAL" property="enquiryQuantity" />
    <result column="enquiry_status" jdbcType="TINYINT" property="enquiryStatus" />
    <result column="comments" jdbcType="VARCHAR" property="comments" />
    <result column="selected" jdbcType="TINYINT" property="selected" />
    <result column="quote_date" jdbcType="TIMESTAMP" property="quoteDate" />
    <result column="selected_date" jdbcType="TIMESTAMP" property="selectedDate" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="enquiry_increase_range" jdbcType="DECIMAL" property="enquiryIncreaseRange" />
    <result column="valid_to" jdbcType="DATE" property="validTo" />
    <result column="original_level" jdbcType="INTEGER" property="originalLevel" />
    <result column="confirm_level" jdbcType="INTEGER" property="confirmLevel" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, enquiry_order_no, goods_code, supplier_no, supplier_name, enquiry_price, enquiry_quantity, 
    enquiry_status, comments, selected, quote_date, selected_date, gmt_create, gmt_update, 
    enquiry_increase_range, valid_to, original_level, confirm_level
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmEnquiryResultExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_enquiry_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_enquiry_result
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from srm_enquiry_result
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmEnquiryResultExample">
    delete from srm_enquiry_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmEnquiryResult">
    insert into srm_enquiry_result (id, enquiry_order_no, goods_code, 
      supplier_no, supplier_name, enquiry_price, 
      enquiry_quantity, enquiry_status, comments, 
      selected, quote_date, selected_date, 
      gmt_create, gmt_update, enquiry_increase_range, 
      valid_to, original_level, confirm_level
      )
    values (#{id,jdbcType=INTEGER}, #{enquiryOrderNo,jdbcType=VARCHAR}, #{goodsCode,jdbcType=VARCHAR}, 
      #{supplierNo,jdbcType=VARCHAR}, #{supplierName,jdbcType=VARCHAR}, #{enquiryPrice,jdbcType=DECIMAL}, 
      #{enquiryQuantity,jdbcType=DECIMAL}, #{enquiryStatus,jdbcType=TINYINT}, #{comments,jdbcType=VARCHAR}, 
      #{selected,jdbcType=TINYINT}, #{quoteDate,jdbcType=TIMESTAMP}, #{selectedDate,jdbcType=TIMESTAMP}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{enquiryIncreaseRange,jdbcType=DECIMAL}, 
      #{validTo,jdbcType=DATE}, #{originalLevel,jdbcType=INTEGER}, #{confirmLevel,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmEnquiryResult">
    insert into srm_enquiry_result
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="enquiryOrderNo != null">
        enquiry_order_no,
      </if>
      <if test="goodsCode != null">
        goods_code,
      </if>
      <if test="supplierNo != null">
        supplier_no,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="enquiryPrice != null">
        enquiry_price,
      </if>
      <if test="enquiryQuantity != null">
        enquiry_quantity,
      </if>
      <if test="enquiryStatus != null">
        enquiry_status,
      </if>
      <if test="comments != null">
        comments,
      </if>
      <if test="selected != null">
        selected,
      </if>
      <if test="quoteDate != null">
        quote_date,
      </if>
      <if test="selectedDate != null">
        selected_date,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="enquiryIncreaseRange != null">
        enquiry_increase_range,
      </if>
      <if test="validTo != null">
        valid_to,
      </if>
      <if test="originalLevel != null">
        original_level,
      </if>
      <if test="confirmLevel != null">
        confirm_level,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="enquiryOrderNo != null">
        #{enquiryOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null">
        #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="enquiryPrice != null">
        #{enquiryPrice,jdbcType=DECIMAL},
      </if>
      <if test="enquiryQuantity != null">
        #{enquiryQuantity,jdbcType=DECIMAL},
      </if>
      <if test="enquiryStatus != null">
        #{enquiryStatus,jdbcType=TINYINT},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="selected != null">
        #{selected,jdbcType=TINYINT},
      </if>
      <if test="quoteDate != null">
        #{quoteDate,jdbcType=TIMESTAMP},
      </if>
      <if test="selectedDate != null">
        #{selectedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="enquiryIncreaseRange != null">
        #{enquiryIncreaseRange,jdbcType=DECIMAL},
      </if>
      <if test="validTo != null">
        #{validTo,jdbcType=DATE},
      </if>
      <if test="originalLevel != null">
        #{originalLevel,jdbcType=INTEGER},
      </if>
      <if test="confirmLevel != null">
        #{confirmLevel,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmEnquiryResultExample" resultType="java.lang.Long">
    select count(*) from srm_enquiry_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_enquiry_result
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.enquiryOrderNo != null">
        enquiry_order_no = #{record.enquiryOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCode != null">
        goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierNo != null">
        supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierName != null">
        supplier_name = #{record.supplierName,jdbcType=VARCHAR},
      </if>
      <if test="record.enquiryPrice != null">
        enquiry_price = #{record.enquiryPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.enquiryQuantity != null">
        enquiry_quantity = #{record.enquiryQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.enquiryStatus != null">
        enquiry_status = #{record.enquiryStatus,jdbcType=TINYINT},
      </if>
      <if test="record.comments != null">
        comments = #{record.comments,jdbcType=VARCHAR},
      </if>
      <if test="record.selected != null">
        selected = #{record.selected,jdbcType=TINYINT},
      </if>
      <if test="record.quoteDate != null">
        quote_date = #{record.quoteDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.selectedDate != null">
        selected_date = #{record.selectedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.enquiryIncreaseRange != null">
        enquiry_increase_range = #{record.enquiryIncreaseRange,jdbcType=DECIMAL},
      </if>
      <if test="record.validTo != null">
        valid_to = #{record.validTo,jdbcType=DATE},
      </if>
      <if test="record.originalLevel != null">
        original_level = #{record.originalLevel,jdbcType=INTEGER},
      </if>
      <if test="record.confirmLevel != null">
        confirm_level = #{record.confirmLevel,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_enquiry_result
    set id = #{record.id,jdbcType=INTEGER},
      enquiry_order_no = #{record.enquiryOrderNo,jdbcType=VARCHAR},
      goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      supplier_name = #{record.supplierName,jdbcType=VARCHAR},
      enquiry_price = #{record.enquiryPrice,jdbcType=DECIMAL},
      enquiry_quantity = #{record.enquiryQuantity,jdbcType=DECIMAL},
      enquiry_status = #{record.enquiryStatus,jdbcType=TINYINT},
      comments = #{record.comments,jdbcType=VARCHAR},
      selected = #{record.selected,jdbcType=TINYINT},
      quote_date = #{record.quoteDate,jdbcType=TIMESTAMP},
      selected_date = #{record.selectedDate,jdbcType=TIMESTAMP},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      enquiry_increase_range = #{record.enquiryIncreaseRange,jdbcType=DECIMAL},
      valid_to = #{record.validTo,jdbcType=DATE},
      original_level = #{record.originalLevel,jdbcType=INTEGER},
      confirm_level = #{record.confirmLevel,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmEnquiryResult">
    update srm_enquiry_result
    <set>
      <if test="enquiryOrderNo != null">
        enquiry_order_no = #{enquiryOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        goods_code = #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null">
        supplier_no = #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="enquiryPrice != null">
        enquiry_price = #{enquiryPrice,jdbcType=DECIMAL},
      </if>
      <if test="enquiryQuantity != null">
        enquiry_quantity = #{enquiryQuantity,jdbcType=DECIMAL},
      </if>
      <if test="enquiryStatus != null">
        enquiry_status = #{enquiryStatus,jdbcType=TINYINT},
      </if>
      <if test="comments != null">
        comments = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="selected != null">
        selected = #{selected,jdbcType=TINYINT},
      </if>
      <if test="quoteDate != null">
        quote_date = #{quoteDate,jdbcType=TIMESTAMP},
      </if>
      <if test="selectedDate != null">
        selected_date = #{selectedDate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="enquiryIncreaseRange != null">
        enquiry_increase_range = #{enquiryIncreaseRange,jdbcType=DECIMAL},
      </if>
      <if test="validTo != null">
        valid_to = #{validTo,jdbcType=DATE},
      </if>
      <if test="originalLevel != null">
        original_level = #{originalLevel,jdbcType=INTEGER},
      </if>
      <if test="confirmLevel != null">
        confirm_level = #{confirmLevel,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmEnquiryResult">
    update srm_enquiry_result
    set enquiry_order_no = #{enquiryOrderNo,jdbcType=VARCHAR},
      goods_code = #{goodsCode,jdbcType=VARCHAR},
      supplier_no = #{supplierNo,jdbcType=VARCHAR},
      supplier_name = #{supplierName,jdbcType=VARCHAR},
      enquiry_price = #{enquiryPrice,jdbcType=DECIMAL},
      enquiry_quantity = #{enquiryQuantity,jdbcType=DECIMAL},
      enquiry_status = #{enquiryStatus,jdbcType=TINYINT},
      comments = #{comments,jdbcType=VARCHAR},
      selected = #{selected,jdbcType=TINYINT},
      quote_date = #{quoteDate,jdbcType=TIMESTAMP},
      selected_date = #{selectedDate,jdbcType=TIMESTAMP},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      enquiry_increase_range = #{enquiryIncreaseRange,jdbcType=DECIMAL},
      valid_to = #{validTo,jdbcType=DATE},
      original_level = #{originalLevel,jdbcType=INTEGER},
      confirm_level = #{confirmLevel,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>