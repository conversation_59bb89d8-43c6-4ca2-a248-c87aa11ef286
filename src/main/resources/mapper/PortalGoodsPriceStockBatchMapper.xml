<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.PortalGoodsPriceStockBatchMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.PortalGoodsPriceStockBatch">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_no" jdbcType="VARCHAR" property="storeNo" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="list_price" jdbcType="DECIMAL" property="listPrice" />
    <result column="batch_code" jdbcType="VARCHAR" property="batchCode" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="stock" jdbcType="DECIMAL" property="stock" />
    <result column="available_stock" jdbcType="DECIMAL" property="availableStock" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="expire_date" jdbcType="TIMESTAMP" property="expireDate" />
    <result column="produce_date" jdbcType="TIMESTAMP" property="produceDate" />
    <result column="produce_company" jdbcType="VARCHAR" property="produceCompany" />
    <result column="supply" jdbcType="VARCHAR" property="supply" />
    <result column="sync_date" jdbcType="TIMESTAMP" property="syncDate" />
    <result column="location" jdbcType="VARCHAR" property="location" />
    <result column="produce_area" jdbcType="VARCHAR" property="produceArea" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="last_modified_by" jdbcType="VARCHAR" property="lastModifiedBy" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_id, company_code, store_id, store_no, goods_code, list_price, batch_code, 
    batch_no, stock, available_stock, unit, expire_date, produce_date, produce_company, 
    supply, sync_date, `location`, produce_area, `status`, version, extend, created_by, 
    last_modified_by, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.PortalGoodsPriceStockBatchExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from portal_goods_price_stock_batch
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from portal_goods_price_stock_batch
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from portal_goods_price_stock_batch
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.PortalGoodsPriceStockBatchExample">
    delete from portal_goods_price_stock_batch
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.PortalGoodsPriceStockBatch">
    insert into portal_goods_price_stock_batch (id, business_id, company_code, 
      store_id, store_no, goods_code, 
      list_price, batch_code, batch_no, 
      stock, available_stock, unit, 
      expire_date, produce_date, produce_company, 
      supply, sync_date, `location`, 
      produce_area, `status`, version, 
      extend, created_by, last_modified_by, 
      gmt_create, gmt_update)
    values (#{id,jdbcType=BIGINT}, #{businessId,jdbcType=BIGINT}, #{companyCode,jdbcType=VARCHAR}, 
      #{storeId,jdbcType=BIGINT}, #{storeNo,jdbcType=VARCHAR}, #{goodsCode,jdbcType=VARCHAR}, 
      #{listPrice,jdbcType=DECIMAL}, #{batchCode,jdbcType=VARCHAR}, #{batchNo,jdbcType=VARCHAR}, 
      #{stock,jdbcType=DECIMAL}, #{availableStock,jdbcType=DECIMAL}, #{unit,jdbcType=VARCHAR}, 
      #{expireDate,jdbcType=TIMESTAMP}, #{produceDate,jdbcType=TIMESTAMP}, #{produceCompany,jdbcType=VARCHAR}, 
      #{supply,jdbcType=VARCHAR}, #{syncDate,jdbcType=TIMESTAMP}, #{location,jdbcType=VARCHAR}, 
      #{produceArea,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{version,jdbcType=INTEGER}, 
      #{extend,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{lastModifiedBy,jdbcType=VARCHAR}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.PortalGoodsPriceStockBatch">
    insert into portal_goods_price_stock_batch
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="storeNo != null">
        store_no,
      </if>
      <if test="goodsCode != null">
        goods_code,
      </if>
      <if test="listPrice != null">
        list_price,
      </if>
      <if test="batchCode != null">
        batch_code,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="stock != null">
        stock,
      </if>
      <if test="availableStock != null">
        available_stock,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="expireDate != null">
        expire_date,
      </if>
      <if test="produceDate != null">
        produce_date,
      </if>
      <if test="produceCompany != null">
        produce_company,
      </if>
      <if test="supply != null">
        supply,
      </if>
      <if test="syncDate != null">
        sync_date,
      </if>
      <if test="location != null">
        `location`,
      </if>
      <if test="produceArea != null">
        produce_area,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeNo != null">
        #{storeNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="listPrice != null">
        #{listPrice,jdbcType=DECIMAL},
      </if>
      <if test="batchCode != null">
        #{batchCode,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="stock != null">
        #{stock,jdbcType=DECIMAL},
      </if>
      <if test="availableStock != null">
        #{availableStock,jdbcType=DECIMAL},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="expireDate != null">
        #{expireDate,jdbcType=TIMESTAMP},
      </if>
      <if test="produceDate != null">
        #{produceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="produceCompany != null">
        #{produceCompany,jdbcType=VARCHAR},
      </if>
      <if test="supply != null">
        #{supply,jdbcType=VARCHAR},
      </if>
      <if test="syncDate != null">
        #{syncDate,jdbcType=TIMESTAMP},
      </if>
      <if test="location != null">
        #{location,jdbcType=VARCHAR},
      </if>
      <if test="produceArea != null">
        #{produceArea,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedBy != null">
        #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.PortalGoodsPriceStockBatchExample" resultType="java.lang.Long">
    select count(*) from portal_goods_price_stock_batch
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update portal_goods_price_stock_batch
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.storeNo != null">
        store_no = #{record.storeNo,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCode != null">
        goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.listPrice != null">
        list_price = #{record.listPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.batchCode != null">
        batch_code = #{record.batchCode,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.stock != null">
        stock = #{record.stock,jdbcType=DECIMAL},
      </if>
      <if test="record.availableStock != null">
        available_stock = #{record.availableStock,jdbcType=DECIMAL},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.expireDate != null">
        expire_date = #{record.expireDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.produceDate != null">
        produce_date = #{record.produceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.produceCompany != null">
        produce_company = #{record.produceCompany,jdbcType=VARCHAR},
      </if>
      <if test="record.supply != null">
        supply = #{record.supply,jdbcType=VARCHAR},
      </if>
      <if test="record.syncDate != null">
        sync_date = #{record.syncDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.location != null">
        `location` = #{record.location,jdbcType=VARCHAR},
      </if>
      <if test="record.produceArea != null">
        produce_area = #{record.produceArea,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.lastModifiedBy != null">
        last_modified_by = #{record.lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update portal_goods_price_stock_batch
    set id = #{record.id,jdbcType=BIGINT},
      business_id = #{record.businessId,jdbcType=BIGINT},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      store_id = #{record.storeId,jdbcType=BIGINT},
      store_no = #{record.storeNo,jdbcType=VARCHAR},
      goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      list_price = #{record.listPrice,jdbcType=DECIMAL},
      batch_code = #{record.batchCode,jdbcType=VARCHAR},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      stock = #{record.stock,jdbcType=DECIMAL},
      available_stock = #{record.availableStock,jdbcType=DECIMAL},
      unit = #{record.unit,jdbcType=VARCHAR},
      expire_date = #{record.expireDate,jdbcType=TIMESTAMP},
      produce_date = #{record.produceDate,jdbcType=TIMESTAMP},
      produce_company = #{record.produceCompany,jdbcType=VARCHAR},
      supply = #{record.supply,jdbcType=VARCHAR},
      sync_date = #{record.syncDate,jdbcType=TIMESTAMP},
      `location` = #{record.location,jdbcType=VARCHAR},
      produce_area = #{record.produceArea,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=TINYINT},
      version = #{record.version,jdbcType=INTEGER},
      extend = #{record.extend,jdbcType=VARCHAR},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      last_modified_by = #{record.lastModifiedBy,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.PortalGoodsPriceStockBatch">
    update portal_goods_price_stock_batch
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeNo != null">
        store_no = #{storeNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        goods_code = #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="listPrice != null">
        list_price = #{listPrice,jdbcType=DECIMAL},
      </if>
      <if test="batchCode != null">
        batch_code = #{batchCode,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="stock != null">
        stock = #{stock,jdbcType=DECIMAL},
      </if>
      <if test="availableStock != null">
        available_stock = #{availableStock,jdbcType=DECIMAL},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="expireDate != null">
        expire_date = #{expireDate,jdbcType=TIMESTAMP},
      </if>
      <if test="produceDate != null">
        produce_date = #{produceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="produceCompany != null">
        produce_company = #{produceCompany,jdbcType=VARCHAR},
      </if>
      <if test="supply != null">
        supply = #{supply,jdbcType=VARCHAR},
      </if>
      <if test="syncDate != null">
        sync_date = #{syncDate,jdbcType=TIMESTAMP},
      </if>
      <if test="location != null">
        `location` = #{location,jdbcType=VARCHAR},
      </if>
      <if test="produceArea != null">
        produce_area = #{produceArea,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="lastModifiedBy != null">
        last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.PortalGoodsPriceStockBatch">
    update portal_goods_price_stock_batch
    set business_id = #{businessId,jdbcType=BIGINT},
      company_code = #{companyCode,jdbcType=VARCHAR},
      store_id = #{storeId,jdbcType=BIGINT},
      store_no = #{storeNo,jdbcType=VARCHAR},
      goods_code = #{goodsCode,jdbcType=VARCHAR},
      list_price = #{listPrice,jdbcType=DECIMAL},
      batch_code = #{batchCode,jdbcType=VARCHAR},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      stock = #{stock,jdbcType=DECIMAL},
      available_stock = #{availableStock,jdbcType=DECIMAL},
      unit = #{unit,jdbcType=VARCHAR},
      expire_date = #{expireDate,jdbcType=TIMESTAMP},
      produce_date = #{produceDate,jdbcType=TIMESTAMP},
      produce_company = #{produceCompany,jdbcType=VARCHAR},
      supply = #{supply,jdbcType=VARCHAR},
      sync_date = #{syncDate,jdbcType=TIMESTAMP},
      `location` = #{location,jdbcType=VARCHAR},
      produce_area = #{produceArea,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      version = #{version,jdbcType=INTEGER},
      extend = #{extend,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      last_modified_by = #{lastModifiedBy,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>