<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.UnifiedPurchaseOrderGoodsBatchMapper">

    <update id="batchUpdateCountByExample" parameterType="java.util.List">
        update unified_purchase_order_goods
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="receiving_count=case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.receivingCount!=null">
                        WHEN id=#{item.id,jdbcType=INTEGER} then #{item.receivingCount}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_by=case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.updateBy!=null">
                        WHEN id=#{item.id,jdbcType=INTEGER} then #{item.updateBy}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>
    <insert id="batchInsertSelective" parameterType="java.util.List">
        insert into unified_purchase_order_goods
        (purchase_order_no, goods_code,
        bar_code, goods_common_name, goods_name,
        row_no, specifications, dosage_form,
        apply_count, receiving_count, habitat,
        manufacturer, status, extend,
        created_by, update_by, stock_use_ratio,
        confirm_count, unit, buying_price,
        apprdocno, deliver_gather_count)
        values
        <foreach collection="list" item="item" index="index" separator="," >
          (#{item.purchaseOrderNo,jdbcType=VARCHAR}, #{item.goodsCode,jdbcType=VARCHAR},
          #{item.barCode,jdbcType=VARCHAR}, #{item.goodsCommonName,jdbcType=VARCHAR}, #{item.goodsName,jdbcType=VARCHAR},
          #{item.rowNo,jdbcType=BIGINT}, #{item.specifications,jdbcType=VARCHAR}, #{item.dosageForm,jdbcType=VARCHAR},
          #{item.applyCount,jdbcType=INTEGER}, #{item.receivingCount,jdbcType=INTEGER}, #{item.habitat,jdbcType=VARCHAR},
          #{item.manufacturer,jdbcType=VARCHAR}, #{item.status,jdbcType=TINYINT}, #{item.extend,jdbcType=VARCHAR},
          #{item.createdBy,jdbcType=BIGINT}, #{item.updateBy,jdbcType=BIGINT}, #{item.stockUseRatio,jdbcType=DECIMAL},
          #{item.confirmCount,jdbcType=INTEGER}, #{item.unit,jdbcType=VARCHAR}, #{item.buyingPrice,jdbcType=DECIMAL},
          #{item.apprdocno,jdbcType=VARCHAR}, #{item.deliverGatherCount,jdbcType=INTEGER})
        </foreach>
    </insert>

    <update id="batchUpdateCountBySap" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";" open="" close="">
            update unified_purchase_order_goods
            <set>
                receiving_count = deliver_gather_count
            </set>
            where goods_code = #{item.goodsCode} AND purchase_order_no = #{item.purchaseOrderNo}
        </foreach>
    </update>
</mapper>
