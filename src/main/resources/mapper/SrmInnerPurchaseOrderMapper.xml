<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmInnerPurchaseOrderMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmInnerPurchaseOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="purchase_order_no" jdbcType="VARCHAR" property="purchaseOrderNo" />
    <result column="purchaser_code" jdbcType="VARCHAR" property="purchaserCode" />
    <result column="purchase_delete_id" jdbcType="VARCHAR" property="purchaseDeleteId" />
    <result column="confirm_status" jdbcType="INTEGER" property="confirmStatus" />
    <result column="order_reserve_status" jdbcType="INTEGER" property="orderReserveStatus" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="purchase_org" jdbcType="VARCHAR" property="purchaseOrg" />
    <result column="purchase_org_name" jdbcType="VARCHAR" property="purchaseOrgName" />
    <result column="purchase_group" jdbcType="VARCHAR" property="purchaseGroup" />
    <result column="purchase_type" jdbcType="VARCHAR" property="purchaseType" />
    <result column="supplier_no" jdbcType="VARCHAR" property="supplierNo" />
    <result column="supplier_company_code" jdbcType="VARCHAR" property="supplierCompanyCode" />
    <result column="supplier_company_name" jdbcType="VARCHAR" property="supplierCompanyName" />
    <result column="supplier_warehouse_code" jdbcType="VARCHAR" property="supplierWarehouseCode" />
    <result column="supplier_warehouse_name" jdbcType="VARCHAR" property="supplierWarehouseName" />
    <result column="supplier_salesman_code" jdbcType="VARCHAR" property="supplierSalesmanCode" />
    <result column="pay_code" jdbcType="VARCHAR" property="payCode" />
    <result column="purchase_date" jdbcType="TIMESTAMP" property="purchaseDate" />
    <result column="purchase_person" jdbcType="VARCHAR" property="purchasePerson" />
    <result column="no_tax_amount" jdbcType="DECIMAL" property="noTaxAmount" />
    <result column="tax_amount" jdbcType="DECIMAL" property="taxAmount" />
    <result column="have_tax_amount" jdbcType="DECIMAL" property="haveTaxAmount" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="tel" jdbcType="VARCHAR" property="tel" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, purchase_order_no, purchaser_code, purchase_delete_id, confirm_status, order_reserve_status, 
    create_date, company_code, company_name, warehouse_code, warehouse_name, purchase_org, 
    purchase_org_name, purchase_group, purchase_type, supplier_no, supplier_company_code, 
    supplier_company_name, supplier_warehouse_code, supplier_warehouse_name, supplier_salesman_code, 
    pay_code, purchase_date, purchase_person, no_tax_amount, tax_amount, have_tax_amount, 
    address, tel, gmt_create, gmt_update, `status`, extend, version, created_by, created_name, 
    updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_inner_purchase_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_inner_purchase_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from srm_inner_purchase_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrderExample">
    delete from srm_inner_purchase_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrder">
    insert into srm_inner_purchase_order (id, purchase_order_no, purchaser_code, 
      purchase_delete_id, confirm_status, order_reserve_status, 
      create_date, company_code, company_name, 
      warehouse_code, warehouse_name, purchase_org, 
      purchase_org_name, purchase_group, purchase_type, 
      supplier_no, supplier_company_code, supplier_company_name, 
      supplier_warehouse_code, supplier_warehouse_name, 
      supplier_salesman_code, pay_code, purchase_date, 
      purchase_person, no_tax_amount, tax_amount, 
      have_tax_amount, address, tel, 
      gmt_create, gmt_update, `status`, 
      extend, version, created_by, 
      created_name, updated_by, updated_name
      )
    values (#{id,jdbcType=BIGINT}, #{purchaseOrderNo,jdbcType=VARCHAR}, #{purchaserCode,jdbcType=VARCHAR}, 
      #{purchaseDeleteId,jdbcType=VARCHAR}, #{confirmStatus,jdbcType=INTEGER}, #{orderReserveStatus,jdbcType=INTEGER}, 
      #{createDate,jdbcType=TIMESTAMP}, #{companyCode,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, 
      #{warehouseCode,jdbcType=VARCHAR}, #{warehouseName,jdbcType=VARCHAR}, #{purchaseOrg,jdbcType=VARCHAR}, 
      #{purchaseOrgName,jdbcType=VARCHAR}, #{purchaseGroup,jdbcType=VARCHAR}, #{purchaseType,jdbcType=VARCHAR}, 
      #{supplierNo,jdbcType=VARCHAR}, #{supplierCompanyCode,jdbcType=VARCHAR}, #{supplierCompanyName,jdbcType=VARCHAR}, 
      #{supplierWarehouseCode,jdbcType=VARCHAR}, #{supplierWarehouseName,jdbcType=VARCHAR}, 
      #{supplierSalesmanCode,jdbcType=VARCHAR}, #{payCode,jdbcType=VARCHAR}, #{purchaseDate,jdbcType=TIMESTAMP}, 
      #{purchasePerson,jdbcType=VARCHAR}, #{noTaxAmount,jdbcType=DECIMAL}, #{taxAmount,jdbcType=DECIMAL}, 
      #{haveTaxAmount,jdbcType=DECIMAL}, #{address,jdbcType=VARCHAR}, #{tel,jdbcType=VARCHAR}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}, 
      #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, 
      #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrder">
    insert into srm_inner_purchase_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="purchaseOrderNo != null">
        purchase_order_no,
      </if>
      <if test="purchaserCode != null">
        purchaser_code,
      </if>
      <if test="purchaseDeleteId != null">
        purchase_delete_id,
      </if>
      <if test="confirmStatus != null">
        confirm_status,
      </if>
      <if test="orderReserveStatus != null">
        order_reserve_status,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="warehouseCode != null">
        warehouse_code,
      </if>
      <if test="warehouseName != null">
        warehouse_name,
      </if>
      <if test="purchaseOrg != null">
        purchase_org,
      </if>
      <if test="purchaseOrgName != null">
        purchase_org_name,
      </if>
      <if test="purchaseGroup != null">
        purchase_group,
      </if>
      <if test="purchaseType != null">
        purchase_type,
      </if>
      <if test="supplierNo != null">
        supplier_no,
      </if>
      <if test="supplierCompanyCode != null">
        supplier_company_code,
      </if>
      <if test="supplierCompanyName != null">
        supplier_company_name,
      </if>
      <if test="supplierWarehouseCode != null">
        supplier_warehouse_code,
      </if>
      <if test="supplierWarehouseName != null">
        supplier_warehouse_name,
      </if>
      <if test="supplierSalesmanCode != null">
        supplier_salesman_code,
      </if>
      <if test="payCode != null">
        pay_code,
      </if>
      <if test="purchaseDate != null">
        purchase_date,
      </if>
      <if test="purchasePerson != null">
        purchase_person,
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount,
      </if>
      <if test="taxAmount != null">
        tax_amount,
      </if>
      <if test="haveTaxAmount != null">
        have_tax_amount,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="tel != null">
        tel,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="purchaseOrderNo != null">
        #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaserCode != null">
        #{purchaserCode,jdbcType=VARCHAR},
      </if>
      <if test="purchaseDeleteId != null">
        #{purchaseDeleteId,jdbcType=VARCHAR},
      </if>
      <if test="confirmStatus != null">
        #{confirmStatus,jdbcType=INTEGER},
      </if>
      <if test="orderReserveStatus != null">
        #{orderReserveStatus,jdbcType=INTEGER},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrg != null">
        #{purchaseOrg,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrgName != null">
        #{purchaseOrgName,jdbcType=VARCHAR},
      </if>
      <if test="purchaseGroup != null">
        #{purchaseGroup,jdbcType=VARCHAR},
      </if>
      <if test="purchaseType != null">
        #{purchaseType,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null">
        #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierCompanyCode != null">
        #{supplierCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierCompanyName != null">
        #{supplierCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="supplierWarehouseCode != null">
        #{supplierWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierWarehouseName != null">
        #{supplierWarehouseName,jdbcType=VARCHAR},
      </if>
      <if test="supplierSalesmanCode != null">
        #{supplierSalesmanCode,jdbcType=VARCHAR},
      </if>
      <if test="payCode != null">
        #{payCode,jdbcType=VARCHAR},
      </if>
      <if test="purchaseDate != null">
        #{purchaseDate,jdbcType=TIMESTAMP},
      </if>
      <if test="purchasePerson != null">
        #{purchasePerson,jdbcType=VARCHAR},
      </if>
      <if test="noTaxAmount != null">
        #{noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="haveTaxAmount != null">
        #{haveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="tel != null">
        #{tel,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrderExample" resultType="java.lang.Long">
    select count(*) from srm_inner_purchase_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_inner_purchase_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.purchaseOrderNo != null">
        purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaserCode != null">
        purchaser_code = #{record.purchaserCode,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseDeleteId != null">
        purchase_delete_id = #{record.purchaseDeleteId,jdbcType=VARCHAR},
      </if>
      <if test="record.confirmStatus != null">
        confirm_status = #{record.confirmStatus,jdbcType=INTEGER},
      </if>
      <if test="record.orderReserveStatus != null">
        order_reserve_status = #{record.orderReserveStatus,jdbcType=INTEGER},
      </if>
      <if test="record.createDate != null">
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseCode != null">
        warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseName != null">
        warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrg != null">
        purchase_org = #{record.purchaseOrg,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrgName != null">
        purchase_org_name = #{record.purchaseOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseGroup != null">
        purchase_group = #{record.purchaseGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseType != null">
        purchase_type = #{record.purchaseType,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierNo != null">
        supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierCompanyCode != null">
        supplier_company_code = #{record.supplierCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierCompanyName != null">
        supplier_company_name = #{record.supplierCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierWarehouseCode != null">
        supplier_warehouse_code = #{record.supplierWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierWarehouseName != null">
        supplier_warehouse_name = #{record.supplierWarehouseName,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierSalesmanCode != null">
        supplier_salesman_code = #{record.supplierSalesmanCode,jdbcType=VARCHAR},
      </if>
      <if test="record.payCode != null">
        pay_code = #{record.payCode,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseDate != null">
        purchase_date = #{record.purchaseDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.purchasePerson != null">
        purchase_person = #{record.purchasePerson,jdbcType=VARCHAR},
      </if>
      <if test="record.noTaxAmount != null">
        no_tax_amount = #{record.noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.taxAmount != null">
        tax_amount = #{record.taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.haveTaxAmount != null">
        have_tax_amount = #{record.haveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.address != null">
        address = #{record.address,jdbcType=VARCHAR},
      </if>
      <if test="record.tel != null">
        tel = #{record.tel,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_inner_purchase_order
    set id = #{record.id,jdbcType=BIGINT},
      purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      purchaser_code = #{record.purchaserCode,jdbcType=VARCHAR},
      purchase_delete_id = #{record.purchaseDeleteId,jdbcType=VARCHAR},
      confirm_status = #{record.confirmStatus,jdbcType=INTEGER},
      order_reserve_status = #{record.orderReserveStatus,jdbcType=INTEGER},
      create_date = #{record.createDate,jdbcType=TIMESTAMP},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      purchase_org = #{record.purchaseOrg,jdbcType=VARCHAR},
      purchase_org_name = #{record.purchaseOrgName,jdbcType=VARCHAR},
      purchase_group = #{record.purchaseGroup,jdbcType=VARCHAR},
      purchase_type = #{record.purchaseType,jdbcType=VARCHAR},
      supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      supplier_company_code = #{record.supplierCompanyCode,jdbcType=VARCHAR},
      supplier_company_name = #{record.supplierCompanyName,jdbcType=VARCHAR},
      supplier_warehouse_code = #{record.supplierWarehouseCode,jdbcType=VARCHAR},
      supplier_warehouse_name = #{record.supplierWarehouseName,jdbcType=VARCHAR},
      supplier_salesman_code = #{record.supplierSalesmanCode,jdbcType=VARCHAR},
      pay_code = #{record.payCode,jdbcType=VARCHAR},
      purchase_date = #{record.purchaseDate,jdbcType=TIMESTAMP},
      purchase_person = #{record.purchasePerson,jdbcType=VARCHAR},
      no_tax_amount = #{record.noTaxAmount,jdbcType=DECIMAL},
      tax_amount = #{record.taxAmount,jdbcType=DECIMAL},
      have_tax_amount = #{record.haveTaxAmount,jdbcType=DECIMAL},
      address = #{record.address,jdbcType=VARCHAR},
      tel = #{record.tel,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      `status` = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrder">
    update srm_inner_purchase_order
    <set>
      <if test="purchaseOrderNo != null">
        purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaserCode != null">
        purchaser_code = #{purchaserCode,jdbcType=VARCHAR},
      </if>
      <if test="purchaseDeleteId != null">
        purchase_delete_id = #{purchaseDeleteId,jdbcType=VARCHAR},
      </if>
      <if test="confirmStatus != null">
        confirm_status = #{confirmStatus,jdbcType=INTEGER},
      </if>
      <if test="orderReserveStatus != null">
        order_reserve_status = #{orderReserveStatus,jdbcType=INTEGER},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrg != null">
        purchase_org = #{purchaseOrg,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrgName != null">
        purchase_org_name = #{purchaseOrgName,jdbcType=VARCHAR},
      </if>
      <if test="purchaseGroup != null">
        purchase_group = #{purchaseGroup,jdbcType=VARCHAR},
      </if>
      <if test="purchaseType != null">
        purchase_type = #{purchaseType,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null">
        supplier_no = #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierCompanyCode != null">
        supplier_company_code = #{supplierCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierCompanyName != null">
        supplier_company_name = #{supplierCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="supplierWarehouseCode != null">
        supplier_warehouse_code = #{supplierWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierWarehouseName != null">
        supplier_warehouse_name = #{supplierWarehouseName,jdbcType=VARCHAR},
      </if>
      <if test="supplierSalesmanCode != null">
        supplier_salesman_code = #{supplierSalesmanCode,jdbcType=VARCHAR},
      </if>
      <if test="payCode != null">
        pay_code = #{payCode,jdbcType=VARCHAR},
      </if>
      <if test="purchaseDate != null">
        purchase_date = #{purchaseDate,jdbcType=TIMESTAMP},
      </if>
      <if test="purchasePerson != null">
        purchase_person = #{purchasePerson,jdbcType=VARCHAR},
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount = #{noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        tax_amount = #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="haveTaxAmount != null">
        have_tax_amount = #{haveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="tel != null">
        tel = #{tel,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrder">
    update srm_inner_purchase_order
    set purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      purchaser_code = #{purchaserCode,jdbcType=VARCHAR},
      purchase_delete_id = #{purchaseDeleteId,jdbcType=VARCHAR},
      confirm_status = #{confirmStatus,jdbcType=INTEGER},
      order_reserve_status = #{orderReserveStatus,jdbcType=INTEGER},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      purchase_org = #{purchaseOrg,jdbcType=VARCHAR},
      purchase_org_name = #{purchaseOrgName,jdbcType=VARCHAR},
      purchase_group = #{purchaseGroup,jdbcType=VARCHAR},
      purchase_type = #{purchaseType,jdbcType=VARCHAR},
      supplier_no = #{supplierNo,jdbcType=VARCHAR},
      supplier_company_code = #{supplierCompanyCode,jdbcType=VARCHAR},
      supplier_company_name = #{supplierCompanyName,jdbcType=VARCHAR},
      supplier_warehouse_code = #{supplierWarehouseCode,jdbcType=VARCHAR},
      supplier_warehouse_name = #{supplierWarehouseName,jdbcType=VARCHAR},
      supplier_salesman_code = #{supplierSalesmanCode,jdbcType=VARCHAR},
      pay_code = #{payCode,jdbcType=VARCHAR},
      purchase_date = #{purchaseDate,jdbcType=TIMESTAMP},
      purchase_person = #{purchasePerson,jdbcType=VARCHAR},
      no_tax_amount = #{noTaxAmount,jdbcType=DECIMAL},
      tax_amount = #{taxAmount,jdbcType=DECIMAL},
      have_tax_amount = #{haveTaxAmount,jdbcType=DECIMAL},
      address = #{address,jdbcType=VARCHAR},
      tel = #{tel,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>