<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SupplierBasicInfoMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SupplierBasicInfo">
    <id column="supplier_id" jdbcType="BIGINT" property="supplierId" />
    <result column="supplier_no" jdbcType="VARCHAR" property="supplierNo" />
    <result column="supplier_mark" jdbcType="TINYINT" property="supplierMark" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="supplier_desc" jdbcType="VARCHAR" property="supplierDesc" />
    <result column="supplier_opcode" jdbcType="VARCHAR" property="supplierOpcode" />
    <result column="supplier_oldname" jdbcType="VARCHAR" property="supplierOldname" />
    <result column="taxno" jdbcType="VARCHAR" property="taxno" />
    <result column="supplier_group_no" jdbcType="VARCHAR" property="supplierGroupNo" />
    <result column="father_supplier_no" jdbcType="VARCHAR" property="fatherSupplierNo" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="fasupgroup_no" jdbcType="VARCHAR" property="fasupgroupNo" />
    <result column="corporation" jdbcType="VARCHAR" property="corporation" />
    <result column="country" jdbcType="VARCHAR" property="country" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="regadd" jdbcType="VARCHAR" property="regadd" />
    <result column="supplier_whadd" jdbcType="VARCHAR" property="supplierWhadd" />
    <result column="supplier_tel" jdbcType="VARCHAR" property="supplierTel" />
    <result column="supplier_contact" jdbcType="VARCHAR" property="supplierContact" />
    <result column="supplier_mobil" jdbcType="VARCHAR" property="supplierMobil" />
    <result column="supplier_fax" jdbcType="VARCHAR" property="supplierFax" />
    <result column="supplier_language" jdbcType="VARCHAR" property="supplierLanguage" />
    <result column="orgfreez" jdbcType="VARCHAR" property="orgfreez" />
    <result column="deleteind" jdbcType="VARCHAR" property="deleteind" />
    <result column="channeltype" jdbcType="VARCHAR" property="channeltype" />
    <result column="orgordfreez" jdbcType="VARCHAR" property="orgordfreez" />
    <result column="DUPLICATE" jdbcType="VARCHAR" property="duplicate" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="credit_starttime_jt" jdbcType="VARCHAR" property="creditStarttimeJt" />
    <result column="credit_endtime_jt" jdbcType="VARCHAR" property="creditEndtimeJt" />
    <result column="temporarycreditday_jt" jdbcType="VARCHAR" property="temporarycreditdayJt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    supplier_id, supplier_no, supplier_mark, supplier_name, supplier_desc, supplier_opcode, 
    supplier_oldname, taxno, supplier_group_no, father_supplier_no, remarks, fasupgroup_no, 
    corporation, country, province, city, area, regadd, supplier_whadd, supplier_tel, 
    supplier_contact, supplier_mobil, supplier_fax, supplier_language, orgfreez, deleteind, 
    channeltype, orgordfreez, DUPLICATE, version, created_by, gmt_create, updated_by, 
    gmt_update, extend, `status`, credit_starttime_jt, credit_endtime_jt, temporarycreditday_jt
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SupplierBasicInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from supplier_basic_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from supplier_basic_info
    where supplier_id = #{supplierId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from supplier_basic_info
    where supplier_id = #{supplierId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SupplierBasicInfoExample">
    delete from supplier_basic_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SupplierBasicInfo">
    insert into supplier_basic_info (supplier_id, supplier_no, supplier_mark, 
      supplier_name, supplier_desc, supplier_opcode, 
      supplier_oldname, taxno, supplier_group_no, 
      father_supplier_no, remarks, fasupgroup_no, 
      corporation, country, province, 
      city, area, regadd, 
      supplier_whadd, supplier_tel, supplier_contact, 
      supplier_mobil, supplier_fax, supplier_language, 
      orgfreez, deleteind, channeltype, 
      orgordfreez, DUPLICATE, version, 
      created_by, gmt_create, updated_by, 
      gmt_update, extend, `status`, 
      credit_starttime_jt, credit_endtime_jt, temporarycreditday_jt
      )
    values (#{supplierId,jdbcType=BIGINT}, #{supplierNo,jdbcType=VARCHAR}, #{supplierMark,jdbcType=TINYINT}, 
      #{supplierName,jdbcType=VARCHAR}, #{supplierDesc,jdbcType=VARCHAR}, #{supplierOpcode,jdbcType=VARCHAR}, 
      #{supplierOldname,jdbcType=VARCHAR}, #{taxno,jdbcType=VARCHAR}, #{supplierGroupNo,jdbcType=VARCHAR}, 
      #{fatherSupplierNo,jdbcType=VARCHAR}, #{remarks,jdbcType=VARCHAR}, #{fasupgroupNo,jdbcType=VARCHAR}, 
      #{corporation,jdbcType=VARCHAR}, #{country,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, 
      #{city,jdbcType=VARCHAR}, #{area,jdbcType=VARCHAR}, #{regadd,jdbcType=VARCHAR}, 
      #{supplierWhadd,jdbcType=VARCHAR}, #{supplierTel,jdbcType=VARCHAR}, #{supplierContact,jdbcType=VARCHAR}, 
      #{supplierMobil,jdbcType=VARCHAR}, #{supplierFax,jdbcType=VARCHAR}, #{supplierLanguage,jdbcType=VARCHAR}, 
      #{orgfreez,jdbcType=VARCHAR}, #{deleteind,jdbcType=VARCHAR}, #{channeltype,jdbcType=VARCHAR}, 
      #{orgordfreez,jdbcType=VARCHAR}, #{duplicate,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{creditStarttimeJt,jdbcType=VARCHAR}, #{creditEndtimeJt,jdbcType=VARCHAR}, #{temporarycreditdayJt,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SupplierBasicInfo">
    insert into supplier_basic_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="supplierNo != null">
        supplier_no,
      </if>
      <if test="supplierMark != null">
        supplier_mark,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="supplierDesc != null">
        supplier_desc,
      </if>
      <if test="supplierOpcode != null">
        supplier_opcode,
      </if>
      <if test="supplierOldname != null">
        supplier_oldname,
      </if>
      <if test="taxno != null">
        taxno,
      </if>
      <if test="supplierGroupNo != null">
        supplier_group_no,
      </if>
      <if test="fatherSupplierNo != null">
        father_supplier_no,
      </if>
      <if test="remarks != null">
        remarks,
      </if>
      <if test="fasupgroupNo != null">
        fasupgroup_no,
      </if>
      <if test="corporation != null">
        corporation,
      </if>
      <if test="country != null">
        country,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="area != null">
        area,
      </if>
      <if test="regadd != null">
        regadd,
      </if>
      <if test="supplierWhadd != null">
        supplier_whadd,
      </if>
      <if test="supplierTel != null">
        supplier_tel,
      </if>
      <if test="supplierContact != null">
        supplier_contact,
      </if>
      <if test="supplierMobil != null">
        supplier_mobil,
      </if>
      <if test="supplierFax != null">
        supplier_fax,
      </if>
      <if test="supplierLanguage != null">
        supplier_language,
      </if>
      <if test="orgfreez != null">
        orgfreez,
      </if>
      <if test="deleteind != null">
        deleteind,
      </if>
      <if test="channeltype != null">
        channeltype,
      </if>
      <if test="orgordfreez != null">
        orgordfreez,
      </if>
      <if test="duplicate != null">
        DUPLICATE,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="creditStarttimeJt != null">
        credit_starttime_jt,
      </if>
      <if test="creditEndtimeJt != null">
        credit_endtime_jt,
      </if>
      <if test="temporarycreditdayJt != null">
        temporarycreditday_jt,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="supplierId != null">
        #{supplierId,jdbcType=BIGINT},
      </if>
      <if test="supplierNo != null">
        #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierMark != null">
        #{supplierMark,jdbcType=TINYINT},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="supplierDesc != null">
        #{supplierDesc,jdbcType=VARCHAR},
      </if>
      <if test="supplierOpcode != null">
        #{supplierOpcode,jdbcType=VARCHAR},
      </if>
      <if test="supplierOldname != null">
        #{supplierOldname,jdbcType=VARCHAR},
      </if>
      <if test="taxno != null">
        #{taxno,jdbcType=VARCHAR},
      </if>
      <if test="supplierGroupNo != null">
        #{supplierGroupNo,jdbcType=VARCHAR},
      </if>
      <if test="fatherSupplierNo != null">
        #{fatherSupplierNo,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="fasupgroupNo != null">
        #{fasupgroupNo,jdbcType=VARCHAR},
      </if>
      <if test="corporation != null">
        #{corporation,jdbcType=VARCHAR},
      </if>
      <if test="country != null">
        #{country,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="regadd != null">
        #{regadd,jdbcType=VARCHAR},
      </if>
      <if test="supplierWhadd != null">
        #{supplierWhadd,jdbcType=VARCHAR},
      </if>
      <if test="supplierTel != null">
        #{supplierTel,jdbcType=VARCHAR},
      </if>
      <if test="supplierContact != null">
        #{supplierContact,jdbcType=VARCHAR},
      </if>
      <if test="supplierMobil != null">
        #{supplierMobil,jdbcType=VARCHAR},
      </if>
      <if test="supplierFax != null">
        #{supplierFax,jdbcType=VARCHAR},
      </if>
      <if test="supplierLanguage != null">
        #{supplierLanguage,jdbcType=VARCHAR},
      </if>
      <if test="orgfreez != null">
        #{orgfreez,jdbcType=VARCHAR},
      </if>
      <if test="deleteind != null">
        #{deleteind,jdbcType=VARCHAR},
      </if>
      <if test="channeltype != null">
        #{channeltype,jdbcType=VARCHAR},
      </if>
      <if test="orgordfreez != null">
        #{orgordfreez,jdbcType=VARCHAR},
      </if>
      <if test="duplicate != null">
        #{duplicate,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="creditStarttimeJt != null">
        #{creditStarttimeJt,jdbcType=VARCHAR},
      </if>
      <if test="creditEndtimeJt != null">
        #{creditEndtimeJt,jdbcType=VARCHAR},
      </if>
      <if test="temporarycreditdayJt != null">
        #{temporarycreditdayJt,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SupplierBasicInfoExample" resultType="java.lang.Long">
    select count(*) from supplier_basic_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update supplier_basic_info
    <set>
      <if test="record.supplierId != null">
        supplier_id = #{record.supplierId,jdbcType=BIGINT},
      </if>
      <if test="record.supplierNo != null">
        supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierMark != null">
        supplier_mark = #{record.supplierMark,jdbcType=TINYINT},
      </if>
      <if test="record.supplierName != null">
        supplier_name = #{record.supplierName,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierDesc != null">
        supplier_desc = #{record.supplierDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierOpcode != null">
        supplier_opcode = #{record.supplierOpcode,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierOldname != null">
        supplier_oldname = #{record.supplierOldname,jdbcType=VARCHAR},
      </if>
      <if test="record.taxno != null">
        taxno = #{record.taxno,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierGroupNo != null">
        supplier_group_no = #{record.supplierGroupNo,jdbcType=VARCHAR},
      </if>
      <if test="record.fatherSupplierNo != null">
        father_supplier_no = #{record.fatherSupplierNo,jdbcType=VARCHAR},
      </if>
      <if test="record.remarks != null">
        remarks = #{record.remarks,jdbcType=VARCHAR},
      </if>
      <if test="record.fasupgroupNo != null">
        fasupgroup_no = #{record.fasupgroupNo,jdbcType=VARCHAR},
      </if>
      <if test="record.corporation != null">
        corporation = #{record.corporation,jdbcType=VARCHAR},
      </if>
      <if test="record.country != null">
        country = #{record.country,jdbcType=VARCHAR},
      </if>
      <if test="record.province != null">
        province = #{record.province,jdbcType=VARCHAR},
      </if>
      <if test="record.city != null">
        city = #{record.city,jdbcType=VARCHAR},
      </if>
      <if test="record.area != null">
        area = #{record.area,jdbcType=VARCHAR},
      </if>
      <if test="record.regadd != null">
        regadd = #{record.regadd,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierWhadd != null">
        supplier_whadd = #{record.supplierWhadd,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierTel != null">
        supplier_tel = #{record.supplierTel,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierContact != null">
        supplier_contact = #{record.supplierContact,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierMobil != null">
        supplier_mobil = #{record.supplierMobil,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierFax != null">
        supplier_fax = #{record.supplierFax,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierLanguage != null">
        supplier_language = #{record.supplierLanguage,jdbcType=VARCHAR},
      </if>
      <if test="record.orgfreez != null">
        orgfreez = #{record.orgfreez,jdbcType=VARCHAR},
      </if>
      <if test="record.deleteind != null">
        deleteind = #{record.deleteind,jdbcType=VARCHAR},
      </if>
      <if test="record.channeltype != null">
        channeltype = #{record.channeltype,jdbcType=VARCHAR},
      </if>
      <if test="record.orgordfreez != null">
        orgordfreez = #{record.orgordfreez,jdbcType=VARCHAR},
      </if>
      <if test="record.duplicate != null">
        DUPLICATE = #{record.duplicate,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.creditStarttimeJt != null">
        credit_starttime_jt = #{record.creditStarttimeJt,jdbcType=VARCHAR},
      </if>
      <if test="record.creditEndtimeJt != null">
        credit_endtime_jt = #{record.creditEndtimeJt,jdbcType=VARCHAR},
      </if>
      <if test="record.temporarycreditdayJt != null">
        temporarycreditday_jt = #{record.temporarycreditdayJt,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update supplier_basic_info
    set supplier_id = #{record.supplierId,jdbcType=BIGINT},
      supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      supplier_mark = #{record.supplierMark,jdbcType=TINYINT},
      supplier_name = #{record.supplierName,jdbcType=VARCHAR},
      supplier_desc = #{record.supplierDesc,jdbcType=VARCHAR},
      supplier_opcode = #{record.supplierOpcode,jdbcType=VARCHAR},
      supplier_oldname = #{record.supplierOldname,jdbcType=VARCHAR},
      taxno = #{record.taxno,jdbcType=VARCHAR},
      supplier_group_no = #{record.supplierGroupNo,jdbcType=VARCHAR},
      father_supplier_no = #{record.fatherSupplierNo,jdbcType=VARCHAR},
      remarks = #{record.remarks,jdbcType=VARCHAR},
      fasupgroup_no = #{record.fasupgroupNo,jdbcType=VARCHAR},
      corporation = #{record.corporation,jdbcType=VARCHAR},
      country = #{record.country,jdbcType=VARCHAR},
      province = #{record.province,jdbcType=VARCHAR},
      city = #{record.city,jdbcType=VARCHAR},
      area = #{record.area,jdbcType=VARCHAR},
      regadd = #{record.regadd,jdbcType=VARCHAR},
      supplier_whadd = #{record.supplierWhadd,jdbcType=VARCHAR},
      supplier_tel = #{record.supplierTel,jdbcType=VARCHAR},
      supplier_contact = #{record.supplierContact,jdbcType=VARCHAR},
      supplier_mobil = #{record.supplierMobil,jdbcType=VARCHAR},
      supplier_fax = #{record.supplierFax,jdbcType=VARCHAR},
      supplier_language = #{record.supplierLanguage,jdbcType=VARCHAR},
      orgfreez = #{record.orgfreez,jdbcType=VARCHAR},
      deleteind = #{record.deleteind,jdbcType=VARCHAR},
      channeltype = #{record.channeltype,jdbcType=VARCHAR},
      orgordfreez = #{record.orgordfreez,jdbcType=VARCHAR},
      DUPLICATE = #{record.duplicate,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=TINYINT},
      credit_starttime_jt = #{record.creditStarttimeJt,jdbcType=VARCHAR},
      credit_endtime_jt = #{record.creditEndtimeJt,jdbcType=VARCHAR},
      temporarycreditday_jt = #{record.temporarycreditdayJt,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SupplierBasicInfo">
    update supplier_basic_info
    <set>
      <if test="supplierNo != null">
        supplier_no = #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierMark != null">
        supplier_mark = #{supplierMark,jdbcType=TINYINT},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="supplierDesc != null">
        supplier_desc = #{supplierDesc,jdbcType=VARCHAR},
      </if>
      <if test="supplierOpcode != null">
        supplier_opcode = #{supplierOpcode,jdbcType=VARCHAR},
      </if>
      <if test="supplierOldname != null">
        supplier_oldname = #{supplierOldname,jdbcType=VARCHAR},
      </if>
      <if test="taxno != null">
        taxno = #{taxno,jdbcType=VARCHAR},
      </if>
      <if test="supplierGroupNo != null">
        supplier_group_no = #{supplierGroupNo,jdbcType=VARCHAR},
      </if>
      <if test="fatherSupplierNo != null">
        father_supplier_no = #{fatherSupplierNo,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="fasupgroupNo != null">
        fasupgroup_no = #{fasupgroupNo,jdbcType=VARCHAR},
      </if>
      <if test="corporation != null">
        corporation = #{corporation,jdbcType=VARCHAR},
      </if>
      <if test="country != null">
        country = #{country,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        area = #{area,jdbcType=VARCHAR},
      </if>
      <if test="regadd != null">
        regadd = #{regadd,jdbcType=VARCHAR},
      </if>
      <if test="supplierWhadd != null">
        supplier_whadd = #{supplierWhadd,jdbcType=VARCHAR},
      </if>
      <if test="supplierTel != null">
        supplier_tel = #{supplierTel,jdbcType=VARCHAR},
      </if>
      <if test="supplierContact != null">
        supplier_contact = #{supplierContact,jdbcType=VARCHAR},
      </if>
      <if test="supplierMobil != null">
        supplier_mobil = #{supplierMobil,jdbcType=VARCHAR},
      </if>
      <if test="supplierFax != null">
        supplier_fax = #{supplierFax,jdbcType=VARCHAR},
      </if>
      <if test="supplierLanguage != null">
        supplier_language = #{supplierLanguage,jdbcType=VARCHAR},
      </if>
      <if test="orgfreez != null">
        orgfreez = #{orgfreez,jdbcType=VARCHAR},
      </if>
      <if test="deleteind != null">
        deleteind = #{deleteind,jdbcType=VARCHAR},
      </if>
      <if test="channeltype != null">
        channeltype = #{channeltype,jdbcType=VARCHAR},
      </if>
      <if test="orgordfreez != null">
        orgordfreez = #{orgordfreez,jdbcType=VARCHAR},
      </if>
      <if test="duplicate != null">
        DUPLICATE = #{duplicate,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="creditStarttimeJt != null">
        credit_starttime_jt = #{creditStarttimeJt,jdbcType=VARCHAR},
      </if>
      <if test="creditEndtimeJt != null">
        credit_endtime_jt = #{creditEndtimeJt,jdbcType=VARCHAR},
      </if>
      <if test="temporarycreditdayJt != null">
        temporarycreditday_jt = #{temporarycreditdayJt,jdbcType=VARCHAR},
      </if>
    </set>
    where supplier_id = #{supplierId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SupplierBasicInfo">
    update supplier_basic_info
    set supplier_no = #{supplierNo,jdbcType=VARCHAR},
      supplier_mark = #{supplierMark,jdbcType=TINYINT},
      supplier_name = #{supplierName,jdbcType=VARCHAR},
      supplier_desc = #{supplierDesc,jdbcType=VARCHAR},
      supplier_opcode = #{supplierOpcode,jdbcType=VARCHAR},
      supplier_oldname = #{supplierOldname,jdbcType=VARCHAR},
      taxno = #{taxno,jdbcType=VARCHAR},
      supplier_group_no = #{supplierGroupNo,jdbcType=VARCHAR},
      father_supplier_no = #{fatherSupplierNo,jdbcType=VARCHAR},
      remarks = #{remarks,jdbcType=VARCHAR},
      fasupgroup_no = #{fasupgroupNo,jdbcType=VARCHAR},
      corporation = #{corporation,jdbcType=VARCHAR},
      country = #{country,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      area = #{area,jdbcType=VARCHAR},
      regadd = #{regadd,jdbcType=VARCHAR},
      supplier_whadd = #{supplierWhadd,jdbcType=VARCHAR},
      supplier_tel = #{supplierTel,jdbcType=VARCHAR},
      supplier_contact = #{supplierContact,jdbcType=VARCHAR},
      supplier_mobil = #{supplierMobil,jdbcType=VARCHAR},
      supplier_fax = #{supplierFax,jdbcType=VARCHAR},
      supplier_language = #{supplierLanguage,jdbcType=VARCHAR},
      orgfreez = #{orgfreez,jdbcType=VARCHAR},
      deleteind = #{deleteind,jdbcType=VARCHAR},
      channeltype = #{channeltype,jdbcType=VARCHAR},
      orgordfreez = #{orgordfreez,jdbcType=VARCHAR},
      DUPLICATE = #{duplicate,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      credit_starttime_jt = #{creditStarttimeJt,jdbcType=VARCHAR},
      credit_endtime_jt = #{creditEndtimeJt,jdbcType=VARCHAR},
      temporarycreditday_jt = #{temporarycreditdayJt,jdbcType=VARCHAR}
    where supplier_id = #{supplierId,jdbcType=BIGINT}
  </update>
</mapper>