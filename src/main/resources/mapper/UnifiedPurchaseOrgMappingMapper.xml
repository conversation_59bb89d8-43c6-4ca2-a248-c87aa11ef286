<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.UnifiedPurchaseOrgMappingMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.UnifiedPurchaseOrgMapping">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="bdp_key" jdbcType="VARCHAR" property="bdpKey" />
    <result column="business_spell" jdbcType="VARCHAR" property="businessSpell" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="work_date" jdbcType="VARCHAR" property="workDate" />
    <result column="sap_name" jdbcType="VARCHAR" property="sapName" />
    <result column="sap_code" jdbcType="VARCHAR" property="sapCode" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, org_id, org_name, business_id, bdp_key, business_spell, gmt_create, gmt_update, 
    `status`, extend, version, created_by, update_by, work_date, sap_name, sap_code
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.UnifiedPurchaseOrgMappingExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from unified_purchase_org_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from unified_purchase_org_mapping
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from unified_purchase_org_mapping
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.UnifiedPurchaseOrgMappingExample">
    delete from unified_purchase_org_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.UnifiedPurchaseOrgMapping">
    insert into unified_purchase_org_mapping (id, org_id, org_name, 
      business_id, bdp_key, business_spell, 
      gmt_create, gmt_update, `status`, 
      extend, version, created_by, 
      update_by, work_date, sap_name, 
      sap_code)
    values (#{id,jdbcType=INTEGER}, #{orgId,jdbcType=BIGINT}, #{orgName,jdbcType=VARCHAR}, 
      #{businessId,jdbcType=BIGINT}, #{bdpKey,jdbcType=VARCHAR}, #{businessSpell,jdbcType=VARCHAR}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}, 
      #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, 
      #{updateBy,jdbcType=BIGINT}, #{workDate,jdbcType=VARCHAR}, #{sapName,jdbcType=VARCHAR}, 
      #{sapCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.UnifiedPurchaseOrgMapping">
    insert into unified_purchase_org_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="bdpKey != null">
        bdp_key,
      </if>
      <if test="businessSpell != null">
        business_spell,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="workDate != null">
        work_date,
      </if>
      <if test="sapName != null">
        sap_name,
      </if>
      <if test="sapCode != null">
        sap_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="bdpKey != null">
        #{bdpKey,jdbcType=VARCHAR},
      </if>
      <if test="businessSpell != null">
        #{businessSpell,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="workDate != null">
        #{workDate,jdbcType=VARCHAR},
      </if>
      <if test="sapName != null">
        #{sapName,jdbcType=VARCHAR},
      </if>
      <if test="sapCode != null">
        #{sapCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.UnifiedPurchaseOrgMappingExample" resultType="java.lang.Long">
    select count(*) from unified_purchase_org_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update unified_purchase_org_mapping
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.bdpKey != null">
        bdp_key = #{record.bdpKey,jdbcType=VARCHAR},
      </if>
      <if test="record.businessSpell != null">
        business_spell = #{record.businessSpell,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=BIGINT},
      </if>
      <if test="record.workDate != null">
        work_date = #{record.workDate,jdbcType=VARCHAR},
      </if>
      <if test="record.sapName != null">
        sap_name = #{record.sapName,jdbcType=VARCHAR},
      </if>
      <if test="record.sapCode != null">
        sap_code = #{record.sapCode,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update unified_purchase_org_mapping
    set id = #{record.id,jdbcType=INTEGER},
      org_id = #{record.orgId,jdbcType=BIGINT},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      business_id = #{record.businessId,jdbcType=BIGINT},
      bdp_key = #{record.bdpKey,jdbcType=VARCHAR},
      business_spell = #{record.businessSpell,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      `status` = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      update_by = #{record.updateBy,jdbcType=BIGINT},
      work_date = #{record.workDate,jdbcType=VARCHAR},
      sap_name = #{record.sapName,jdbcType=VARCHAR},
      sap_code = #{record.sapCode,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.UnifiedPurchaseOrgMapping">
    update unified_purchase_org_mapping
    <set>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="bdpKey != null">
        bdp_key = #{bdpKey,jdbcType=VARCHAR},
      </if>
      <if test="businessSpell != null">
        business_spell = #{businessSpell,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="workDate != null">
        work_date = #{workDate,jdbcType=VARCHAR},
      </if>
      <if test="sapName != null">
        sap_name = #{sapName,jdbcType=VARCHAR},
      </if>
      <if test="sapCode != null">
        sap_code = #{sapCode,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.UnifiedPurchaseOrgMapping">
    update unified_purchase_org_mapping
    set org_id = #{orgId,jdbcType=BIGINT},
      org_name = #{orgName,jdbcType=VARCHAR},
      business_id = #{businessId,jdbcType=BIGINT},
      bdp_key = #{bdpKey,jdbcType=VARCHAR},
      business_spell = #{businessSpell,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      work_date = #{workDate,jdbcType=VARCHAR},
      sap_name = #{sapName,jdbcType=VARCHAR},
      sap_code = #{sapCode,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>