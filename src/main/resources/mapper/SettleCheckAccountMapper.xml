<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SettleCheckAccountMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SettleCheckAccount">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="delivery_no" jdbcType="VARCHAR" property="deliveryNo" />
    <result column="delivery_date" jdbcType="TIMESTAMP" property="deliveryDate" />
    <result column="purchase_order_no" jdbcType="VARCHAR" property="purchaseOrderNo" />
    <result column="purchase_order_id" jdbcType="VARCHAR" property="purchaseOrderId" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="delivery_count" jdbcType="DECIMAL" property="deliveryCount" />
    <result column="no_tax_amount" jdbcType="DECIMAL" property="noTaxAmount" />
    <result column="have_tax_amount" jdbcType="DECIMAL" property="haveTaxAmount" />
    <result column="move_type" jdbcType="INTEGER" property="moveType" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="check_status" jdbcType="INTEGER" property="checkStatus" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="compny_code" jdbcType="VARCHAR" property="compnyCode" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="supplier_no" jdbcType="VARCHAR" property="supplierNo" />
    <result column="supplier_salesman_code" jdbcType="VARCHAR" property="supplierSalesmanCode" />
    <result column="unite_key" jdbcType="VARCHAR" property="uniteKey" />
    <result column="warehouse" jdbcType="VARCHAR" property="warehouse" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="pay_code" jdbcType="VARCHAR" property="payCode" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, delivery_no, delivery_date, purchase_order_no, purchase_order_id, create_date, 
    delivery_count, no_tax_amount, have_tax_amount, move_type, order_type, check_status, 
    org_id, compny_code, org_name, supplier_no, supplier_salesman_code, unite_key, warehouse, 
    warehouse_name, pay_code, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SettleCheckAccountExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from settle_check_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from settle_check_account
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from settle_check_account
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SettleCheckAccountExample">
    delete from settle_check_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SettleCheckAccount">
    insert into settle_check_account (id, delivery_no, delivery_date, 
      purchase_order_no, purchase_order_id, create_date, 
      delivery_count, no_tax_amount, have_tax_amount, 
      move_type, order_type, check_status, 
      org_id, compny_code, org_name, 
      supplier_no, supplier_salesman_code, unite_key, 
      warehouse, warehouse_name, pay_code, 
      gmt_create, gmt_update)
    values (#{id,jdbcType=INTEGER}, #{deliveryNo,jdbcType=VARCHAR}, #{deliveryDate,jdbcType=TIMESTAMP}, 
      #{purchaseOrderNo,jdbcType=VARCHAR}, #{purchaseOrderId,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}, 
      #{deliveryCount,jdbcType=DECIMAL}, #{noTaxAmount,jdbcType=DECIMAL}, #{haveTaxAmount,jdbcType=DECIMAL}, 
      #{moveType,jdbcType=INTEGER}, #{orderType,jdbcType=INTEGER}, #{checkStatus,jdbcType=INTEGER}, 
      #{orgId,jdbcType=VARCHAR}, #{compnyCode,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR}, 
      #{supplierNo,jdbcType=VARCHAR}, #{supplierSalesmanCode,jdbcType=VARCHAR}, #{uniteKey,jdbcType=VARCHAR}, 
      #{warehouse,jdbcType=VARCHAR}, #{warehouseName,jdbcType=VARCHAR}, #{payCode,jdbcType=VARCHAR}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SettleCheckAccount">
    insert into settle_check_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deliveryNo != null">
        delivery_no,
      </if>
      <if test="deliveryDate != null">
        delivery_date,
      </if>
      <if test="purchaseOrderNo != null">
        purchase_order_no,
      </if>
      <if test="purchaseOrderId != null">
        purchase_order_id,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="deliveryCount != null">
        delivery_count,
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount,
      </if>
      <if test="haveTaxAmount != null">
        have_tax_amount,
      </if>
      <if test="moveType != null">
        move_type,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="checkStatus != null">
        check_status,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="compnyCode != null">
        compny_code,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="supplierNo != null">
        supplier_no,
      </if>
      <if test="supplierSalesmanCode != null">
        supplier_salesman_code,
      </if>
      <if test="uniteKey != null">
        unite_key,
      </if>
      <if test="warehouse != null">
        warehouse,
      </if>
      <if test="warehouseName != null">
        warehouse_name,
      </if>
      <if test="payCode != null">
        pay_code,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deliveryNo != null">
        #{deliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryDate != null">
        #{deliveryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="purchaseOrderNo != null">
        #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderId != null">
        #{purchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryCount != null">
        #{deliveryCount,jdbcType=DECIMAL},
      </if>
      <if test="noTaxAmount != null">
        #{noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="haveTaxAmount != null">
        #{haveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="moveType != null">
        #{moveType,jdbcType=INTEGER},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="checkStatus != null">
        #{checkStatus,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="compnyCode != null">
        #{compnyCode,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null">
        #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierSalesmanCode != null">
        #{supplierSalesmanCode,jdbcType=VARCHAR},
      </if>
      <if test="uniteKey != null">
        #{uniteKey,jdbcType=VARCHAR},
      </if>
      <if test="warehouse != null">
        #{warehouse,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="payCode != null">
        #{payCode,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SettleCheckAccountExample" resultType="java.lang.Long">
    select count(*) from settle_check_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update settle_check_account
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.deliveryNo != null">
        delivery_no = #{record.deliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryDate != null">
        delivery_date = #{record.deliveryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.purchaseOrderNo != null">
        purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrderId != null">
        purchase_order_id = #{record.purchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null">
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deliveryCount != null">
        delivery_count = #{record.deliveryCount,jdbcType=DECIMAL},
      </if>
      <if test="record.noTaxAmount != null">
        no_tax_amount = #{record.noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.haveTaxAmount != null">
        have_tax_amount = #{record.haveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.moveType != null">
        move_type = #{record.moveType,jdbcType=INTEGER},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=INTEGER},
      </if>
      <if test="record.checkStatus != null">
        check_status = #{record.checkStatus,jdbcType=INTEGER},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=VARCHAR},
      </if>
      <if test="record.compnyCode != null">
        compny_code = #{record.compnyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierNo != null">
        supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierSalesmanCode != null">
        supplier_salesman_code = #{record.supplierSalesmanCode,jdbcType=VARCHAR},
      </if>
      <if test="record.uniteKey != null">
        unite_key = #{record.uniteKey,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouse != null">
        warehouse = #{record.warehouse,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseName != null">
        warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="record.payCode != null">
        pay_code = #{record.payCode,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update settle_check_account
    set id = #{record.id,jdbcType=INTEGER},
      delivery_no = #{record.deliveryNo,jdbcType=VARCHAR},
      delivery_date = #{record.deliveryDate,jdbcType=TIMESTAMP},
      purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      purchase_order_id = #{record.purchaseOrderId,jdbcType=VARCHAR},
      create_date = #{record.createDate,jdbcType=TIMESTAMP},
      delivery_count = #{record.deliveryCount,jdbcType=DECIMAL},
      no_tax_amount = #{record.noTaxAmount,jdbcType=DECIMAL},
      have_tax_amount = #{record.haveTaxAmount,jdbcType=DECIMAL},
      move_type = #{record.moveType,jdbcType=INTEGER},
      order_type = #{record.orderType,jdbcType=INTEGER},
      check_status = #{record.checkStatus,jdbcType=INTEGER},
      org_id = #{record.orgId,jdbcType=VARCHAR},
      compny_code = #{record.compnyCode,jdbcType=VARCHAR},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      supplier_salesman_code = #{record.supplierSalesmanCode,jdbcType=VARCHAR},
      unite_key = #{record.uniteKey,jdbcType=VARCHAR},
      warehouse = #{record.warehouse,jdbcType=VARCHAR},
      warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      pay_code = #{record.payCode,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SettleCheckAccount">
    update settle_check_account
    <set>
      <if test="deliveryNo != null">
        delivery_no = #{deliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryDate != null">
        delivery_date = #{deliveryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="purchaseOrderNo != null">
        purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderId != null">
        purchase_order_id = #{purchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryCount != null">
        delivery_count = #{deliveryCount,jdbcType=DECIMAL},
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount = #{noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="haveTaxAmount != null">
        have_tax_amount = #{haveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="moveType != null">
        move_type = #{moveType,jdbcType=INTEGER},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="checkStatus != null">
        check_status = #{checkStatus,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="compnyCode != null">
        compny_code = #{compnyCode,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null">
        supplier_no = #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierSalesmanCode != null">
        supplier_salesman_code = #{supplierSalesmanCode,jdbcType=VARCHAR},
      </if>
      <if test="uniteKey != null">
        unite_key = #{uniteKey,jdbcType=VARCHAR},
      </if>
      <if test="warehouse != null">
        warehouse = #{warehouse,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="payCode != null">
        pay_code = #{payCode,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SettleCheckAccount">
    update settle_check_account
    set delivery_no = #{deliveryNo,jdbcType=VARCHAR},
      delivery_date = #{deliveryDate,jdbcType=TIMESTAMP},
      purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      purchase_order_id = #{purchaseOrderId,jdbcType=VARCHAR},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      delivery_count = #{deliveryCount,jdbcType=DECIMAL},
      no_tax_amount = #{noTaxAmount,jdbcType=DECIMAL},
      have_tax_amount = #{haveTaxAmount,jdbcType=DECIMAL},
      move_type = #{moveType,jdbcType=INTEGER},
      order_type = #{orderType,jdbcType=INTEGER},
      check_status = #{checkStatus,jdbcType=INTEGER},
      org_id = #{orgId,jdbcType=VARCHAR},
      compny_code = #{compnyCode,jdbcType=VARCHAR},
      org_name = #{orgName,jdbcType=VARCHAR},
      supplier_no = #{supplierNo,jdbcType=VARCHAR},
      supplier_salesman_code = #{supplierSalesmanCode,jdbcType=VARCHAR},
      unite_key = #{uniteKey,jdbcType=VARCHAR},
      warehouse = #{warehouse,jdbcType=VARCHAR},
      warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      pay_code = #{payCode,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>