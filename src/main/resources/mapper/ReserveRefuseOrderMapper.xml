<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.ReserveRefuseOrderMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.ReserveRefuseOrder">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="refuse_order_no" jdbcType="VARCHAR" property="refuseOrderNo" />
    <result column="deliver_status" jdbcType="VARCHAR" property="deliverStatus" />
    <result column="delivery_no" jdbcType="VARCHAR" property="deliveryNo" />
    <result column="purchase_order_no" jdbcType="VARCHAR" property="purchaseOrderNo" />
    <result column="purchase_order_id" jdbcType="VARCHAR" property="purchaseOrderId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="warehouse_no" jdbcType="VARCHAR" property="warehouseNo" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="supplier_no" jdbcType="VARCHAR" property="supplierNo" />
    <result column="send_no" jdbcType="VARCHAR" property="sendNo" />
    <result column="send_id" jdbcType="INTEGER" property="sendId" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="expiration_date" jdbcType="TIMESTAMP" property="expirationDate" />
    <result column="refuse_quantity" jdbcType="DECIMAL" property="refuseQuantity" />
    <result column="refuse_reason" jdbcType="VARCHAR" property="refuseReason" />
    <result column="refuse_time" jdbcType="TIMESTAMP" property="refuseTime" />
    <result column="refuse_person" jdbcType="VARCHAR" property="refusePerson" />
    <result column="deliver_person" jdbcType="VARCHAR" property="deliverPerson" />
    <result column="deliver_time" jdbcType="TIMESTAMP" property="deliverTime" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, refuse_order_no, deliver_status, delivery_no, purchase_order_no, purchase_order_id, 
    company_code, company_name, warehouse_no, warehouse_name, supplier_no, send_no, send_id, 
    goods_code, batch_no, expiration_date, refuse_quantity, refuse_reason, refuse_time, 
    refuse_person, deliver_person, deliver_time, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.ReserveRefuseOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from reserve_refuse_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from reserve_refuse_order
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from reserve_refuse_order
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.ReserveRefuseOrderExample">
    delete from reserve_refuse_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.ReserveRefuseOrder">
    insert into reserve_refuse_order (id, refuse_order_no, deliver_status, 
      delivery_no, purchase_order_no, purchase_order_id, 
      company_code, company_name, warehouse_no, 
      warehouse_name, supplier_no, send_no, 
      send_id, goods_code, batch_no, 
      expiration_date, refuse_quantity, refuse_reason, 
      refuse_time, refuse_person, deliver_person, 
      deliver_time, gmt_create, gmt_update
      )
    values (#{id,jdbcType=INTEGER}, #{refuseOrderNo,jdbcType=VARCHAR}, #{deliverStatus,jdbcType=VARCHAR}, 
      #{deliveryNo,jdbcType=VARCHAR}, #{purchaseOrderNo,jdbcType=VARCHAR}, #{purchaseOrderId,jdbcType=VARCHAR}, 
      #{companyCode,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, #{warehouseNo,jdbcType=VARCHAR}, 
      #{warehouseName,jdbcType=VARCHAR}, #{supplierNo,jdbcType=VARCHAR}, #{sendNo,jdbcType=VARCHAR}, 
      #{sendId,jdbcType=INTEGER}, #{goodsCode,jdbcType=VARCHAR}, #{batchNo,jdbcType=VARCHAR}, 
      #{expirationDate,jdbcType=TIMESTAMP}, #{refuseQuantity,jdbcType=DECIMAL}, #{refuseReason,jdbcType=VARCHAR}, 
      #{refuseTime,jdbcType=TIMESTAMP}, #{refusePerson,jdbcType=VARCHAR}, #{deliverPerson,jdbcType=VARCHAR}, 
      #{deliverTime,jdbcType=TIMESTAMP}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.ReserveRefuseOrder">
    insert into reserve_refuse_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="refuseOrderNo != null">
        refuse_order_no,
      </if>
      <if test="deliverStatus != null">
        deliver_status,
      </if>
      <if test="deliveryNo != null">
        delivery_no,
      </if>
      <if test="purchaseOrderNo != null">
        purchase_order_no,
      </if>
      <if test="purchaseOrderId != null">
        purchase_order_id,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="warehouseNo != null">
        warehouse_no,
      </if>
      <if test="warehouseName != null">
        warehouse_name,
      </if>
      <if test="supplierNo != null">
        supplier_no,
      </if>
      <if test="sendNo != null">
        send_no,
      </if>
      <if test="sendId != null">
        send_id,
      </if>
      <if test="goodsCode != null">
        goods_code,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="expirationDate != null">
        expiration_date,
      </if>
      <if test="refuseQuantity != null">
        refuse_quantity,
      </if>
      <if test="refuseReason != null">
        refuse_reason,
      </if>
      <if test="refuseTime != null">
        refuse_time,
      </if>
      <if test="refusePerson != null">
        refuse_person,
      </if>
      <if test="deliverPerson != null">
        deliver_person,
      </if>
      <if test="deliverTime != null">
        deliver_time,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="refuseOrderNo != null">
        #{refuseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="deliverStatus != null">
        #{deliverStatus,jdbcType=VARCHAR},
      </if>
      <if test="deliveryNo != null">
        #{deliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderNo != null">
        #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderId != null">
        #{purchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseNo != null">
        #{warehouseNo,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null">
        #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="sendNo != null">
        #{sendNo,jdbcType=VARCHAR},
      </if>
      <if test="sendId != null">
        #{sendId,jdbcType=INTEGER},
      </if>
      <if test="goodsCode != null">
        #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="expirationDate != null">
        #{expirationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="refuseQuantity != null">
        #{refuseQuantity,jdbcType=DECIMAL},
      </if>
      <if test="refuseReason != null">
        #{refuseReason,jdbcType=VARCHAR},
      </if>
      <if test="refuseTime != null">
        #{refuseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refusePerson != null">
        #{refusePerson,jdbcType=VARCHAR},
      </if>
      <if test="deliverPerson != null">
        #{deliverPerson,jdbcType=VARCHAR},
      </if>
      <if test="deliverTime != null">
        #{deliverTime,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.ReserveRefuseOrderExample" resultType="java.lang.Long">
    select count(*) from reserve_refuse_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update reserve_refuse_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.refuseOrderNo != null">
        refuse_order_no = #{record.refuseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.deliverStatus != null">
        deliver_status = #{record.deliverStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryNo != null">
        delivery_no = #{record.deliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrderNo != null">
        purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrderId != null">
        purchase_order_id = #{record.purchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseNo != null">
        warehouse_no = #{record.warehouseNo,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseName != null">
        warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierNo != null">
        supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="record.sendNo != null">
        send_no = #{record.sendNo,jdbcType=VARCHAR},
      </if>
      <if test="record.sendId != null">
        send_id = #{record.sendId,jdbcType=INTEGER},
      </if>
      <if test="record.goodsCode != null">
        goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.expirationDate != null">
        expiration_date = #{record.expirationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.refuseQuantity != null">
        refuse_quantity = #{record.refuseQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.refuseReason != null">
        refuse_reason = #{record.refuseReason,jdbcType=VARCHAR},
      </if>
      <if test="record.refuseTime != null">
        refuse_time = #{record.refuseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.refusePerson != null">
        refuse_person = #{record.refusePerson,jdbcType=VARCHAR},
      </if>
      <if test="record.deliverPerson != null">
        deliver_person = #{record.deliverPerson,jdbcType=VARCHAR},
      </if>
      <if test="record.deliverTime != null">
        deliver_time = #{record.deliverTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update reserve_refuse_order
    set id = #{record.id,jdbcType=INTEGER},
      refuse_order_no = #{record.refuseOrderNo,jdbcType=VARCHAR},
      deliver_status = #{record.deliverStatus,jdbcType=VARCHAR},
      delivery_no = #{record.deliveryNo,jdbcType=VARCHAR},
      purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      purchase_order_id = #{record.purchaseOrderId,jdbcType=VARCHAR},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      warehouse_no = #{record.warehouseNo,jdbcType=VARCHAR},
      warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      send_no = #{record.sendNo,jdbcType=VARCHAR},
      send_id = #{record.sendId,jdbcType=INTEGER},
      goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      expiration_date = #{record.expirationDate,jdbcType=TIMESTAMP},
      refuse_quantity = #{record.refuseQuantity,jdbcType=DECIMAL},
      refuse_reason = #{record.refuseReason,jdbcType=VARCHAR},
      refuse_time = #{record.refuseTime,jdbcType=TIMESTAMP},
      refuse_person = #{record.refusePerson,jdbcType=VARCHAR},
      deliver_person = #{record.deliverPerson,jdbcType=VARCHAR},
      deliver_time = #{record.deliverTime,jdbcType=TIMESTAMP},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.ReserveRefuseOrder">
    update reserve_refuse_order
    <set>
      <if test="refuseOrderNo != null">
        refuse_order_no = #{refuseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="deliverStatus != null">
        deliver_status = #{deliverStatus,jdbcType=VARCHAR},
      </if>
      <if test="deliveryNo != null">
        delivery_no = #{deliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderNo != null">
        purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderId != null">
        purchase_order_id = #{purchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseNo != null">
        warehouse_no = #{warehouseNo,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null">
        supplier_no = #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="sendNo != null">
        send_no = #{sendNo,jdbcType=VARCHAR},
      </if>
      <if test="sendId != null">
        send_id = #{sendId,jdbcType=INTEGER},
      </if>
      <if test="goodsCode != null">
        goods_code = #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="expirationDate != null">
        expiration_date = #{expirationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="refuseQuantity != null">
        refuse_quantity = #{refuseQuantity,jdbcType=DECIMAL},
      </if>
      <if test="refuseReason != null">
        refuse_reason = #{refuseReason,jdbcType=VARCHAR},
      </if>
      <if test="refuseTime != null">
        refuse_time = #{refuseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refusePerson != null">
        refuse_person = #{refusePerson,jdbcType=VARCHAR},
      </if>
      <if test="deliverPerson != null">
        deliver_person = #{deliverPerson,jdbcType=VARCHAR},
      </if>
      <if test="deliverTime != null">
        deliver_time = #{deliverTime,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.ReserveRefuseOrder">
    update reserve_refuse_order
    set refuse_order_no = #{refuseOrderNo,jdbcType=VARCHAR},
      deliver_status = #{deliverStatus,jdbcType=VARCHAR},
      delivery_no = #{deliveryNo,jdbcType=VARCHAR},
      purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      purchase_order_id = #{purchaseOrderId,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      warehouse_no = #{warehouseNo,jdbcType=VARCHAR},
      warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      supplier_no = #{supplierNo,jdbcType=VARCHAR},
      send_no = #{sendNo,jdbcType=VARCHAR},
      send_id = #{sendId,jdbcType=INTEGER},
      goods_code = #{goodsCode,jdbcType=VARCHAR},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      expiration_date = #{expirationDate,jdbcType=TIMESTAMP},
      refuse_quantity = #{refuseQuantity,jdbcType=DECIMAL},
      refuse_reason = #{refuseReason,jdbcType=VARCHAR},
      refuse_time = #{refuseTime,jdbcType=TIMESTAMP},
      refuse_person = #{refusePerson,jdbcType=VARCHAR},
      deliver_person = #{deliverPerson,jdbcType=VARCHAR},
      deliver_time = #{deliverTime,jdbcType=TIMESTAMP},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>