<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmInnerPurchaseOrderSendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmInnerPurchaseOrderSend">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="send_no" jdbcType="VARCHAR" property="sendNo" />
    <result column="purchase_order_no" jdbcType="VARCHAR" property="purchaseOrderNo" />
    <result column="send_status" jdbcType="INTEGER" property="sendStatus" />
    <result column="expect_arrival_time" jdbcType="TIMESTAMP" property="expectArrivalTime" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="purchase_org" jdbcType="VARCHAR" property="purchaseOrg" />
    <result column="purchase_org_name" jdbcType="VARCHAR" property="purchaseOrgName" />
    <result column="supplier_no" jdbcType="VARCHAR" property="supplierNo" />
    <result column="supplier_company_code" jdbcType="VARCHAR" property="supplierCompanyCode" />
    <result column="supplier_company_name" jdbcType="VARCHAR" property="supplierCompanyName" />
    <result column="supplier_warehouse_code" jdbcType="VARCHAR" property="supplierWarehouseCode" />
    <result column="supplier_warehouse_name" jdbcType="VARCHAR" property="supplierWarehouseName" />
    <result column="no_tax_send_amount" jdbcType="DECIMAL" property="noTaxSendAmount" />
    <result column="tax_amount" jdbcType="DECIMAL" property="taxAmount" />
    <result column="have_tax_send_amount" jdbcType="DECIMAL" property="haveTaxSendAmount" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, send_no, purchase_order_no, send_status, expect_arrival_time, create_date, company_code, 
    company_name, warehouse_code, warehouse_name, purchase_org, purchase_org_name, supplier_no, 
    supplier_company_code, supplier_company_name, supplier_warehouse_code, supplier_warehouse_name, 
    no_tax_send_amount, tax_amount, have_tax_send_amount, `status`, extend, version, 
    created_by, created_name, updated_by, updated_name, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrderSendExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_inner_purchase_order_send
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_inner_purchase_order_send
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from srm_inner_purchase_order_send
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrderSendExample">
    delete from srm_inner_purchase_order_send
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrderSend">
    insert into srm_inner_purchase_order_send (id, send_no, purchase_order_no, 
      send_status, expect_arrival_time, create_date, 
      company_code, company_name, warehouse_code, 
      warehouse_name, purchase_org, purchase_org_name, 
      supplier_no, supplier_company_code, supplier_company_name, 
      supplier_warehouse_code, supplier_warehouse_name, 
      no_tax_send_amount, tax_amount, have_tax_send_amount, 
      `status`, extend, version, 
      created_by, created_name, updated_by, 
      updated_name, gmt_create, gmt_update
      )
    values (#{id,jdbcType=INTEGER}, #{sendNo,jdbcType=VARCHAR}, #{purchaseOrderNo,jdbcType=VARCHAR}, 
      #{sendStatus,jdbcType=INTEGER}, #{expectArrivalTime,jdbcType=TIMESTAMP}, #{createDate,jdbcType=TIMESTAMP}, 
      #{companyCode,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, #{warehouseCode,jdbcType=VARCHAR}, 
      #{warehouseName,jdbcType=VARCHAR}, #{purchaseOrg,jdbcType=VARCHAR}, #{purchaseOrgName,jdbcType=VARCHAR}, 
      #{supplierNo,jdbcType=VARCHAR}, #{supplierCompanyCode,jdbcType=VARCHAR}, #{supplierCompanyName,jdbcType=VARCHAR}, 
      #{supplierWarehouseCode,jdbcType=VARCHAR}, #{supplierWarehouseName,jdbcType=VARCHAR}, 
      #{noTaxSendAmount,jdbcType=DECIMAL}, #{taxAmount,jdbcType=DECIMAL}, #{haveTaxSendAmount,jdbcType=DECIMAL}, 
      #{status,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, 
      #{updatedName,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrderSend">
    insert into srm_inner_purchase_order_send
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sendNo != null">
        send_no,
      </if>
      <if test="purchaseOrderNo != null">
        purchase_order_no,
      </if>
      <if test="sendStatus != null">
        send_status,
      </if>
      <if test="expectArrivalTime != null">
        expect_arrival_time,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="warehouseCode != null">
        warehouse_code,
      </if>
      <if test="warehouseName != null">
        warehouse_name,
      </if>
      <if test="purchaseOrg != null">
        purchase_org,
      </if>
      <if test="purchaseOrgName != null">
        purchase_org_name,
      </if>
      <if test="supplierNo != null">
        supplier_no,
      </if>
      <if test="supplierCompanyCode != null">
        supplier_company_code,
      </if>
      <if test="supplierCompanyName != null">
        supplier_company_name,
      </if>
      <if test="supplierWarehouseCode != null">
        supplier_warehouse_code,
      </if>
      <if test="supplierWarehouseName != null">
        supplier_warehouse_name,
      </if>
      <if test="noTaxSendAmount != null">
        no_tax_send_amount,
      </if>
      <if test="taxAmount != null">
        tax_amount,
      </if>
      <if test="haveTaxSendAmount != null">
        have_tax_send_amount,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="sendNo != null">
        #{sendNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderNo != null">
        #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="sendStatus != null">
        #{sendStatus,jdbcType=INTEGER},
      </if>
      <if test="expectArrivalTime != null">
        #{expectArrivalTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrg != null">
        #{purchaseOrg,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrgName != null">
        #{purchaseOrgName,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null">
        #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierCompanyCode != null">
        #{supplierCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierCompanyName != null">
        #{supplierCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="supplierWarehouseCode != null">
        #{supplierWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierWarehouseName != null">
        #{supplierWarehouseName,jdbcType=VARCHAR},
      </if>
      <if test="noTaxSendAmount != null">
        #{noTaxSendAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="haveTaxSendAmount != null">
        #{haveTaxSendAmount,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrderSendExample" resultType="java.lang.Long">
    select count(*) from srm_inner_purchase_order_send
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_inner_purchase_order_send
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.sendNo != null">
        send_no = #{record.sendNo,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrderNo != null">
        purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.sendStatus != null">
        send_status = #{record.sendStatus,jdbcType=INTEGER},
      </if>
      <if test="record.expectArrivalTime != null">
        expect_arrival_time = #{record.expectArrivalTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createDate != null">
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseCode != null">
        warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseName != null">
        warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrg != null">
        purchase_org = #{record.purchaseOrg,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrgName != null">
        purchase_org_name = #{record.purchaseOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierNo != null">
        supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierCompanyCode != null">
        supplier_company_code = #{record.supplierCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierCompanyName != null">
        supplier_company_name = #{record.supplierCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierWarehouseCode != null">
        supplier_warehouse_code = #{record.supplierWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierWarehouseName != null">
        supplier_warehouse_name = #{record.supplierWarehouseName,jdbcType=VARCHAR},
      </if>
      <if test="record.noTaxSendAmount != null">
        no_tax_send_amount = #{record.noTaxSendAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.taxAmount != null">
        tax_amount = #{record.taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.haveTaxSendAmount != null">
        have_tax_send_amount = #{record.haveTaxSendAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_inner_purchase_order_send
    set id = #{record.id,jdbcType=INTEGER},
      send_no = #{record.sendNo,jdbcType=VARCHAR},
      purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      send_status = #{record.sendStatus,jdbcType=INTEGER},
      expect_arrival_time = #{record.expectArrivalTime,jdbcType=TIMESTAMP},
      create_date = #{record.createDate,jdbcType=TIMESTAMP},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      purchase_org = #{record.purchaseOrg,jdbcType=VARCHAR},
      purchase_org_name = #{record.purchaseOrgName,jdbcType=VARCHAR},
      supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      supplier_company_code = #{record.supplierCompanyCode,jdbcType=VARCHAR},
      supplier_company_name = #{record.supplierCompanyName,jdbcType=VARCHAR},
      supplier_warehouse_code = #{record.supplierWarehouseCode,jdbcType=VARCHAR},
      supplier_warehouse_name = #{record.supplierWarehouseName,jdbcType=VARCHAR},
      no_tax_send_amount = #{record.noTaxSendAmount,jdbcType=DECIMAL},
      tax_amount = #{record.taxAmount,jdbcType=DECIMAL},
      have_tax_send_amount = #{record.haveTaxSendAmount,jdbcType=DECIMAL},
      `status` = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrderSend">
    update srm_inner_purchase_order_send
    <set>
      <if test="sendNo != null">
        send_no = #{sendNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderNo != null">
        purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="sendStatus != null">
        send_status = #{sendStatus,jdbcType=INTEGER},
      </if>
      <if test="expectArrivalTime != null">
        expect_arrival_time = #{expectArrivalTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrg != null">
        purchase_org = #{purchaseOrg,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrgName != null">
        purchase_org_name = #{purchaseOrgName,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null">
        supplier_no = #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierCompanyCode != null">
        supplier_company_code = #{supplierCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierCompanyName != null">
        supplier_company_name = #{supplierCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="supplierWarehouseCode != null">
        supplier_warehouse_code = #{supplierWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierWarehouseName != null">
        supplier_warehouse_name = #{supplierWarehouseName,jdbcType=VARCHAR},
      </if>
      <if test="noTaxSendAmount != null">
        no_tax_send_amount = #{noTaxSendAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        tax_amount = #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="haveTaxSendAmount != null">
        have_tax_send_amount = #{haveTaxSendAmount,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrderSend">
    update srm_inner_purchase_order_send
    set send_no = #{sendNo,jdbcType=VARCHAR},
      purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      send_status = #{sendStatus,jdbcType=INTEGER},
      expect_arrival_time = #{expectArrivalTime,jdbcType=TIMESTAMP},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      purchase_org = #{purchaseOrg,jdbcType=VARCHAR},
      purchase_org_name = #{purchaseOrgName,jdbcType=VARCHAR},
      supplier_no = #{supplierNo,jdbcType=VARCHAR},
      supplier_company_code = #{supplierCompanyCode,jdbcType=VARCHAR},
      supplier_company_name = #{supplierCompanyName,jdbcType=VARCHAR},
      supplier_warehouse_code = #{supplierWarehouseCode,jdbcType=VARCHAR},
      supplier_warehouse_name = #{supplierWarehouseName,jdbcType=VARCHAR},
      no_tax_send_amount = #{noTaxSendAmount,jdbcType=DECIMAL},
      tax_amount = #{taxAmount,jdbcType=DECIMAL},
      have_tax_send_amount = #{haveTaxSendAmount,jdbcType=DECIMAL},
      `status` = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>