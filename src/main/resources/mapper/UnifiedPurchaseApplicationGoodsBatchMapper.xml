<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.UnifiedPurchaseApplicationGoodsBatchMapper">

    <update id="batchUpdateCountByGoodsCodeAndApplicationNo" parameterType="java.util.List">

        <foreach collection="list" item="item" index="index" separator=";" open="" close="">
            update unified_purchase_application_goods
            <set>
                receiving_count = #{item.receivingCount}
            </set>
            where goods_code = #{item.goodsCode} AND purchase_application_no = #{item.purchaseApplicationNo}
        </foreach>
    </update>
    <update id="batchUpdateApprovedCount" parameterType="java.util.List">

        <foreach collection="list" item="item" index="index" separator=";" open="" close="">
            update unified_purchase_application_goods
            <set>
                approved_count = #{item.approvedCount}
            </set>
            where goods_code = #{item.goodsCode} AND purchase_application_no = #{item.purchaseApplicationNo}
        </foreach>
    </update>
    <insert id="batchInsertSelective" parameterType="java.util.List">
        insert into unified_purchase_application_goods
        (purchase_application_no, goods_code,
        bar_code, spu_id, goods_common_name,
        goods_name, description, row_no,
        specifications, dosage_form, apply_count,
        approved_count, receiving_count, cold_chain,
        receiving_temperature, habitat, manufacturer,
        extend, created_by,
        update_by, os_apply_count, box_specifications,
        move_mounth_sale_count, store_stock_count, dc_stock_count,
        way_order_count, stock_use_ratio, supplier_repertory_no,
        supplier_repertory_name, supplier_no, supplier_name,
        all_stock, buying_price, supplier_lead_time,
        dc_lead_time, now_sale_store_count, future_sale_store_count,
        reason_comments, unified_company_no, unified_company_name,
        received_time, apprdocno, replenishment_num,
        unit)
        VALUES
        <foreach collection="list" item="item" index="index" separator="," >
        (#{item.purchaseApplicationNo,jdbcType=VARCHAR}, #{item.goodsCode,jdbcType=VARCHAR},
        #{item.barCode,jdbcType=VARCHAR}, #{item.spuId,jdbcType=BIGINT}, #{item.goodsCommonName,jdbcType=VARCHAR},
        #{item.goodsName,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR}, #{item.rowNo,jdbcType=BIGINT},
        #{item.specifications,jdbcType=VARCHAR}, #{item.dosageForm,jdbcType=VARCHAR}, #{item.applyCount,jdbcType=INTEGER},
        #{item.approvedCount,jdbcType=INTEGER}, #{item.receivingCount,jdbcType=INTEGER}, #{item.coldChain,jdbcType=TINYINT},
        #{item.receivingTemperature,jdbcType=INTEGER}, #{item.habitat,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR},
        #{item.extend,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=BIGINT},
        #{item.updateBy,jdbcType=BIGINT}, #{item.osApplyCount,jdbcType=INTEGER}, #{item.boxSpecifications,jdbcType=VARCHAR},
        #{item.moveMounthSaleCount,jdbcType=DECIMAL}, #{item.storeStockCount,jdbcType=DECIMAL}, #{item.dcStockCount,jdbcType=DECIMAL},
        #{item.wayOrderCount,jdbcType=INTEGER}, #{item.stockUseRatio,jdbcType=DECIMAL}, #{item.supplierRepertoryNo,jdbcType=VARCHAR},
        #{item.supplierRepertoryName,jdbcType=VARCHAR}, #{item.supplierNo,jdbcType=VARCHAR}, #{item.supplierName,jdbcType=VARCHAR},
        #{item.allStock,jdbcType=DECIMAL}, #{item.buyingPrice,jdbcType=DECIMAL}, #{item.supplierLeadTime,jdbcType=DECIMAL},
        #{item.dcLeadTime,jdbcType=DECIMAL}, #{item.nowSaleStoreCount,jdbcType=INTEGER}, #{item.futureSaleStoreCount,jdbcType=INTEGER},
        #{item.reasonComments,jdbcType=VARCHAR}, #{item.unifiedCompanyNo,jdbcType=VARCHAR}, #{item.unifiedCompanyName,jdbcType=VARCHAR},
        #{item.receivedTime,jdbcType=TIMESTAMP}, #{item.apprdocno,jdbcType=VARCHAR}, #{item.replenishmentNum,jdbcType=DECIMAL},
        #{item.unit,jdbcType=VARCHAR})
        </foreach>
    </insert>


    <select id="getGoodsListPage" parameterType="com.cowell.purchase.rest.vo.PurchaseApplicationRequestVO" resultType="com.cowell.purchase.service.dto.PurchaseCollectDto">
        SELECT
        DISTINCT upag.goods_code goodsCode
        FROM unified_purchase_application upa LEFT JOIN
        unified_purchase_application_goods upag ON
        upa.purchase_application_no = upag.purchase_application_no
        WHERE upa.status = 0 AND upag.status = 0
        <if test="request.purchaseApplicationNo != null">
            and upa.purchase_application_no = #{request.purchaseApplicationNo}
        </if>
        <if test="request.businessIdList != null and request.businessIdList.size >0">
            and upa.business_id IN
            <foreach collection="request.businessIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="request.goodsNoList != null and request.goodsNoList.size >0">
            and upag.goods_code IN
            <foreach collection="request.goodsNoList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.startDate != null">
            and upa.gmt_create &gt;= #{request.startDate}
        </if>
        <if test="request.endDate != null">
            and upa.gmt_create &lt;= #{request.endDate}
        </if>
        <if test="request.auditStatus != null">
            and upa.audit_status = #{request.auditStatus}
        </if>
        <if test="request.limit != null">
            <if test="request.offset != null">
                limit ${request.offset}, ${request.limit}
            </if>
            <if test="request.offset == null">
                limit ${request.limit}
            </if>
        </if>
    </select>

    <select id="statGoodsForNum" parameterType="com.cowell.purchase.rest.vo.PurchaseApplicationRequestVO" resultType="com.cowell.purchase.service.dto.PurchaseCollectDto">
        SELECT
        upag.id id,
        upag.unified_company_no unifiedCompanyNo,
        upag.unified_company_name unifiedCompanyName,
        upag.supplier_repertory_no receiptRepertoryNo,
        upag.supplier_repertory_name receiptRepertoryName,
        upag.goods_code goodsCode,
        upag.goods_common_name goodsCommonName,
        upag.specifications specifications,
        upag.description description,
        upag.bar_code barCode,
        SUM(upag.apply_count) applyAllCounts,
        SUM(upag.receiving_count) receivingAllCounts
        FROM cowell_purchase.unified_purchase_application upa LEFT JOIN
        cowell_purchase.unified_purchase_application_goods upag ON
        upa.purchase_application_no = upag.purchase_application_no
        WHERE upa.status = 0 AND upag.status = 0
        <if test="request.purchaseApplicationNo != null">
            and upa.purchase_application_no = #{request.purchaseApplicationNo}
        </if>
        <if test="request.businessIdList != null and request.businessIdList.size >0">
            and upa.business_id IN
            <foreach collection="request.businessIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.goodsNoList != null and request.goodsNoList.size > 0">
            and upag.goods_code IN
            <foreach collection="request.goodsNoList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.startDate != null">
            and upa.gmt_create &gt;= #{request.startDate}
        </if>
        <if test="request.endDate != null">
            and upa.gmt_create &lt;= #{request.endDate}
        </if>
        <if test="request.auditStatus != null">
            and upa.audit_status = #{request.auditStatus}
        </if>
        GROUP BY upag.goods_code, upag.unified_company_no

    </select>

    <select id="count" parameterType="com.cowell.purchase.rest.vo.PurchaseApplicationRequestVO" resultType="java.lang.Integer">
        SELECT count(DISTINCT upag.goods_code, upag.unified_company_no)
        FROM cowell_purchase.unified_purchase_application upa LEFT JOIN
        cowell_purchase.unified_purchase_application_goods upag ON
        upa.purchase_application_no = upag.purchase_application_no
        WHERE upa.status = 0 AND upag.status = 0
        <if test="request.purchaseApplicationNo != null">
            and upa.purchase_application_no = #{request.purchaseApplicationNo}
        </if>
        <if test="request.businessIdList != null and request.businessIdList.size >0">
            and upa.business_id IN
            <foreach collection="request.businessIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.goodsNoList != null and request.goodsNoList.size > 0">
            and upag.goods_code IN
            <foreach collection="request.goodsNoList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.startDate != null">
            and upa.gmt_create &gt;= #{request.startDate}
        </if>
        <if test="request.endDate != null">
            and upa.gmt_create &lt;= #{request.endDate}
        </if>
        <if test="request.auditStatus != null">
            and upa.audit_status = #{request.auditStatus}
        </if>
    </select>

    <select id="selectDetail" parameterType="com.cowell.purchase.rest.vo.PurchaseApplicationRequestVO" resultType="com.cowell.purchase.service.dto.PurchaseApplicationDto">
        SELECT
        upa.purchase_application_no purchaseApplicationNo,
        upag.unified_company_name unifiedCompanyName,
        upag.supplier_repertory_name supplierRepertoryName,
        upag.goods_code goodsCode,
        upag.goods_common_name goodsCommonName,
        upag.specifications specifications,
        upag.description description,
        upag.bar_code barCode,
        upa.business_id businessId,
        upa.business_name businessName,
        upa.express_model expressModel,
        upa.want_receipt_date wantReceiptDate,
        upa.receipt_repertory_no receiptRepertoryNo,
        upa.receipt_repertory_name receiptRepertoryName,
        upag.apply_count applyCount,
        upa.gmt_create gmtCreate,
        upa.audit_status auditStatus,
        upa.approved_time auditTime,
        upa.submit_time submitTime,
        upag.os_apply_count osApplyCount,
        upag.box_specifications boxSpecifications,
        upag.move_mounth_sale_count moveMounthSaleCount,
        upag.store_stock_count storeStockCount,
        upag.dc_stock_count dcStockCount,
        upag.way_order_count wayOrderCount,
        upag.stock_use_ratio stockUseRatio,
        upag.supplier_no supplierNo,
        upag.supplier_name supplierName,
        upag.buying_price buyingPrice,
        upag.supplier_lead_time supplierLeadTime,
        upag.dc_lead_time dcLeadTime,
        upag.now_sale_store_count nowSaleStoreCount,
        upag.future_sale_store_count futureSaleStoreCount,
        upag.reason_comments reasonComments,
        upag.receiving_count receivingCount
        FROM cowell_purchase.unified_purchase_application upa LEFT JOIN
        cowell_purchase.unified_purchase_application_goods upag ON
        upa.purchase_application_no = upag.purchase_application_no
        WHERE upa.status = 0 AND upag.status = 0
        <if test="request.purchaseApplicationNo != null">
            and upa.purchase_application_no = #{request.purchaseApplicationNo}
        </if>
        <if test="request.unifiedCompany != null">
            and upag.unified_company_no = #{request.unifiedCompany}
        </if>
        <if test="request.businessIdList != null and request.businessIdList.size >0">
            and upa.business_id IN
            <foreach collection="request.businessIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.goodsCodes != null">
            and upag.goods_code = #{request.goodsCodes}
        </if>
        <if test="request.startDate != null">
            and upa.gmt_create &gt;= #{request.startDate}
        </if>
        <if test="request.endDate != null">
            and upa.gmt_create &lt;= #{request.endDate}
        </if>
        <if test="request.auditStatus != null">
            and upa.audit_status = #{request.auditStatus}
        </if>
    </select>
</mapper>
