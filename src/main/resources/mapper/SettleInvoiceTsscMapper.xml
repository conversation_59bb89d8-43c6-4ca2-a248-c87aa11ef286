<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SettleInvoiceTsscMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SettleInvoiceTssc">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="invoice_code" jdbcType="VARCHAR" property="invoiceCode" />
    <result column="invoice_no" jdbcType="VARCHAR" property="invoiceNo" />
    <result column="invoice_key" jdbcType="VARCHAR" property="invoiceKey" />
    <result column="invoice_class" jdbcType="TINYINT" property="invoiceClass" />
    <result column="invoice_type" jdbcType="TINYINT" property="invoiceType" />
    <result column="invoice_date" jdbcType="DATE" property="invoiceDate" />
    <result column="invoice_status" jdbcType="TINYINT" property="invoiceStatus" />
    <result column="deal_status" jdbcType="TINYINT" property="dealStatus" />
    <result column="deal_reason" jdbcType="VARCHAR" property="dealReason" />
    <result column="sales_taxpayer_no" jdbcType="VARCHAR" property="salesTaxpayerNo" />
    <result column="sales_taxpayer" jdbcType="VARCHAR" property="salesTaxpayer" />
    <result column="sales_addr_tel" jdbcType="VARCHAR" property="salesAddrTel" />
    <result column="sales_bank_account" jdbcType="VARCHAR" property="salesBankAccount" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="purchase_taxpayer_no" jdbcType="VARCHAR" property="purchaseTaxpayerNo" />
    <result column="purchase_taxpayer" jdbcType="VARCHAR" property="purchaseTaxpayer" />
    <result column="purchase_addr_tel" jdbcType="VARCHAR" property="purchaseAddrTel" />
    <result column="purchase_bank_account" jdbcType="VARCHAR" property="purchaseBankAccount" />
    <result column="no_tax_amount" jdbcType="DECIMAL" property="noTaxAmount" />
    <result column="tax_amount" jdbcType="DECIMAL" property="taxAmount" />
    <result column="price_tax_amount" jdbcType="DECIMAL" property="priceTaxAmount" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="reserved1" jdbcType="VARCHAR" property="reserved1" />
    <result column="reserved2" jdbcType="VARCHAR" property="reserved2" />
    <result column="reserved3" jdbcType="VARCHAR" property="reserved3" />
    <result column="reserved4" jdbcType="VARCHAR" property="reserved4" />
    <result column="reserved5" jdbcType="VARCHAR" property="reserved5" />
    <result column="reserved6" jdbcType="VARCHAR" property="reserved6" />
    <result column="reserved7" jdbcType="VARCHAR" property="reserved7" />
    <result column="reserved8" jdbcType="VARCHAR" property="reserved8" />
    <result column="reserved9" jdbcType="VARCHAR" property="reserved9" />
    <result column="reserved10" jdbcType="VARCHAR" property="reserved10" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, invoice_code, invoice_no, invoice_key, invoice_class, invoice_type, invoice_date, 
    invoice_status, deal_status, deal_reason, sales_taxpayer_no, sales_taxpayer, sales_addr_tel, 
    sales_bank_account, company_code, purchase_taxpayer_no, purchase_taxpayer, purchase_addr_tel, 
    purchase_bank_account, no_tax_amount, tax_amount, price_tax_amount, memo, reserved1, 
    reserved2, reserved3, reserved4, reserved5, reserved6, reserved7, reserved8, reserved9, 
    reserved10, `status`, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SettleInvoiceTsscExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from settle_invoice_tssc
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from settle_invoice_tssc
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from settle_invoice_tssc
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SettleInvoiceTsscExample">
    delete from settle_invoice_tssc
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SettleInvoiceTssc">
    insert into settle_invoice_tssc (id, invoice_code, invoice_no, 
      invoice_key, invoice_class, invoice_type, 
      invoice_date, invoice_status, deal_status, 
      deal_reason, sales_taxpayer_no, sales_taxpayer, 
      sales_addr_tel, sales_bank_account, company_code, 
      purchase_taxpayer_no, purchase_taxpayer, purchase_addr_tel, 
      purchase_bank_account, no_tax_amount, tax_amount, 
      price_tax_amount, memo, reserved1, 
      reserved2, reserved3, reserved4, 
      reserved5, reserved6, reserved7, 
      reserved8, reserved9, reserved10, 
      `status`, gmt_create, gmt_update
      )
    values (#{id,jdbcType=INTEGER}, #{invoiceCode,jdbcType=VARCHAR}, #{invoiceNo,jdbcType=VARCHAR}, 
      #{invoiceKey,jdbcType=VARCHAR}, #{invoiceClass,jdbcType=TINYINT}, #{invoiceType,jdbcType=TINYINT}, 
      #{invoiceDate,jdbcType=DATE}, #{invoiceStatus,jdbcType=TINYINT}, #{dealStatus,jdbcType=TINYINT}, 
      #{dealReason,jdbcType=VARCHAR}, #{salesTaxpayerNo,jdbcType=VARCHAR}, #{salesTaxpayer,jdbcType=VARCHAR}, 
      #{salesAddrTel,jdbcType=VARCHAR}, #{salesBankAccount,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR}, 
      #{purchaseTaxpayerNo,jdbcType=VARCHAR}, #{purchaseTaxpayer,jdbcType=VARCHAR}, #{purchaseAddrTel,jdbcType=VARCHAR}, 
      #{purchaseBankAccount,jdbcType=VARCHAR}, #{noTaxAmount,jdbcType=DECIMAL}, #{taxAmount,jdbcType=DECIMAL}, 
      #{priceTaxAmount,jdbcType=DECIMAL}, #{memo,jdbcType=VARCHAR}, #{reserved1,jdbcType=VARCHAR}, 
      #{reserved2,jdbcType=VARCHAR}, #{reserved3,jdbcType=VARCHAR}, #{reserved4,jdbcType=VARCHAR}, 
      #{reserved5,jdbcType=VARCHAR}, #{reserved6,jdbcType=VARCHAR}, #{reserved7,jdbcType=VARCHAR}, 
      #{reserved8,jdbcType=VARCHAR}, #{reserved9,jdbcType=VARCHAR}, #{reserved10,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SettleInvoiceTssc">
    insert into settle_invoice_tssc
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="invoiceCode != null">
        invoice_code,
      </if>
      <if test="invoiceNo != null">
        invoice_no,
      </if>
      <if test="invoiceKey != null">
        invoice_key,
      </if>
      <if test="invoiceClass != null">
        invoice_class,
      </if>
      <if test="invoiceType != null">
        invoice_type,
      </if>
      <if test="invoiceDate != null">
        invoice_date,
      </if>
      <if test="invoiceStatus != null">
        invoice_status,
      </if>
      <if test="dealStatus != null">
        deal_status,
      </if>
      <if test="dealReason != null">
        deal_reason,
      </if>
      <if test="salesTaxpayerNo != null">
        sales_taxpayer_no,
      </if>
      <if test="salesTaxpayer != null">
        sales_taxpayer,
      </if>
      <if test="salesAddrTel != null">
        sales_addr_tel,
      </if>
      <if test="salesBankAccount != null">
        sales_bank_account,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="purchaseTaxpayerNo != null">
        purchase_taxpayer_no,
      </if>
      <if test="purchaseTaxpayer != null">
        purchase_taxpayer,
      </if>
      <if test="purchaseAddrTel != null">
        purchase_addr_tel,
      </if>
      <if test="purchaseBankAccount != null">
        purchase_bank_account,
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount,
      </if>
      <if test="taxAmount != null">
        tax_amount,
      </if>
      <if test="priceTaxAmount != null">
        price_tax_amount,
      </if>
      <if test="memo != null">
        memo,
      </if>
      <if test="reserved1 != null">
        reserved1,
      </if>
      <if test="reserved2 != null">
        reserved2,
      </if>
      <if test="reserved3 != null">
        reserved3,
      </if>
      <if test="reserved4 != null">
        reserved4,
      </if>
      <if test="reserved5 != null">
        reserved5,
      </if>
      <if test="reserved6 != null">
        reserved6,
      </if>
      <if test="reserved7 != null">
        reserved7,
      </if>
      <if test="reserved8 != null">
        reserved8,
      </if>
      <if test="reserved9 != null">
        reserved9,
      </if>
      <if test="reserved10 != null">
        reserved10,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="invoiceCode != null">
        #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNo != null">
        #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceKey != null">
        #{invoiceKey,jdbcType=VARCHAR},
      </if>
      <if test="invoiceClass != null">
        #{invoiceClass,jdbcType=TINYINT},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=TINYINT},
      </if>
      <if test="invoiceDate != null">
        #{invoiceDate,jdbcType=DATE},
      </if>
      <if test="invoiceStatus != null">
        #{invoiceStatus,jdbcType=TINYINT},
      </if>
      <if test="dealStatus != null">
        #{dealStatus,jdbcType=TINYINT},
      </if>
      <if test="dealReason != null">
        #{dealReason,jdbcType=VARCHAR},
      </if>
      <if test="salesTaxpayerNo != null">
        #{salesTaxpayerNo,jdbcType=VARCHAR},
      </if>
      <if test="salesTaxpayer != null">
        #{salesTaxpayer,jdbcType=VARCHAR},
      </if>
      <if test="salesAddrTel != null">
        #{salesAddrTel,jdbcType=VARCHAR},
      </if>
      <if test="salesBankAccount != null">
        #{salesBankAccount,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="purchaseTaxpayerNo != null">
        #{purchaseTaxpayerNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseTaxpayer != null">
        #{purchaseTaxpayer,jdbcType=VARCHAR},
      </if>
      <if test="purchaseAddrTel != null">
        #{purchaseAddrTel,jdbcType=VARCHAR},
      </if>
      <if test="purchaseBankAccount != null">
        #{purchaseBankAccount,jdbcType=VARCHAR},
      </if>
      <if test="noTaxAmount != null">
        #{noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="priceTaxAmount != null">
        #{priceTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="reserved1 != null">
        #{reserved1,jdbcType=VARCHAR},
      </if>
      <if test="reserved2 != null">
        #{reserved2,jdbcType=VARCHAR},
      </if>
      <if test="reserved3 != null">
        #{reserved3,jdbcType=VARCHAR},
      </if>
      <if test="reserved4 != null">
        #{reserved4,jdbcType=VARCHAR},
      </if>
      <if test="reserved5 != null">
        #{reserved5,jdbcType=VARCHAR},
      </if>
      <if test="reserved6 != null">
        #{reserved6,jdbcType=VARCHAR},
      </if>
      <if test="reserved7 != null">
        #{reserved7,jdbcType=VARCHAR},
      </if>
      <if test="reserved8 != null">
        #{reserved8,jdbcType=VARCHAR},
      </if>
      <if test="reserved9 != null">
        #{reserved9,jdbcType=VARCHAR},
      </if>
      <if test="reserved10 != null">
        #{reserved10,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SettleInvoiceTsscExample" resultType="java.lang.Long">
    select count(*) from settle_invoice_tssc
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update settle_invoice_tssc
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.invoiceCode != null">
        invoice_code = #{record.invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceNo != null">
        invoice_no = #{record.invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceKey != null">
        invoice_key = #{record.invoiceKey,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceClass != null">
        invoice_class = #{record.invoiceClass,jdbcType=TINYINT},
      </if>
      <if test="record.invoiceType != null">
        invoice_type = #{record.invoiceType,jdbcType=TINYINT},
      </if>
      <if test="record.invoiceDate != null">
        invoice_date = #{record.invoiceDate,jdbcType=DATE},
      </if>
      <if test="record.invoiceStatus != null">
        invoice_status = #{record.invoiceStatus,jdbcType=TINYINT},
      </if>
      <if test="record.dealStatus != null">
        deal_status = #{record.dealStatus,jdbcType=TINYINT},
      </if>
      <if test="record.dealReason != null">
        deal_reason = #{record.dealReason,jdbcType=VARCHAR},
      </if>
      <if test="record.salesTaxpayerNo != null">
        sales_taxpayer_no = #{record.salesTaxpayerNo,jdbcType=VARCHAR},
      </if>
      <if test="record.salesTaxpayer != null">
        sales_taxpayer = #{record.salesTaxpayer,jdbcType=VARCHAR},
      </if>
      <if test="record.salesAddrTel != null">
        sales_addr_tel = #{record.salesAddrTel,jdbcType=VARCHAR},
      </if>
      <if test="record.salesBankAccount != null">
        sales_bank_account = #{record.salesBankAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseTaxpayerNo != null">
        purchase_taxpayer_no = #{record.purchaseTaxpayerNo,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseTaxpayer != null">
        purchase_taxpayer = #{record.purchaseTaxpayer,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseAddrTel != null">
        purchase_addr_tel = #{record.purchaseAddrTel,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseBankAccount != null">
        purchase_bank_account = #{record.purchaseBankAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.noTaxAmount != null">
        no_tax_amount = #{record.noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.taxAmount != null">
        tax_amount = #{record.taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.priceTaxAmount != null">
        price_tax_amount = #{record.priceTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.memo != null">
        memo = #{record.memo,jdbcType=VARCHAR},
      </if>
      <if test="record.reserved1 != null">
        reserved1 = #{record.reserved1,jdbcType=VARCHAR},
      </if>
      <if test="record.reserved2 != null">
        reserved2 = #{record.reserved2,jdbcType=VARCHAR},
      </if>
      <if test="record.reserved3 != null">
        reserved3 = #{record.reserved3,jdbcType=VARCHAR},
      </if>
      <if test="record.reserved4 != null">
        reserved4 = #{record.reserved4,jdbcType=VARCHAR},
      </if>
      <if test="record.reserved5 != null">
        reserved5 = #{record.reserved5,jdbcType=VARCHAR},
      </if>
      <if test="record.reserved6 != null">
        reserved6 = #{record.reserved6,jdbcType=VARCHAR},
      </if>
      <if test="record.reserved7 != null">
        reserved7 = #{record.reserved7,jdbcType=VARCHAR},
      </if>
      <if test="record.reserved8 != null">
        reserved8 = #{record.reserved8,jdbcType=VARCHAR},
      </if>
      <if test="record.reserved9 != null">
        reserved9 = #{record.reserved9,jdbcType=VARCHAR},
      </if>
      <if test="record.reserved10 != null">
        reserved10 = #{record.reserved10,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update settle_invoice_tssc
    set id = #{record.id,jdbcType=INTEGER},
      invoice_code = #{record.invoiceCode,jdbcType=VARCHAR},
      invoice_no = #{record.invoiceNo,jdbcType=VARCHAR},
      invoice_key = #{record.invoiceKey,jdbcType=VARCHAR},
      invoice_class = #{record.invoiceClass,jdbcType=TINYINT},
      invoice_type = #{record.invoiceType,jdbcType=TINYINT},
      invoice_date = #{record.invoiceDate,jdbcType=DATE},
      invoice_status = #{record.invoiceStatus,jdbcType=TINYINT},
      deal_status = #{record.dealStatus,jdbcType=TINYINT},
      deal_reason = #{record.dealReason,jdbcType=VARCHAR},
      sales_taxpayer_no = #{record.salesTaxpayerNo,jdbcType=VARCHAR},
      sales_taxpayer = #{record.salesTaxpayer,jdbcType=VARCHAR},
      sales_addr_tel = #{record.salesAddrTel,jdbcType=VARCHAR},
      sales_bank_account = #{record.salesBankAccount,jdbcType=VARCHAR},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      purchase_taxpayer_no = #{record.purchaseTaxpayerNo,jdbcType=VARCHAR},
      purchase_taxpayer = #{record.purchaseTaxpayer,jdbcType=VARCHAR},
      purchase_addr_tel = #{record.purchaseAddrTel,jdbcType=VARCHAR},
      purchase_bank_account = #{record.purchaseBankAccount,jdbcType=VARCHAR},
      no_tax_amount = #{record.noTaxAmount,jdbcType=DECIMAL},
      tax_amount = #{record.taxAmount,jdbcType=DECIMAL},
      price_tax_amount = #{record.priceTaxAmount,jdbcType=DECIMAL},
      memo = #{record.memo,jdbcType=VARCHAR},
      reserved1 = #{record.reserved1,jdbcType=VARCHAR},
      reserved2 = #{record.reserved2,jdbcType=VARCHAR},
      reserved3 = #{record.reserved3,jdbcType=VARCHAR},
      reserved4 = #{record.reserved4,jdbcType=VARCHAR},
      reserved5 = #{record.reserved5,jdbcType=VARCHAR},
      reserved6 = #{record.reserved6,jdbcType=VARCHAR},
      reserved7 = #{record.reserved7,jdbcType=VARCHAR},
      reserved8 = #{record.reserved8,jdbcType=VARCHAR},
      reserved9 = #{record.reserved9,jdbcType=VARCHAR},
      reserved10 = #{record.reserved10,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SettleInvoiceTssc">
    update settle_invoice_tssc
    <set>
      <if test="invoiceCode != null">
        invoice_code = #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNo != null">
        invoice_no = #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceKey != null">
        invoice_key = #{invoiceKey,jdbcType=VARCHAR},
      </if>
      <if test="invoiceClass != null">
        invoice_class = #{invoiceClass,jdbcType=TINYINT},
      </if>
      <if test="invoiceType != null">
        invoice_type = #{invoiceType,jdbcType=TINYINT},
      </if>
      <if test="invoiceDate != null">
        invoice_date = #{invoiceDate,jdbcType=DATE},
      </if>
      <if test="invoiceStatus != null">
        invoice_status = #{invoiceStatus,jdbcType=TINYINT},
      </if>
      <if test="dealStatus != null">
        deal_status = #{dealStatus,jdbcType=TINYINT},
      </if>
      <if test="dealReason != null">
        deal_reason = #{dealReason,jdbcType=VARCHAR},
      </if>
      <if test="salesTaxpayerNo != null">
        sales_taxpayer_no = #{salesTaxpayerNo,jdbcType=VARCHAR},
      </if>
      <if test="salesTaxpayer != null">
        sales_taxpayer = #{salesTaxpayer,jdbcType=VARCHAR},
      </if>
      <if test="salesAddrTel != null">
        sales_addr_tel = #{salesAddrTel,jdbcType=VARCHAR},
      </if>
      <if test="salesBankAccount != null">
        sales_bank_account = #{salesBankAccount,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="purchaseTaxpayerNo != null">
        purchase_taxpayer_no = #{purchaseTaxpayerNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseTaxpayer != null">
        purchase_taxpayer = #{purchaseTaxpayer,jdbcType=VARCHAR},
      </if>
      <if test="purchaseAddrTel != null">
        purchase_addr_tel = #{purchaseAddrTel,jdbcType=VARCHAR},
      </if>
      <if test="purchaseBankAccount != null">
        purchase_bank_account = #{purchaseBankAccount,jdbcType=VARCHAR},
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount = #{noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        tax_amount = #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="priceTaxAmount != null">
        price_tax_amount = #{priceTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="memo != null">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="reserved1 != null">
        reserved1 = #{reserved1,jdbcType=VARCHAR},
      </if>
      <if test="reserved2 != null">
        reserved2 = #{reserved2,jdbcType=VARCHAR},
      </if>
      <if test="reserved3 != null">
        reserved3 = #{reserved3,jdbcType=VARCHAR},
      </if>
      <if test="reserved4 != null">
        reserved4 = #{reserved4,jdbcType=VARCHAR},
      </if>
      <if test="reserved5 != null">
        reserved5 = #{reserved5,jdbcType=VARCHAR},
      </if>
      <if test="reserved6 != null">
        reserved6 = #{reserved6,jdbcType=VARCHAR},
      </if>
      <if test="reserved7 != null">
        reserved7 = #{reserved7,jdbcType=VARCHAR},
      </if>
      <if test="reserved8 != null">
        reserved8 = #{reserved8,jdbcType=VARCHAR},
      </if>
      <if test="reserved9 != null">
        reserved9 = #{reserved9,jdbcType=VARCHAR},
      </if>
      <if test="reserved10 != null">
        reserved10 = #{reserved10,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SettleInvoiceTssc">
    update settle_invoice_tssc
    set invoice_code = #{invoiceCode,jdbcType=VARCHAR},
      invoice_no = #{invoiceNo,jdbcType=VARCHAR},
      invoice_key = #{invoiceKey,jdbcType=VARCHAR},
      invoice_class = #{invoiceClass,jdbcType=TINYINT},
      invoice_type = #{invoiceType,jdbcType=TINYINT},
      invoice_date = #{invoiceDate,jdbcType=DATE},
      invoice_status = #{invoiceStatus,jdbcType=TINYINT},
      deal_status = #{dealStatus,jdbcType=TINYINT},
      deal_reason = #{dealReason,jdbcType=VARCHAR},
      sales_taxpayer_no = #{salesTaxpayerNo,jdbcType=VARCHAR},
      sales_taxpayer = #{salesTaxpayer,jdbcType=VARCHAR},
      sales_addr_tel = #{salesAddrTel,jdbcType=VARCHAR},
      sales_bank_account = #{salesBankAccount,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      purchase_taxpayer_no = #{purchaseTaxpayerNo,jdbcType=VARCHAR},
      purchase_taxpayer = #{purchaseTaxpayer,jdbcType=VARCHAR},
      purchase_addr_tel = #{purchaseAddrTel,jdbcType=VARCHAR},
      purchase_bank_account = #{purchaseBankAccount,jdbcType=VARCHAR},
      no_tax_amount = #{noTaxAmount,jdbcType=DECIMAL},
      tax_amount = #{taxAmount,jdbcType=DECIMAL},
      price_tax_amount = #{priceTaxAmount,jdbcType=DECIMAL},
      memo = #{memo,jdbcType=VARCHAR},
      reserved1 = #{reserved1,jdbcType=VARCHAR},
      reserved2 = #{reserved2,jdbcType=VARCHAR},
      reserved3 = #{reserved3,jdbcType=VARCHAR},
      reserved4 = #{reserved4,jdbcType=VARCHAR},
      reserved5 = #{reserved5,jdbcType=VARCHAR},
      reserved6 = #{reserved6,jdbcType=VARCHAR},
      reserved7 = #{reserved7,jdbcType=VARCHAR},
      reserved8 = #{reserved8,jdbcType=VARCHAR},
      reserved9 = #{reserved9,jdbcType=VARCHAR},
      reserved10 = #{reserved10,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>