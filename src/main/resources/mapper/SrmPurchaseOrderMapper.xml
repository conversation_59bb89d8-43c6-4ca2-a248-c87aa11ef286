<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmPurchaseOrderMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmPurchaseOrder">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="purchase_order_no" jdbcType="VARCHAR" property="purchaseOrderNo" />
    <result column="purchase_plan_no" jdbcType="VARCHAR" property="purchasePlanNo" />
    <result column="bguid" jdbcType="VARCHAR" property="bguid" />
    <result column="purchaser_code" jdbcType="VARCHAR" property="purchaserCode" />
    <result column="purchase_delete_id" jdbcType="VARCHAR" property="purchaseDeleteId" />
    <result column="confirm_status" jdbcType="INTEGER" property="confirmStatus" />
    <result column="deliver_status" jdbcType="INTEGER" property="deliverStatus" />
    <result column="confirm_time" jdbcType="TIMESTAMP" property="confirmTime" />
    <result column="close_time_limit" jdbcType="INTEGER" property="closeTimeLimit" />
    <result column="close_time" jdbcType="TIMESTAMP" property="closeTime" />
    <result column="order_source" jdbcType="TINYINT" property="orderSource" />
    <result column="plan_create_time" jdbcType="TIMESTAMP" property="planCreateTime" />
    <result column="plan_recept_time" jdbcType="TIMESTAMP" property="planReceptTime" />
    <result column="plan_confirm_time" jdbcType="TIMESTAMP" property="planConfirmTime" />
    <result column="plan_trans_time" jdbcType="TIMESTAMP" property="planTransTime" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="purchase_org" jdbcType="VARCHAR" property="purchaseOrg" />
    <result column="purchase_group" jdbcType="VARCHAR" property="purchaseGroup" />
    <result column="purchase_type" jdbcType="VARCHAR" property="purchaseType" />
    <result column="supplier_no" jdbcType="VARCHAR" property="supplierNo" />
    <result column="supplier_salesman_code" jdbcType="VARCHAR" property="supplierSalesmanCode" />
    <result column="pay_code" jdbcType="VARCHAR" property="payCode" />
    <result column="purchase_date" jdbcType="TIMESTAMP" property="purchaseDate" />
    <result column="purchase_person" jdbcType="VARCHAR" property="purchasePerson" />
    <result column="no_tax_amount" jdbcType="DECIMAL" property="noTaxAmount" />
    <result column="tax_amount" jdbcType="DECIMAL" property="taxAmount" />
    <result column="have_tax_amount" jdbcType="DECIMAL" property="haveTaxAmount" />
    <result column="plan_no_tax_amount" jdbcType="DECIMAL" property="planNoTaxAmount" />
    <result column="plan_tax_amount" jdbcType="DECIMAL" property="planTaxAmount" />
    <result column="plan_have_tax_amount" jdbcType="DECIMAL" property="planHaveTaxAmount" />
    <result column="confirm_no_tax_amount" jdbcType="DECIMAL" property="confirmNoTaxAmount" />
    <result column="confirm_tax_amount" jdbcType="DECIMAL" property="confirmTaxAmount" />
    <result column="confirm_have_tax_amount" jdbcType="DECIMAL" property="confirmHaveTaxAmount" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="tel" jdbcType="VARCHAR" property="tel" />
    <result column="bak1" jdbcType="VARCHAR" property="bak1" />
    <result column="bak2" jdbcType="VARCHAR" property="bak2" />
    <result column="order_amount" jdbcType="VARCHAR" property="orderAmount" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="last_update_date" jdbcType="TIMESTAMP" property="lastUpdateDate" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="mark" jdbcType="VARCHAR" property="mark" />
    <result column="btype" jdbcType="VARCHAR" property="btype" />
    <result column="is_send" jdbcType="TINYINT" property="isSend" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, purchase_order_no, purchase_plan_no, bguid, purchaser_code, purchase_delete_id, 
    confirm_status, deliver_status, confirm_time, close_time_limit, close_time, order_source, 
    plan_create_time, plan_recept_time, plan_confirm_time, plan_trans_time, create_date, 
    org_id, org_name, warehouse_code, warehouse_name, purchase_org, purchase_group, purchase_type, 
    supplier_no, supplier_salesman_code, pay_code, purchase_date, purchase_person, no_tax_amount, 
    tax_amount, have_tax_amount, plan_no_tax_amount, plan_tax_amount, plan_have_tax_amount, 
    confirm_no_tax_amount, confirm_tax_amount, confirm_have_tax_amount, address, tel, 
    bak1, bak2, order_amount, gmt_create, gmt_update, `operator`, last_update_date, company_code, 
    mark, btype, is_send
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmPurchaseOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_purchase_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_purchase_order
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from srm_purchase_order
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmPurchaseOrderExample">
    delete from srm_purchase_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmPurchaseOrder">
    insert into srm_purchase_order (id, purchase_order_no, purchase_plan_no, 
      bguid, purchaser_code, purchase_delete_id, 
      confirm_status, deliver_status, confirm_time, 
      close_time_limit, close_time, order_source, 
      plan_create_time, plan_recept_time, plan_confirm_time, 
      plan_trans_time, create_date, org_id, 
      org_name, warehouse_code, warehouse_name, 
      purchase_org, purchase_group, purchase_type, 
      supplier_no, supplier_salesman_code, pay_code, 
      purchase_date, purchase_person, no_tax_amount, 
      tax_amount, have_tax_amount, plan_no_tax_amount, 
      plan_tax_amount, plan_have_tax_amount, confirm_no_tax_amount, 
      confirm_tax_amount, confirm_have_tax_amount, 
      address, tel, bak1, 
      bak2, order_amount, gmt_create, 
      gmt_update, `operator`, last_update_date, 
      company_code, mark, btype, 
      is_send)
    values (#{id,jdbcType=INTEGER}, #{purchaseOrderNo,jdbcType=VARCHAR}, #{purchasePlanNo,jdbcType=VARCHAR}, 
      #{bguid,jdbcType=VARCHAR}, #{purchaserCode,jdbcType=VARCHAR}, #{purchaseDeleteId,jdbcType=VARCHAR}, 
      #{confirmStatus,jdbcType=INTEGER}, #{deliverStatus,jdbcType=INTEGER}, #{confirmTime,jdbcType=TIMESTAMP}, 
      #{closeTimeLimit,jdbcType=INTEGER}, #{closeTime,jdbcType=TIMESTAMP}, #{orderSource,jdbcType=TINYINT}, 
      #{planCreateTime,jdbcType=TIMESTAMP}, #{planReceptTime,jdbcType=TIMESTAMP}, #{planConfirmTime,jdbcType=TIMESTAMP}, 
      #{planTransTime,jdbcType=TIMESTAMP}, #{createDate,jdbcType=TIMESTAMP}, #{orgId,jdbcType=VARCHAR}, 
      #{orgName,jdbcType=VARCHAR}, #{warehouseCode,jdbcType=VARCHAR}, #{warehouseName,jdbcType=VARCHAR}, 
      #{purchaseOrg,jdbcType=VARCHAR}, #{purchaseGroup,jdbcType=VARCHAR}, #{purchaseType,jdbcType=VARCHAR}, 
      #{supplierNo,jdbcType=VARCHAR}, #{supplierSalesmanCode,jdbcType=VARCHAR}, #{payCode,jdbcType=VARCHAR}, 
      #{purchaseDate,jdbcType=TIMESTAMP}, #{purchasePerson,jdbcType=VARCHAR}, #{noTaxAmount,jdbcType=DECIMAL}, 
      #{taxAmount,jdbcType=DECIMAL}, #{haveTaxAmount,jdbcType=DECIMAL}, #{planNoTaxAmount,jdbcType=DECIMAL}, 
      #{planTaxAmount,jdbcType=DECIMAL}, #{planHaveTaxAmount,jdbcType=DECIMAL}, #{confirmNoTaxAmount,jdbcType=DECIMAL}, 
      #{confirmTaxAmount,jdbcType=DECIMAL}, #{confirmHaveTaxAmount,jdbcType=DECIMAL}, 
      #{address,jdbcType=VARCHAR}, #{tel,jdbcType=VARCHAR}, #{bak1,jdbcType=VARCHAR}, 
      #{bak2,jdbcType=VARCHAR}, #{orderAmount,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{operator,jdbcType=VARCHAR}, #{lastUpdateDate,jdbcType=TIMESTAMP}, 
      #{companyCode,jdbcType=VARCHAR}, #{mark,jdbcType=VARCHAR}, #{btype,jdbcType=VARCHAR}, 
      #{isSend,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmPurchaseOrder">
    insert into srm_purchase_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="purchaseOrderNo != null">
        purchase_order_no,
      </if>
      <if test="purchasePlanNo != null">
        purchase_plan_no,
      </if>
      <if test="bguid != null">
        bguid,
      </if>
      <if test="purchaserCode != null">
        purchaser_code,
      </if>
      <if test="purchaseDeleteId != null">
        purchase_delete_id,
      </if>
      <if test="confirmStatus != null">
        confirm_status,
      </if>
      <if test="deliverStatus != null">
        deliver_status,
      </if>
      <if test="confirmTime != null">
        confirm_time,
      </if>
      <if test="closeTimeLimit != null">
        close_time_limit,
      </if>
      <if test="closeTime != null">
        close_time,
      </if>
      <if test="orderSource != null">
        order_source,
      </if>
      <if test="planCreateTime != null">
        plan_create_time,
      </if>
      <if test="planReceptTime != null">
        plan_recept_time,
      </if>
      <if test="planConfirmTime != null">
        plan_confirm_time,
      </if>
      <if test="planTransTime != null">
        plan_trans_time,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="warehouseCode != null">
        warehouse_code,
      </if>
      <if test="warehouseName != null">
        warehouse_name,
      </if>
      <if test="purchaseOrg != null">
        purchase_org,
      </if>
      <if test="purchaseGroup != null">
        purchase_group,
      </if>
      <if test="purchaseType != null">
        purchase_type,
      </if>
      <if test="supplierNo != null">
        supplier_no,
      </if>
      <if test="supplierSalesmanCode != null">
        supplier_salesman_code,
      </if>
      <if test="payCode != null">
        pay_code,
      </if>
      <if test="purchaseDate != null">
        purchase_date,
      </if>
      <if test="purchasePerson != null">
        purchase_person,
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount,
      </if>
      <if test="taxAmount != null">
        tax_amount,
      </if>
      <if test="haveTaxAmount != null">
        have_tax_amount,
      </if>
      <if test="planNoTaxAmount != null">
        plan_no_tax_amount,
      </if>
      <if test="planTaxAmount != null">
        plan_tax_amount,
      </if>
      <if test="planHaveTaxAmount != null">
        plan_have_tax_amount,
      </if>
      <if test="confirmNoTaxAmount != null">
        confirm_no_tax_amount,
      </if>
      <if test="confirmTaxAmount != null">
        confirm_tax_amount,
      </if>
      <if test="confirmHaveTaxAmount != null">
        confirm_have_tax_amount,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="tel != null">
        tel,
      </if>
      <if test="bak1 != null">
        bak1,
      </if>
      <if test="bak2 != null">
        bak2,
      </if>
      <if test="orderAmount != null">
        order_amount,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="operator != null">
        `operator`,
      </if>
      <if test="lastUpdateDate != null">
        last_update_date,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="mark != null">
        mark,
      </if>
      <if test="btype != null">
        btype,
      </if>
      <if test="isSend != null">
        is_send,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="purchaseOrderNo != null">
        #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchasePlanNo != null">
        #{purchasePlanNo,jdbcType=VARCHAR},
      </if>
      <if test="bguid != null">
        #{bguid,jdbcType=VARCHAR},
      </if>
      <if test="purchaserCode != null">
        #{purchaserCode,jdbcType=VARCHAR},
      </if>
      <if test="purchaseDeleteId != null">
        #{purchaseDeleteId,jdbcType=VARCHAR},
      </if>
      <if test="confirmStatus != null">
        #{confirmStatus,jdbcType=INTEGER},
      </if>
      <if test="deliverStatus != null">
        #{deliverStatus,jdbcType=INTEGER},
      </if>
      <if test="confirmTime != null">
        #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="closeTimeLimit != null">
        #{closeTimeLimit,jdbcType=INTEGER},
      </if>
      <if test="closeTime != null">
        #{closeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderSource != null">
        #{orderSource,jdbcType=TINYINT},
      </if>
      <if test="planCreateTime != null">
        #{planCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="planReceptTime != null">
        #{planReceptTime,jdbcType=TIMESTAMP},
      </if>
      <if test="planConfirmTime != null">
        #{planConfirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="planTransTime != null">
        #{planTransTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrg != null">
        #{purchaseOrg,jdbcType=VARCHAR},
      </if>
      <if test="purchaseGroup != null">
        #{purchaseGroup,jdbcType=VARCHAR},
      </if>
      <if test="purchaseType != null">
        #{purchaseType,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null">
        #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierSalesmanCode != null">
        #{supplierSalesmanCode,jdbcType=VARCHAR},
      </if>
      <if test="payCode != null">
        #{payCode,jdbcType=VARCHAR},
      </if>
      <if test="purchaseDate != null">
        #{purchaseDate,jdbcType=TIMESTAMP},
      </if>
      <if test="purchasePerson != null">
        #{purchasePerson,jdbcType=VARCHAR},
      </if>
      <if test="noTaxAmount != null">
        #{noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="haveTaxAmount != null">
        #{haveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="planNoTaxAmount != null">
        #{planNoTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="planTaxAmount != null">
        #{planTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="planHaveTaxAmount != null">
        #{planHaveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="confirmNoTaxAmount != null">
        #{confirmNoTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="confirmTaxAmount != null">
        #{confirmTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="confirmHaveTaxAmount != null">
        #{confirmHaveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="tel != null">
        #{tel,jdbcType=VARCHAR},
      </if>
      <if test="bak1 != null">
        #{bak1,jdbcType=VARCHAR},
      </if>
      <if test="bak2 != null">
        #{bak2,jdbcType=VARCHAR},
      </if>
      <if test="orderAmount != null">
        #{orderAmount,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null">
        #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="mark != null">
        #{mark,jdbcType=VARCHAR},
      </if>
      <if test="btype != null">
        #{btype,jdbcType=VARCHAR},
      </if>
      <if test="isSend != null">
        #{isSend,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmPurchaseOrderExample" resultType="java.lang.Long">
    select count(*) from srm_purchase_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_purchase_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.purchaseOrderNo != null">
        purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.purchasePlanNo != null">
        purchase_plan_no = #{record.purchasePlanNo,jdbcType=VARCHAR},
      </if>
      <if test="record.bguid != null">
        bguid = #{record.bguid,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaserCode != null">
        purchaser_code = #{record.purchaserCode,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseDeleteId != null">
        purchase_delete_id = #{record.purchaseDeleteId,jdbcType=VARCHAR},
      </if>
      <if test="record.confirmStatus != null">
        confirm_status = #{record.confirmStatus,jdbcType=INTEGER},
      </if>
      <if test="record.deliverStatus != null">
        deliver_status = #{record.deliverStatus,jdbcType=INTEGER},
      </if>
      <if test="record.confirmTime != null">
        confirm_time = #{record.confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.closeTimeLimit != null">
        close_time_limit = #{record.closeTimeLimit,jdbcType=INTEGER},
      </if>
      <if test="record.closeTime != null">
        close_time = #{record.closeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderSource != null">
        order_source = #{record.orderSource,jdbcType=TINYINT},
      </if>
      <if test="record.planCreateTime != null">
        plan_create_time = #{record.planCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.planReceptTime != null">
        plan_recept_time = #{record.planReceptTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.planConfirmTime != null">
        plan_confirm_time = #{record.planConfirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.planTransTime != null">
        plan_trans_time = #{record.planTransTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createDate != null">
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=VARCHAR},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseCode != null">
        warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseName != null">
        warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrg != null">
        purchase_org = #{record.purchaseOrg,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseGroup != null">
        purchase_group = #{record.purchaseGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseType != null">
        purchase_type = #{record.purchaseType,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierNo != null">
        supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierSalesmanCode != null">
        supplier_salesman_code = #{record.supplierSalesmanCode,jdbcType=VARCHAR},
      </if>
      <if test="record.payCode != null">
        pay_code = #{record.payCode,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseDate != null">
        purchase_date = #{record.purchaseDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.purchasePerson != null">
        purchase_person = #{record.purchasePerson,jdbcType=VARCHAR},
      </if>
      <if test="record.noTaxAmount != null">
        no_tax_amount = #{record.noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.taxAmount != null">
        tax_amount = #{record.taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.haveTaxAmount != null">
        have_tax_amount = #{record.haveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.planNoTaxAmount != null">
        plan_no_tax_amount = #{record.planNoTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.planTaxAmount != null">
        plan_tax_amount = #{record.planTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.planHaveTaxAmount != null">
        plan_have_tax_amount = #{record.planHaveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.confirmNoTaxAmount != null">
        confirm_no_tax_amount = #{record.confirmNoTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.confirmTaxAmount != null">
        confirm_tax_amount = #{record.confirmTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.confirmHaveTaxAmount != null">
        confirm_have_tax_amount = #{record.confirmHaveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.address != null">
        address = #{record.address,jdbcType=VARCHAR},
      </if>
      <if test="record.tel != null">
        tel = #{record.tel,jdbcType=VARCHAR},
      </if>
      <if test="record.bak1 != null">
        bak1 = #{record.bak1,jdbcType=VARCHAR},
      </if>
      <if test="record.bak2 != null">
        bak2 = #{record.bak2,jdbcType=VARCHAR},
      </if>
      <if test="record.orderAmount != null">
        order_amount = #{record.orderAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operator != null">
        `operator` = #{record.operator,jdbcType=VARCHAR},
      </if>
      <if test="record.lastUpdateDate != null">
        last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.mark != null">
        mark = #{record.mark,jdbcType=VARCHAR},
      </if>
      <if test="record.btype != null">
        btype = #{record.btype,jdbcType=VARCHAR},
      </if>
      <if test="record.isSend != null">
        is_send = #{record.isSend,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_purchase_order
    set id = #{record.id,jdbcType=INTEGER},
      purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      purchase_plan_no = #{record.purchasePlanNo,jdbcType=VARCHAR},
      bguid = #{record.bguid,jdbcType=VARCHAR},
      purchaser_code = #{record.purchaserCode,jdbcType=VARCHAR},
      purchase_delete_id = #{record.purchaseDeleteId,jdbcType=VARCHAR},
      confirm_status = #{record.confirmStatus,jdbcType=INTEGER},
      deliver_status = #{record.deliverStatus,jdbcType=INTEGER},
      confirm_time = #{record.confirmTime,jdbcType=TIMESTAMP},
      close_time_limit = #{record.closeTimeLimit,jdbcType=INTEGER},
      close_time = #{record.closeTime,jdbcType=TIMESTAMP},
      order_source = #{record.orderSource,jdbcType=TINYINT},
      plan_create_time = #{record.planCreateTime,jdbcType=TIMESTAMP},
      plan_recept_time = #{record.planReceptTime,jdbcType=TIMESTAMP},
      plan_confirm_time = #{record.planConfirmTime,jdbcType=TIMESTAMP},
      plan_trans_time = #{record.planTransTime,jdbcType=TIMESTAMP},
      create_date = #{record.createDate,jdbcType=TIMESTAMP},
      org_id = #{record.orgId,jdbcType=VARCHAR},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      purchase_org = #{record.purchaseOrg,jdbcType=VARCHAR},
      purchase_group = #{record.purchaseGroup,jdbcType=VARCHAR},
      purchase_type = #{record.purchaseType,jdbcType=VARCHAR},
      supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      supplier_salesman_code = #{record.supplierSalesmanCode,jdbcType=VARCHAR},
      pay_code = #{record.payCode,jdbcType=VARCHAR},
      purchase_date = #{record.purchaseDate,jdbcType=TIMESTAMP},
      purchase_person = #{record.purchasePerson,jdbcType=VARCHAR},
      no_tax_amount = #{record.noTaxAmount,jdbcType=DECIMAL},
      tax_amount = #{record.taxAmount,jdbcType=DECIMAL},
      have_tax_amount = #{record.haveTaxAmount,jdbcType=DECIMAL},
      plan_no_tax_amount = #{record.planNoTaxAmount,jdbcType=DECIMAL},
      plan_tax_amount = #{record.planTaxAmount,jdbcType=DECIMAL},
      plan_have_tax_amount = #{record.planHaveTaxAmount,jdbcType=DECIMAL},
      confirm_no_tax_amount = #{record.confirmNoTaxAmount,jdbcType=DECIMAL},
      confirm_tax_amount = #{record.confirmTaxAmount,jdbcType=DECIMAL},
      confirm_have_tax_amount = #{record.confirmHaveTaxAmount,jdbcType=DECIMAL},
      address = #{record.address,jdbcType=VARCHAR},
      tel = #{record.tel,jdbcType=VARCHAR},
      bak1 = #{record.bak1,jdbcType=VARCHAR},
      bak2 = #{record.bak2,jdbcType=VARCHAR},
      order_amount = #{record.orderAmount,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      `operator` = #{record.operator,jdbcType=VARCHAR},
      last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      mark = #{record.mark,jdbcType=VARCHAR},
      btype = #{record.btype,jdbcType=VARCHAR},
      is_send = #{record.isSend,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmPurchaseOrder">
    update srm_purchase_order
    <set>
      <if test="purchaseOrderNo != null">
        purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchasePlanNo != null">
        purchase_plan_no = #{purchasePlanNo,jdbcType=VARCHAR},
      </if>
      <if test="bguid != null">
        bguid = #{bguid,jdbcType=VARCHAR},
      </if>
      <if test="purchaserCode != null">
        purchaser_code = #{purchaserCode,jdbcType=VARCHAR},
      </if>
      <if test="purchaseDeleteId != null">
        purchase_delete_id = #{purchaseDeleteId,jdbcType=VARCHAR},
      </if>
      <if test="confirmStatus != null">
        confirm_status = #{confirmStatus,jdbcType=INTEGER},
      </if>
      <if test="deliverStatus != null">
        deliver_status = #{deliverStatus,jdbcType=INTEGER},
      </if>
      <if test="confirmTime != null">
        confirm_time = #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="closeTimeLimit != null">
        close_time_limit = #{closeTimeLimit,jdbcType=INTEGER},
      </if>
      <if test="closeTime != null">
        close_time = #{closeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderSource != null">
        order_source = #{orderSource,jdbcType=TINYINT},
      </if>
      <if test="planCreateTime != null">
        plan_create_time = #{planCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="planReceptTime != null">
        plan_recept_time = #{planReceptTime,jdbcType=TIMESTAMP},
      </if>
      <if test="planConfirmTime != null">
        plan_confirm_time = #{planConfirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="planTransTime != null">
        plan_trans_time = #{planTransTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrg != null">
        purchase_org = #{purchaseOrg,jdbcType=VARCHAR},
      </if>
      <if test="purchaseGroup != null">
        purchase_group = #{purchaseGroup,jdbcType=VARCHAR},
      </if>
      <if test="purchaseType != null">
        purchase_type = #{purchaseType,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null">
        supplier_no = #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierSalesmanCode != null">
        supplier_salesman_code = #{supplierSalesmanCode,jdbcType=VARCHAR},
      </if>
      <if test="payCode != null">
        pay_code = #{payCode,jdbcType=VARCHAR},
      </if>
      <if test="purchaseDate != null">
        purchase_date = #{purchaseDate,jdbcType=TIMESTAMP},
      </if>
      <if test="purchasePerson != null">
        purchase_person = #{purchasePerson,jdbcType=VARCHAR},
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount = #{noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        tax_amount = #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="haveTaxAmount != null">
        have_tax_amount = #{haveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="planNoTaxAmount != null">
        plan_no_tax_amount = #{planNoTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="planTaxAmount != null">
        plan_tax_amount = #{planTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="planHaveTaxAmount != null">
        plan_have_tax_amount = #{planHaveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="confirmNoTaxAmount != null">
        confirm_no_tax_amount = #{confirmNoTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="confirmTaxAmount != null">
        confirm_tax_amount = #{confirmTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="confirmHaveTaxAmount != null">
        confirm_have_tax_amount = #{confirmHaveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="tel != null">
        tel = #{tel,jdbcType=VARCHAR},
      </if>
      <if test="bak1 != null">
        bak1 = #{bak1,jdbcType=VARCHAR},
      </if>
      <if test="bak2 != null">
        bak2 = #{bak2,jdbcType=VARCHAR},
      </if>
      <if test="orderAmount != null">
        order_amount = #{orderAmount,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="operator != null">
        `operator` = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null">
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="mark != null">
        mark = #{mark,jdbcType=VARCHAR},
      </if>
      <if test="btype != null">
        btype = #{btype,jdbcType=VARCHAR},
      </if>
      <if test="isSend != null">
        is_send = #{isSend,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmPurchaseOrder">
    update srm_purchase_order
    set purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      purchase_plan_no = #{purchasePlanNo,jdbcType=VARCHAR},
      bguid = #{bguid,jdbcType=VARCHAR},
      purchaser_code = #{purchaserCode,jdbcType=VARCHAR},
      purchase_delete_id = #{purchaseDeleteId,jdbcType=VARCHAR},
      confirm_status = #{confirmStatus,jdbcType=INTEGER},
      deliver_status = #{deliverStatus,jdbcType=INTEGER},
      confirm_time = #{confirmTime,jdbcType=TIMESTAMP},
      close_time_limit = #{closeTimeLimit,jdbcType=INTEGER},
      close_time = #{closeTime,jdbcType=TIMESTAMP},
      order_source = #{orderSource,jdbcType=TINYINT},
      plan_create_time = #{planCreateTime,jdbcType=TIMESTAMP},
      plan_recept_time = #{planReceptTime,jdbcType=TIMESTAMP},
      plan_confirm_time = #{planConfirmTime,jdbcType=TIMESTAMP},
      plan_trans_time = #{planTransTime,jdbcType=TIMESTAMP},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      org_id = #{orgId,jdbcType=VARCHAR},
      org_name = #{orgName,jdbcType=VARCHAR},
      warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      purchase_org = #{purchaseOrg,jdbcType=VARCHAR},
      purchase_group = #{purchaseGroup,jdbcType=VARCHAR},
      purchase_type = #{purchaseType,jdbcType=VARCHAR},
      supplier_no = #{supplierNo,jdbcType=VARCHAR},
      supplier_salesman_code = #{supplierSalesmanCode,jdbcType=VARCHAR},
      pay_code = #{payCode,jdbcType=VARCHAR},
      purchase_date = #{purchaseDate,jdbcType=TIMESTAMP},
      purchase_person = #{purchasePerson,jdbcType=VARCHAR},
      no_tax_amount = #{noTaxAmount,jdbcType=DECIMAL},
      tax_amount = #{taxAmount,jdbcType=DECIMAL},
      have_tax_amount = #{haveTaxAmount,jdbcType=DECIMAL},
      plan_no_tax_amount = #{planNoTaxAmount,jdbcType=DECIMAL},
      plan_tax_amount = #{planTaxAmount,jdbcType=DECIMAL},
      plan_have_tax_amount = #{planHaveTaxAmount,jdbcType=DECIMAL},
      confirm_no_tax_amount = #{confirmNoTaxAmount,jdbcType=DECIMAL},
      confirm_tax_amount = #{confirmTaxAmount,jdbcType=DECIMAL},
      confirm_have_tax_amount = #{confirmHaveTaxAmount,jdbcType=DECIMAL},
      address = #{address,jdbcType=VARCHAR},
      tel = #{tel,jdbcType=VARCHAR},
      bak1 = #{bak1,jdbcType=VARCHAR},
      bak2 = #{bak2,jdbcType=VARCHAR},
      order_amount = #{orderAmount,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      `operator` = #{operator,jdbcType=VARCHAR},
      last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      company_code = #{companyCode,jdbcType=VARCHAR},
      mark = #{mark,jdbcType=VARCHAR},
      btype = #{btype,jdbcType=VARCHAR},
      is_send = #{isSend,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>