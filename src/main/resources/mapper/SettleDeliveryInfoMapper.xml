<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SettleDeliveryInfoMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SettleDeliveryInfo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="settle_order_no" jdbcType="VARCHAR" property="settleOrderNo" />
    <result column="delivery_no" jdbcType="VARCHAR" property="deliveryNo" />
    <result column="delivery_id" jdbcType="VARCHAR" property="deliveryId" />
    <result column="delivery_year" jdbcType="VARCHAR" property="deliveryYear" />
    <result column="delivery_key" jdbcType="VARCHAR" property="deliveryKey" />
    <result column="delivery_date" jdbcType="TIMESTAMP" property="deliveryDate" />
    <result column="purchase_order_no" jdbcType="VARCHAR" property="purchaseOrderNo" />
    <result column="purchase_order_id" jdbcType="VARCHAR" property="purchaseOrderId" />
    <result column="purchase_org_name" jdbcType="VARCHAR" property="purchaseOrgName" />
    <result column="purchase_org_no" jdbcType="VARCHAR" property="purchaseOrgNo" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="base_unit" jdbcType="VARCHAR" property="baseUnit" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="deduct_from_count" jdbcType="DECIMAL" property="deductFromCount" />
    <result column="red_rush" jdbcType="INTEGER" property="redRush" />
    <result column="price_unit" jdbcType="VARCHAR" property="priceUnit" />
    <result column="no_tax_amount" jdbcType="DECIMAL" property="noTaxAmount" />
    <result column="have_tax_amount" jdbcType="DECIMAL" property="haveTaxAmount" />
    <result column="no_tax_price" jdbcType="DECIMAL" property="noTaxPrice" />
    <result column="have_tax_price" jdbcType="DECIMAL" property="haveTaxPrice" />
    <result column="tax_rate" jdbcType="VARCHAR" property="taxRate" />
    <result column="tax_amount" jdbcType="DECIMAL" property="taxAmount" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, settle_order_no, delivery_no, delivery_id, delivery_year, delivery_key, delivery_date, 
    purchase_order_no, purchase_order_id, purchase_org_name, purchase_org_no, goods_code, 
    base_unit, batch_no, order_type, create_date, deduct_from_count, red_rush, price_unit, 
    no_tax_amount, have_tax_amount, no_tax_price, have_tax_price, tax_rate, tax_amount, 
    gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SettleDeliveryInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from settle_delivery_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from settle_delivery_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from settle_delivery_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SettleDeliveryInfoExample">
    delete from settle_delivery_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SettleDeliveryInfo">
    insert into settle_delivery_info (id, settle_order_no, delivery_no, 
      delivery_id, delivery_year, delivery_key, 
      delivery_date, purchase_order_no, purchase_order_id, 
      purchase_org_name, purchase_org_no, goods_code, 
      base_unit, batch_no, order_type, 
      create_date, deduct_from_count, red_rush, 
      price_unit, no_tax_amount, have_tax_amount, 
      no_tax_price, have_tax_price, tax_rate, 
      tax_amount, gmt_create, gmt_update
      )
    values (#{id,jdbcType=INTEGER}, #{settleOrderNo,jdbcType=VARCHAR}, #{deliveryNo,jdbcType=VARCHAR}, 
      #{deliveryId,jdbcType=VARCHAR}, #{deliveryYear,jdbcType=VARCHAR}, #{deliveryKey,jdbcType=VARCHAR}, 
      #{deliveryDate,jdbcType=TIMESTAMP}, #{purchaseOrderNo,jdbcType=VARCHAR}, #{purchaseOrderId,jdbcType=VARCHAR}, 
      #{purchaseOrgName,jdbcType=VARCHAR}, #{purchaseOrgNo,jdbcType=VARCHAR}, #{goodsCode,jdbcType=VARCHAR}, 
      #{baseUnit,jdbcType=VARCHAR}, #{batchNo,jdbcType=VARCHAR}, #{orderType,jdbcType=INTEGER}, 
      #{createDate,jdbcType=TIMESTAMP}, #{deductFromCount,jdbcType=DECIMAL}, #{redRush,jdbcType=INTEGER}, 
      #{priceUnit,jdbcType=VARCHAR}, #{noTaxAmount,jdbcType=DECIMAL}, #{haveTaxAmount,jdbcType=DECIMAL}, 
      #{noTaxPrice,jdbcType=DECIMAL}, #{haveTaxPrice,jdbcType=DECIMAL}, #{taxRate,jdbcType=VARCHAR}, 
      #{taxAmount,jdbcType=DECIMAL}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SettleDeliveryInfo">
    insert into settle_delivery_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="settleOrderNo != null">
        settle_order_no,
      </if>
      <if test="deliveryNo != null">
        delivery_no,
      </if>
      <if test="deliveryId != null">
        delivery_id,
      </if>
      <if test="deliveryYear != null">
        delivery_year,
      </if>
      <if test="deliveryKey != null">
        delivery_key,
      </if>
      <if test="deliveryDate != null">
        delivery_date,
      </if>
      <if test="purchaseOrderNo != null">
        purchase_order_no,
      </if>
      <if test="purchaseOrderId != null">
        purchase_order_id,
      </if>
      <if test="purchaseOrgName != null">
        purchase_org_name,
      </if>
      <if test="purchaseOrgNo != null">
        purchase_org_no,
      </if>
      <if test="goodsCode != null">
        goods_code,
      </if>
      <if test="baseUnit != null">
        base_unit,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="deductFromCount != null">
        deduct_from_count,
      </if>
      <if test="redRush != null">
        red_rush,
      </if>
      <if test="priceUnit != null">
        price_unit,
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount,
      </if>
      <if test="haveTaxAmount != null">
        have_tax_amount,
      </if>
      <if test="noTaxPrice != null">
        no_tax_price,
      </if>
      <if test="haveTaxPrice != null">
        have_tax_price,
      </if>
      <if test="taxRate != null">
        tax_rate,
      </if>
      <if test="taxAmount != null">
        tax_amount,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="settleOrderNo != null">
        #{settleOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryNo != null">
        #{deliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryId != null">
        #{deliveryId,jdbcType=VARCHAR},
      </if>
      <if test="deliveryYear != null">
        #{deliveryYear,jdbcType=VARCHAR},
      </if>
      <if test="deliveryKey != null">
        #{deliveryKey,jdbcType=VARCHAR},
      </if>
      <if test="deliveryDate != null">
        #{deliveryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="purchaseOrderNo != null">
        #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderId != null">
        #{purchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrgName != null">
        #{purchaseOrgName,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrgNo != null">
        #{purchaseOrgNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="baseUnit != null">
        #{baseUnit,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="deductFromCount != null">
        #{deductFromCount,jdbcType=DECIMAL},
      </if>
      <if test="redRush != null">
        #{redRush,jdbcType=INTEGER},
      </if>
      <if test="priceUnit != null">
        #{priceUnit,jdbcType=VARCHAR},
      </if>
      <if test="noTaxAmount != null">
        #{noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="haveTaxAmount != null">
        #{haveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="noTaxPrice != null">
        #{noTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="haveTaxPrice != null">
        #{haveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=VARCHAR},
      </if>
      <if test="taxAmount != null">
        #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SettleDeliveryInfoExample" resultType="java.lang.Long">
    select count(*) from settle_delivery_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update settle_delivery_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.settleOrderNo != null">
        settle_order_no = #{record.settleOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryNo != null">
        delivery_no = #{record.deliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryId != null">
        delivery_id = #{record.deliveryId,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryYear != null">
        delivery_year = #{record.deliveryYear,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryKey != null">
        delivery_key = #{record.deliveryKey,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryDate != null">
        delivery_date = #{record.deliveryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.purchaseOrderNo != null">
        purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrderId != null">
        purchase_order_id = #{record.purchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrgName != null">
        purchase_org_name = #{record.purchaseOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrgNo != null">
        purchase_org_no = #{record.purchaseOrgNo,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCode != null">
        goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.baseUnit != null">
        base_unit = #{record.baseUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=INTEGER},
      </if>
      <if test="record.createDate != null">
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deductFromCount != null">
        deduct_from_count = #{record.deductFromCount,jdbcType=DECIMAL},
      </if>
      <if test="record.redRush != null">
        red_rush = #{record.redRush,jdbcType=INTEGER},
      </if>
      <if test="record.priceUnit != null">
        price_unit = #{record.priceUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.noTaxAmount != null">
        no_tax_amount = #{record.noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.haveTaxAmount != null">
        have_tax_amount = #{record.haveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.noTaxPrice != null">
        no_tax_price = #{record.noTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.haveTaxPrice != null">
        have_tax_price = #{record.haveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.taxRate != null">
        tax_rate = #{record.taxRate,jdbcType=VARCHAR},
      </if>
      <if test="record.taxAmount != null">
        tax_amount = #{record.taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update settle_delivery_info
    set id = #{record.id,jdbcType=INTEGER},
      settle_order_no = #{record.settleOrderNo,jdbcType=VARCHAR},
      delivery_no = #{record.deliveryNo,jdbcType=VARCHAR},
      delivery_id = #{record.deliveryId,jdbcType=VARCHAR},
      delivery_year = #{record.deliveryYear,jdbcType=VARCHAR},
      delivery_key = #{record.deliveryKey,jdbcType=VARCHAR},
      delivery_date = #{record.deliveryDate,jdbcType=TIMESTAMP},
      purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      purchase_order_id = #{record.purchaseOrderId,jdbcType=VARCHAR},
      purchase_org_name = #{record.purchaseOrgName,jdbcType=VARCHAR},
      purchase_org_no = #{record.purchaseOrgNo,jdbcType=VARCHAR},
      goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      base_unit = #{record.baseUnit,jdbcType=VARCHAR},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      order_type = #{record.orderType,jdbcType=INTEGER},
      create_date = #{record.createDate,jdbcType=TIMESTAMP},
      deduct_from_count = #{record.deductFromCount,jdbcType=DECIMAL},
      red_rush = #{record.redRush,jdbcType=INTEGER},
      price_unit = #{record.priceUnit,jdbcType=VARCHAR},
      no_tax_amount = #{record.noTaxAmount,jdbcType=DECIMAL},
      have_tax_amount = #{record.haveTaxAmount,jdbcType=DECIMAL},
      no_tax_price = #{record.noTaxPrice,jdbcType=DECIMAL},
      have_tax_price = #{record.haveTaxPrice,jdbcType=DECIMAL},
      tax_rate = #{record.taxRate,jdbcType=VARCHAR},
      tax_amount = #{record.taxAmount,jdbcType=DECIMAL},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SettleDeliveryInfo">
    update settle_delivery_info
    <set>
      <if test="settleOrderNo != null">
        settle_order_no = #{settleOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryNo != null">
        delivery_no = #{deliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryId != null">
        delivery_id = #{deliveryId,jdbcType=VARCHAR},
      </if>
      <if test="deliveryYear != null">
        delivery_year = #{deliveryYear,jdbcType=VARCHAR},
      </if>
      <if test="deliveryKey != null">
        delivery_key = #{deliveryKey,jdbcType=VARCHAR},
      </if>
      <if test="deliveryDate != null">
        delivery_date = #{deliveryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="purchaseOrderNo != null">
        purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderId != null">
        purchase_order_id = #{purchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrgName != null">
        purchase_org_name = #{purchaseOrgName,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrgNo != null">
        purchase_org_no = #{purchaseOrgNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        goods_code = #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="baseUnit != null">
        base_unit = #{baseUnit,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="deductFromCount != null">
        deduct_from_count = #{deductFromCount,jdbcType=DECIMAL},
      </if>
      <if test="redRush != null">
        red_rush = #{redRush,jdbcType=INTEGER},
      </if>
      <if test="priceUnit != null">
        price_unit = #{priceUnit,jdbcType=VARCHAR},
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount = #{noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="haveTaxAmount != null">
        have_tax_amount = #{haveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="noTaxPrice != null">
        no_tax_price = #{noTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="haveTaxPrice != null">
        have_tax_price = #{haveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="taxRate != null">
        tax_rate = #{taxRate,jdbcType=VARCHAR},
      </if>
      <if test="taxAmount != null">
        tax_amount = #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SettleDeliveryInfo">
    update settle_delivery_info
    set settle_order_no = #{settleOrderNo,jdbcType=VARCHAR},
      delivery_no = #{deliveryNo,jdbcType=VARCHAR},
      delivery_id = #{deliveryId,jdbcType=VARCHAR},
      delivery_year = #{deliveryYear,jdbcType=VARCHAR},
      delivery_key = #{deliveryKey,jdbcType=VARCHAR},
      delivery_date = #{deliveryDate,jdbcType=TIMESTAMP},
      purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      purchase_order_id = #{purchaseOrderId,jdbcType=VARCHAR},
      purchase_org_name = #{purchaseOrgName,jdbcType=VARCHAR},
      purchase_org_no = #{purchaseOrgNo,jdbcType=VARCHAR},
      goods_code = #{goodsCode,jdbcType=VARCHAR},
      base_unit = #{baseUnit,jdbcType=VARCHAR},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      order_type = #{orderType,jdbcType=INTEGER},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      deduct_from_count = #{deductFromCount,jdbcType=DECIMAL},
      red_rush = #{redRush,jdbcType=INTEGER},
      price_unit = #{priceUnit,jdbcType=VARCHAR},
      no_tax_amount = #{noTaxAmount,jdbcType=DECIMAL},
      have_tax_amount = #{haveTaxAmount,jdbcType=DECIMAL},
      no_tax_price = #{noTaxPrice,jdbcType=DECIMAL},
      have_tax_price = #{haveTaxPrice,jdbcType=DECIMAL},
      tax_rate = #{taxRate,jdbcType=VARCHAR},
      tax_amount = #{taxAmount,jdbcType=DECIMAL},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>