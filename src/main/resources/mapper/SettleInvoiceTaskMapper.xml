<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SettleInvoiceTaskMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SettleInvoiceTask">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="task_no" jdbcType="VARCHAR" property="taskNo" />
    <result column="gain_type" jdbcType="TINYINT" property="gainType" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="task_date" jdbcType="TIMESTAMP" property="taskDate" />
    <result column="task_status" jdbcType="TINYINT" property="taskStatus" />
    <result column="dist_success_count" jdbcType="INTEGER" property="distSuccessCount" />
    <result column="check_success_count" jdbcType="INTEGER" property="checkSuccessCount" />
    <result column="supplier_no" jdbcType="VARCHAR" property="supplierNo" />
    <result column="os_type" jdbcType="TINYINT" property="osType" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_no, gain_type, file_name, file_url, task_date, task_status, dist_success_count, 
    check_success_count, supplier_no, os_type, `status`, gmt_create, gmt_update, extend, 
    version, created_by, created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SettleInvoiceTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from settle_invoice_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from settle_invoice_task
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from settle_invoice_task
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SettleInvoiceTaskExample">
    delete from settle_invoice_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SettleInvoiceTask">
    insert into settle_invoice_task (id, task_no, gain_type, 
      file_name, file_url, task_date, 
      task_status, dist_success_count, check_success_count, 
      supplier_no, os_type, `status`, 
      gmt_create, gmt_update, extend, 
      version, created_by, created_name, 
      updated_by, updated_name)
    values (#{id,jdbcType=INTEGER}, #{taskNo,jdbcType=VARCHAR}, #{gainType,jdbcType=TINYINT}, 
      #{fileName,jdbcType=VARCHAR}, #{fileUrl,jdbcType=VARCHAR}, #{taskDate,jdbcType=TIMESTAMP}, 
      #{taskStatus,jdbcType=TINYINT}, #{distSuccessCount,jdbcType=INTEGER}, #{checkSuccessCount,jdbcType=INTEGER}, 
      #{supplierNo,jdbcType=VARCHAR}, #{osType,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, 
      #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SettleInvoiceTask">
    insert into settle_invoice_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="taskNo != null">
        task_no,
      </if>
      <if test="gainType != null">
        gain_type,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="fileUrl != null">
        file_url,
      </if>
      <if test="taskDate != null">
        task_date,
      </if>
      <if test="taskStatus != null">
        task_status,
      </if>
      <if test="distSuccessCount != null">
        dist_success_count,
      </if>
      <if test="checkSuccessCount != null">
        check_success_count,
      </if>
      <if test="supplierNo != null">
        supplier_no,
      </if>
      <if test="osType != null">
        os_type,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="taskNo != null">
        #{taskNo,jdbcType=VARCHAR},
      </if>
      <if test="gainType != null">
        #{gainType,jdbcType=TINYINT},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="taskDate != null">
        #{taskDate,jdbcType=TIMESTAMP},
      </if>
      <if test="taskStatus != null">
        #{taskStatus,jdbcType=TINYINT},
      </if>
      <if test="distSuccessCount != null">
        #{distSuccessCount,jdbcType=INTEGER},
      </if>
      <if test="checkSuccessCount != null">
        #{checkSuccessCount,jdbcType=INTEGER},
      </if>
      <if test="supplierNo != null">
        #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="osType != null">
        #{osType,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SettleInvoiceTaskExample" resultType="java.lang.Long">
    select count(*) from settle_invoice_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update settle_invoice_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.taskNo != null">
        task_no = #{record.taskNo,jdbcType=VARCHAR},
      </if>
      <if test="record.gainType != null">
        gain_type = #{record.gainType,jdbcType=TINYINT},
      </if>
      <if test="record.fileName != null">
        file_name = #{record.fileName,jdbcType=VARCHAR},
      </if>
      <if test="record.fileUrl != null">
        file_url = #{record.fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.taskDate != null">
        task_date = #{record.taskDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.taskStatus != null">
        task_status = #{record.taskStatus,jdbcType=TINYINT},
      </if>
      <if test="record.distSuccessCount != null">
        dist_success_count = #{record.distSuccessCount,jdbcType=INTEGER},
      </if>
      <if test="record.checkSuccessCount != null">
        check_success_count = #{record.checkSuccessCount,jdbcType=INTEGER},
      </if>
      <if test="record.supplierNo != null">
        supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="record.osType != null">
        os_type = #{record.osType,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update settle_invoice_task
    set id = #{record.id,jdbcType=INTEGER},
      task_no = #{record.taskNo,jdbcType=VARCHAR},
      gain_type = #{record.gainType,jdbcType=TINYINT},
      file_name = #{record.fileName,jdbcType=VARCHAR},
      file_url = #{record.fileUrl,jdbcType=VARCHAR},
      task_date = #{record.taskDate,jdbcType=TIMESTAMP},
      task_status = #{record.taskStatus,jdbcType=TINYINT},
      dist_success_count = #{record.distSuccessCount,jdbcType=INTEGER},
      check_success_count = #{record.checkSuccessCount,jdbcType=INTEGER},
      supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      os_type = #{record.osType,jdbcType=TINYINT},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SettleInvoiceTask">
    update settle_invoice_task
    <set>
      <if test="taskNo != null">
        task_no = #{taskNo,jdbcType=VARCHAR},
      </if>
      <if test="gainType != null">
        gain_type = #{gainType,jdbcType=TINYINT},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="taskDate != null">
        task_date = #{taskDate,jdbcType=TIMESTAMP},
      </if>
      <if test="taskStatus != null">
        task_status = #{taskStatus,jdbcType=TINYINT},
      </if>
      <if test="distSuccessCount != null">
        dist_success_count = #{distSuccessCount,jdbcType=INTEGER},
      </if>
      <if test="checkSuccessCount != null">
        check_success_count = #{checkSuccessCount,jdbcType=INTEGER},
      </if>
      <if test="supplierNo != null">
        supplier_no = #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="osType != null">
        os_type = #{osType,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SettleInvoiceTask">
    update settle_invoice_task
    set task_no = #{taskNo,jdbcType=VARCHAR},
      gain_type = #{gainType,jdbcType=TINYINT},
      file_name = #{fileName,jdbcType=VARCHAR},
      file_url = #{fileUrl,jdbcType=VARCHAR},
      task_date = #{taskDate,jdbcType=TIMESTAMP},
      task_status = #{taskStatus,jdbcType=TINYINT},
      dist_success_count = #{distSuccessCount,jdbcType=INTEGER},
      check_success_count = #{checkSuccessCount,jdbcType=INTEGER},
      supplier_no = #{supplierNo,jdbcType=VARCHAR},
      os_type = #{osType,jdbcType=TINYINT},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>