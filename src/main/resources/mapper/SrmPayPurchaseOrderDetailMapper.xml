<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmPayPurchaseOrderDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmPayPurchaseOrderDetail">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="pay_purchase_order_no" jdbcType="VARCHAR" property="payPurchaseOrderNo" />
    <result column="purchase_order_no" jdbcType="VARCHAR" property="purchaseOrderNo" />
    <result column="purchase_order_id" jdbcType="VARCHAR" property="purchaseOrderId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="proof_no" jdbcType="VARCHAR" property="proofNo" />
    <result column="proof_id" jdbcType="VARCHAR" property="proofId" />
    <result column="delivery_no" jdbcType="VARCHAR" property="deliveryNo" />
    <result column="delivery_id" jdbcType="VARCHAR" property="deliveryId" />
    <result column="delivery_txt" jdbcType="VARCHAR" property="deliveryTxt" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="goods_description" jdbcType="VARCHAR" property="goodsDescription" />
    <result column="goods_production" jdbcType="VARCHAR" property="goodsProduction" />
    <result column="goods_specifications" jdbcType="VARCHAR" property="goodsSpecifications" />
    <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="order_type" jdbcType="VARCHAR" property="orderType" />
    <result column="pay_condition" jdbcType="VARCHAR" property="payCondition" />
    <result column="pay_amount" jdbcType="DECIMAL" property="payAmount" />
    <result column="tail_differ" jdbcType="DECIMAL" property="tailDiffer" />
    <result column="tax_price" jdbcType="DECIMAL" property="taxPrice" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="basic_date" jdbcType="TIMESTAMP" property="basicDate" />
    <result column="advance_pay_amount" jdbcType="DECIMAL" property="advancePayAmount" />
    <result column="po_type" jdbcType="VARCHAR" property="poType" />
    <result column="item_text" jdbcType="VARCHAR" property="itemText" />
    <result column="bill_no" jdbcType="VARCHAR" property="billNo" />
    <result column="environment" jdbcType="VARCHAR" property="environment" />
    <result column="fiscal_year" jdbcType="VARCHAR" property="fiscalYear" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, pay_purchase_order_no, purchase_order_no, purchase_order_id, company_code, org_id, 
    org_name, proof_no, proof_id, delivery_no, delivery_id, delivery_txt, goods_code, 
    goods_description, goods_production, goods_specifications, supplier_code, supplier_name, 
    order_type, pay_condition, pay_amount, tail_differ, tax_price, create_date, basic_date, 
    advance_pay_amount, po_type, item_text, bill_no, environment, fiscal_year, gmt_create, 
    gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmPayPurchaseOrderDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_pay_purchase_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_pay_purchase_order_detail
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from srm_pay_purchase_order_detail
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmPayPurchaseOrderDetailExample">
    delete from srm_pay_purchase_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmPayPurchaseOrderDetail">
    insert into srm_pay_purchase_order_detail (id, pay_purchase_order_no, purchase_order_no, 
      purchase_order_id, company_code, org_id, 
      org_name, proof_no, proof_id, 
      delivery_no, delivery_id, delivery_txt, 
      goods_code, goods_description, goods_production, 
      goods_specifications, supplier_code, supplier_name, 
      order_type, pay_condition, pay_amount, 
      tail_differ, tax_price, create_date, 
      basic_date, advance_pay_amount, po_type, 
      item_text, bill_no, environment, 
      fiscal_year, gmt_create, gmt_update
      )
    values (#{id,jdbcType=INTEGER}, #{payPurchaseOrderNo,jdbcType=VARCHAR}, #{purchaseOrderNo,jdbcType=VARCHAR}, 
      #{purchaseOrderId,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR}, #{orgId,jdbcType=BIGINT}, 
      #{orgName,jdbcType=VARCHAR}, #{proofNo,jdbcType=VARCHAR}, #{proofId,jdbcType=VARCHAR}, 
      #{deliveryNo,jdbcType=VARCHAR}, #{deliveryId,jdbcType=VARCHAR}, #{deliveryTxt,jdbcType=VARCHAR}, 
      #{goodsCode,jdbcType=VARCHAR}, #{goodsDescription,jdbcType=VARCHAR}, #{goodsProduction,jdbcType=VARCHAR}, 
      #{goodsSpecifications,jdbcType=VARCHAR}, #{supplierCode,jdbcType=VARCHAR}, #{supplierName,jdbcType=VARCHAR}, 
      #{orderType,jdbcType=VARCHAR}, #{payCondition,jdbcType=VARCHAR}, #{payAmount,jdbcType=DECIMAL}, 
      #{tailDiffer,jdbcType=DECIMAL}, #{taxPrice,jdbcType=DECIMAL}, #{createDate,jdbcType=TIMESTAMP}, 
      #{basicDate,jdbcType=TIMESTAMP}, #{advancePayAmount,jdbcType=DECIMAL}, #{poType,jdbcType=VARCHAR}, 
      #{itemText,jdbcType=VARCHAR}, #{billNo,jdbcType=VARCHAR}, #{environment,jdbcType=VARCHAR}, 
      #{fiscalYear,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmPayPurchaseOrderDetail">
    insert into srm_pay_purchase_order_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="payPurchaseOrderNo != null">
        pay_purchase_order_no,
      </if>
      <if test="purchaseOrderNo != null">
        purchase_order_no,
      </if>
      <if test="purchaseOrderId != null">
        purchase_order_id,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="proofNo != null">
        proof_no,
      </if>
      <if test="proofId != null">
        proof_id,
      </if>
      <if test="deliveryNo != null">
        delivery_no,
      </if>
      <if test="deliveryId != null">
        delivery_id,
      </if>
      <if test="deliveryTxt != null">
        delivery_txt,
      </if>
      <if test="goodsCode != null">
        goods_code,
      </if>
      <if test="goodsDescription != null">
        goods_description,
      </if>
      <if test="goodsProduction != null">
        goods_production,
      </if>
      <if test="goodsSpecifications != null">
        goods_specifications,
      </if>
      <if test="supplierCode != null">
        supplier_code,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="payCondition != null">
        pay_condition,
      </if>
      <if test="payAmount != null">
        pay_amount,
      </if>
      <if test="tailDiffer != null">
        tail_differ,
      </if>
      <if test="taxPrice != null">
        tax_price,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="basicDate != null">
        basic_date,
      </if>
      <if test="advancePayAmount != null">
        advance_pay_amount,
      </if>
      <if test="poType != null">
        po_type,
      </if>
      <if test="itemText != null">
        item_text,
      </if>
      <if test="billNo != null">
        bill_no,
      </if>
      <if test="environment != null">
        environment,
      </if>
      <if test="fiscalYear != null">
        fiscal_year,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="payPurchaseOrderNo != null">
        #{payPurchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderNo != null">
        #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderId != null">
        #{purchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="proofNo != null">
        #{proofNo,jdbcType=VARCHAR},
      </if>
      <if test="proofId != null">
        #{proofId,jdbcType=VARCHAR},
      </if>
      <if test="deliveryNo != null">
        #{deliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryId != null">
        #{deliveryId,jdbcType=VARCHAR},
      </if>
      <if test="deliveryTxt != null">
        #{deliveryTxt,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsDescription != null">
        #{goodsDescription,jdbcType=VARCHAR},
      </if>
      <if test="goodsProduction != null">
        #{goodsProduction,jdbcType=VARCHAR},
      </if>
      <if test="goodsSpecifications != null">
        #{goodsSpecifications,jdbcType=VARCHAR},
      </if>
      <if test="supplierCode != null">
        #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="payCondition != null">
        #{payCondition,jdbcType=VARCHAR},
      </if>
      <if test="payAmount != null">
        #{payAmount,jdbcType=DECIMAL},
      </if>
      <if test="tailDiffer != null">
        #{tailDiffer,jdbcType=DECIMAL},
      </if>
      <if test="taxPrice != null">
        #{taxPrice,jdbcType=DECIMAL},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="basicDate != null">
        #{basicDate,jdbcType=TIMESTAMP},
      </if>
      <if test="advancePayAmount != null">
        #{advancePayAmount,jdbcType=DECIMAL},
      </if>
      <if test="poType != null">
        #{poType,jdbcType=VARCHAR},
      </if>
      <if test="itemText != null">
        #{itemText,jdbcType=VARCHAR},
      </if>
      <if test="billNo != null">
        #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="environment != null">
        #{environment,jdbcType=VARCHAR},
      </if>
      <if test="fiscalYear != null">
        #{fiscalYear,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmPayPurchaseOrderDetailExample" resultType="java.lang.Long">
    select count(*) from srm_pay_purchase_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_pay_purchase_order_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.payPurchaseOrderNo != null">
        pay_purchase_order_no = #{record.payPurchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrderNo != null">
        purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrderId != null">
        purchase_order_id = #{record.purchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.proofNo != null">
        proof_no = #{record.proofNo,jdbcType=VARCHAR},
      </if>
      <if test="record.proofId != null">
        proof_id = #{record.proofId,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryNo != null">
        delivery_no = #{record.deliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryId != null">
        delivery_id = #{record.deliveryId,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryTxt != null">
        delivery_txt = #{record.deliveryTxt,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCode != null">
        goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsDescription != null">
        goods_description = #{record.goodsDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsProduction != null">
        goods_production = #{record.goodsProduction,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsSpecifications != null">
        goods_specifications = #{record.goodsSpecifications,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierCode != null">
        supplier_code = #{record.supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierName != null">
        supplier_name = #{record.supplierName,jdbcType=VARCHAR},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=VARCHAR},
      </if>
      <if test="record.payCondition != null">
        pay_condition = #{record.payCondition,jdbcType=VARCHAR},
      </if>
      <if test="record.payAmount != null">
        pay_amount = #{record.payAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.tailDiffer != null">
        tail_differ = #{record.tailDiffer,jdbcType=DECIMAL},
      </if>
      <if test="record.taxPrice != null">
        tax_price = #{record.taxPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.createDate != null">
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.basicDate != null">
        basic_date = #{record.basicDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.advancePayAmount != null">
        advance_pay_amount = #{record.advancePayAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.poType != null">
        po_type = #{record.poType,jdbcType=VARCHAR},
      </if>
      <if test="record.itemText != null">
        item_text = #{record.itemText,jdbcType=VARCHAR},
      </if>
      <if test="record.billNo != null">
        bill_no = #{record.billNo,jdbcType=VARCHAR},
      </if>
      <if test="record.environment != null">
        environment = #{record.environment,jdbcType=VARCHAR},
      </if>
      <if test="record.fiscalYear != null">
        fiscal_year = #{record.fiscalYear,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_pay_purchase_order_detail
    set id = #{record.id,jdbcType=INTEGER},
      pay_purchase_order_no = #{record.payPurchaseOrderNo,jdbcType=VARCHAR},
      purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      purchase_order_id = #{record.purchaseOrderId,jdbcType=VARCHAR},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      org_id = #{record.orgId,jdbcType=BIGINT},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      proof_no = #{record.proofNo,jdbcType=VARCHAR},
      proof_id = #{record.proofId,jdbcType=VARCHAR},
      delivery_no = #{record.deliveryNo,jdbcType=VARCHAR},
      delivery_id = #{record.deliveryId,jdbcType=VARCHAR},
      delivery_txt = #{record.deliveryTxt,jdbcType=VARCHAR},
      goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      goods_description = #{record.goodsDescription,jdbcType=VARCHAR},
      goods_production = #{record.goodsProduction,jdbcType=VARCHAR},
      goods_specifications = #{record.goodsSpecifications,jdbcType=VARCHAR},
      supplier_code = #{record.supplierCode,jdbcType=VARCHAR},
      supplier_name = #{record.supplierName,jdbcType=VARCHAR},
      order_type = #{record.orderType,jdbcType=VARCHAR},
      pay_condition = #{record.payCondition,jdbcType=VARCHAR},
      pay_amount = #{record.payAmount,jdbcType=DECIMAL},
      tail_differ = #{record.tailDiffer,jdbcType=DECIMAL},
      tax_price = #{record.taxPrice,jdbcType=DECIMAL},
      create_date = #{record.createDate,jdbcType=TIMESTAMP},
      basic_date = #{record.basicDate,jdbcType=TIMESTAMP},
      advance_pay_amount = #{record.advancePayAmount,jdbcType=DECIMAL},
      po_type = #{record.poType,jdbcType=VARCHAR},
      item_text = #{record.itemText,jdbcType=VARCHAR},
      bill_no = #{record.billNo,jdbcType=VARCHAR},
      environment = #{record.environment,jdbcType=VARCHAR},
      fiscal_year = #{record.fiscalYear,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmPayPurchaseOrderDetail">
    update srm_pay_purchase_order_detail
    <set>
      <if test="payPurchaseOrderNo != null">
        pay_purchase_order_no = #{payPurchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderNo != null">
        purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderId != null">
        purchase_order_id = #{purchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="proofNo != null">
        proof_no = #{proofNo,jdbcType=VARCHAR},
      </if>
      <if test="proofId != null">
        proof_id = #{proofId,jdbcType=VARCHAR},
      </if>
      <if test="deliveryNo != null">
        delivery_no = #{deliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryId != null">
        delivery_id = #{deliveryId,jdbcType=VARCHAR},
      </if>
      <if test="deliveryTxt != null">
        delivery_txt = #{deliveryTxt,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        goods_code = #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsDescription != null">
        goods_description = #{goodsDescription,jdbcType=VARCHAR},
      </if>
      <if test="goodsProduction != null">
        goods_production = #{goodsProduction,jdbcType=VARCHAR},
      </if>
      <if test="goodsSpecifications != null">
        goods_specifications = #{goodsSpecifications,jdbcType=VARCHAR},
      </if>
      <if test="supplierCode != null">
        supplier_code = #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="payCondition != null">
        pay_condition = #{payCondition,jdbcType=VARCHAR},
      </if>
      <if test="payAmount != null">
        pay_amount = #{payAmount,jdbcType=DECIMAL},
      </if>
      <if test="tailDiffer != null">
        tail_differ = #{tailDiffer,jdbcType=DECIMAL},
      </if>
      <if test="taxPrice != null">
        tax_price = #{taxPrice,jdbcType=DECIMAL},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="basicDate != null">
        basic_date = #{basicDate,jdbcType=TIMESTAMP},
      </if>
      <if test="advancePayAmount != null">
        advance_pay_amount = #{advancePayAmount,jdbcType=DECIMAL},
      </if>
      <if test="poType != null">
        po_type = #{poType,jdbcType=VARCHAR},
      </if>
      <if test="itemText != null">
        item_text = #{itemText,jdbcType=VARCHAR},
      </if>
      <if test="billNo != null">
        bill_no = #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="environment != null">
        environment = #{environment,jdbcType=VARCHAR},
      </if>
      <if test="fiscalYear != null">
        fiscal_year = #{fiscalYear,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmPayPurchaseOrderDetail">
    update srm_pay_purchase_order_detail
    set pay_purchase_order_no = #{payPurchaseOrderNo,jdbcType=VARCHAR},
      purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      purchase_order_id = #{purchaseOrderId,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      org_id = #{orgId,jdbcType=BIGINT},
      org_name = #{orgName,jdbcType=VARCHAR},
      proof_no = #{proofNo,jdbcType=VARCHAR},
      proof_id = #{proofId,jdbcType=VARCHAR},
      delivery_no = #{deliveryNo,jdbcType=VARCHAR},
      delivery_id = #{deliveryId,jdbcType=VARCHAR},
      delivery_txt = #{deliveryTxt,jdbcType=VARCHAR},
      goods_code = #{goodsCode,jdbcType=VARCHAR},
      goods_description = #{goodsDescription,jdbcType=VARCHAR},
      goods_production = #{goodsProduction,jdbcType=VARCHAR},
      goods_specifications = #{goodsSpecifications,jdbcType=VARCHAR},
      supplier_code = #{supplierCode,jdbcType=VARCHAR},
      supplier_name = #{supplierName,jdbcType=VARCHAR},
      order_type = #{orderType,jdbcType=VARCHAR},
      pay_condition = #{payCondition,jdbcType=VARCHAR},
      pay_amount = #{payAmount,jdbcType=DECIMAL},
      tail_differ = #{tailDiffer,jdbcType=DECIMAL},
      tax_price = #{taxPrice,jdbcType=DECIMAL},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      basic_date = #{basicDate,jdbcType=TIMESTAMP},
      advance_pay_amount = #{advancePayAmount,jdbcType=DECIMAL},
      po_type = #{poType,jdbcType=VARCHAR},
      item_text = #{itemText,jdbcType=VARCHAR},
      bill_no = #{billNo,jdbcType=VARCHAR},
      environment = #{environment,jdbcType=VARCHAR},
      fiscal_year = #{fiscalYear,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>