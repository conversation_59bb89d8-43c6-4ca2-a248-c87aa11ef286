<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SettleCheckAccountDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SettleCheckAccountDetail">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="delivery_no" jdbcType="VARCHAR" property="deliveryNo" />
    <result column="delivery_id" jdbcType="VARCHAR" property="deliveryId" />
    <result column="delivery_date" jdbcType="TIMESTAMP" property="deliveryDate" />
    <result column="delivery_year" jdbcType="VARCHAR" property="deliveryYear" />
    <result column="delivery_key" jdbcType="VARCHAR" property="deliveryKey" />
    <result column="sdelivery_no" jdbcType="VARCHAR" property="sdeliveryNo" />
    <result column="sdelivery_id" jdbcType="VARCHAR" property="sdeliveryId" />
    <result column="sdelivery_year" jdbcType="VARCHAR" property="sdeliveryYear" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="purchase_order_no" jdbcType="VARCHAR" property="purchaseOrderNo" />
    <result column="purchase_order_id" jdbcType="VARCHAR" property="purchaseOrderId" />
    <result column="purchase_org_no" jdbcType="VARCHAR" property="purchaseOrgNo" />
    <result column="purchase_org_name" jdbcType="VARCHAR" property="purchaseOrgName" />
    <result column="purchase_person" jdbcType="VARCHAR" property="purchasePerson" />
    <result column="purchase_unit" jdbcType="VARCHAR" property="purchaseUnit" />
    <result column="base_unit" jdbcType="VARCHAR" property="baseUnit" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="org_id" jdbcType="VARCHAR" property="orgId" />
    <result column="compny_code" jdbcType="VARCHAR" property="compnyCode" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="supplier_no" jdbcType="VARCHAR" property="supplierNo" />
    <result column="supplier_salesman_code" jdbcType="VARCHAR" property="supplierSalesmanCode" />
    <result column="srm_group" jdbcType="VARCHAR" property="srmGroup" />
    <result column="event_type" jdbcType="VARCHAR" property="eventType" />
    <result column="delivery_count" jdbcType="DECIMAL" property="deliveryCount" />
    <result column="dno_tax_amount" jdbcType="DECIMAL" property="dnoTaxAmount" />
    <result column="dhave_tax_amount" jdbcType="DECIMAL" property="dhaveTaxAmount" />
    <result column="dhave_tax_price" jdbcType="DECIMAL" property="dhaveTaxPrice" />
    <result column="dno_tax_price" jdbcType="DECIMAL" property="dnoTaxPrice" />
    <result column="may_settle_count" jdbcType="DECIMAL" property="maySettleCount" />
    <result column="warehouse" jdbcType="VARCHAR" property="warehouse" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="price_unit" jdbcType="VARCHAR" property="priceUnit" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="no_tax_amount" jdbcType="DECIMAL" property="noTaxAmount" />
    <result column="have_tax_amount" jdbcType="DECIMAL" property="haveTaxAmount" />
    <result column="purchase_tax_rate" jdbcType="VARCHAR" property="purchaseTaxRate" />
    <result column="tax_rate" jdbcType="VARCHAR" property="taxRate" />
    <result column="tax_differ" jdbcType="VARCHAR" property="taxDiffer" />
    <result column="no_tax_price" jdbcType="DECIMAL" property="noTaxPrice" />
    <result column="have_tax_price" jdbcType="DECIMAL" property="haveTaxPrice" />
    <result column="pay_code" jdbcType="VARCHAR" property="payCode" />
    <result column="move_type" jdbcType="INTEGER" property="moveType" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="check_status" jdbcType="INTEGER" property="checkStatus" />
    <result column="check_time" jdbcType="TIMESTAMP" property="checkTime" />
    <result column="able_type" jdbcType="INTEGER" property="ableType" />
    <result column="invoice_no" jdbcType="VARCHAR" property="invoiceNo" />
    <result column="refuse_quantity" jdbcType="DECIMAL" property="refuseQuantity" />
    <result column="refuse_reason" jdbcType="VARCHAR" property="refuseReason" />
    <result column="send_no" jdbcType="VARCHAR" property="sendNo" />
    <result column="send_id" jdbcType="INTEGER" property="sendId" />
    <result column="insure_no" jdbcType="VARCHAR" property="insureNo" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, delivery_no, delivery_id, delivery_date, delivery_year, delivery_key, sdelivery_no, 
    sdelivery_id, sdelivery_year, business_type, purchase_order_no, purchase_order_id, 
    purchase_org_no, purchase_org_name, purchase_person, purchase_unit, base_unit, create_date, 
    org_id, compny_code, org_name, supplier_no, supplier_salesman_code, srm_group, event_type, 
    delivery_count, dno_tax_amount, dhave_tax_amount, dhave_tax_price, dno_tax_price, 
    may_settle_count, warehouse, warehouse_name, goods_code, price_unit, batch_no, no_tax_amount, 
    have_tax_amount, purchase_tax_rate, tax_rate, tax_differ, no_tax_price, have_tax_price, 
    pay_code, move_type, order_type, check_status, check_time, able_type, invoice_no, 
    refuse_quantity, refuse_reason, send_no, send_id, insure_no, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SettleCheckAccountDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from settle_check_account_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from settle_check_account_detail
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from settle_check_account_detail
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SettleCheckAccountDetailExample">
    delete from settle_check_account_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SettleCheckAccountDetail">
    insert into settle_check_account_detail (id, delivery_no, delivery_id, 
      delivery_date, delivery_year, delivery_key, 
      sdelivery_no, sdelivery_id, sdelivery_year, 
      business_type, purchase_order_no, purchase_order_id, 
      purchase_org_no, purchase_org_name, purchase_person, 
      purchase_unit, base_unit, create_date, 
      org_id, compny_code, org_name, 
      supplier_no, supplier_salesman_code, srm_group, 
      event_type, delivery_count, dno_tax_amount, 
      dhave_tax_amount, dhave_tax_price, dno_tax_price, 
      may_settle_count, warehouse, warehouse_name, 
      goods_code, price_unit, batch_no, 
      no_tax_amount, have_tax_amount, purchase_tax_rate, 
      tax_rate, tax_differ, no_tax_price, 
      have_tax_price, pay_code, move_type, 
      order_type, check_status, check_time, 
      able_type, invoice_no, refuse_quantity, 
      refuse_reason, send_no, send_id, 
      insure_no, gmt_create, gmt_update
      )
    values (#{id,jdbcType=INTEGER}, #{deliveryNo,jdbcType=VARCHAR}, #{deliveryId,jdbcType=VARCHAR}, 
      #{deliveryDate,jdbcType=TIMESTAMP}, #{deliveryYear,jdbcType=VARCHAR}, #{deliveryKey,jdbcType=VARCHAR}, 
      #{sdeliveryNo,jdbcType=VARCHAR}, #{sdeliveryId,jdbcType=VARCHAR}, #{sdeliveryYear,jdbcType=VARCHAR}, 
      #{businessType,jdbcType=VARCHAR}, #{purchaseOrderNo,jdbcType=VARCHAR}, #{purchaseOrderId,jdbcType=VARCHAR}, 
      #{purchaseOrgNo,jdbcType=VARCHAR}, #{purchaseOrgName,jdbcType=VARCHAR}, #{purchasePerson,jdbcType=VARCHAR}, 
      #{purchaseUnit,jdbcType=VARCHAR}, #{baseUnit,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}, 
      #{orgId,jdbcType=VARCHAR}, #{compnyCode,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR}, 
      #{supplierNo,jdbcType=VARCHAR}, #{supplierSalesmanCode,jdbcType=VARCHAR}, #{srmGroup,jdbcType=VARCHAR}, 
      #{eventType,jdbcType=VARCHAR}, #{deliveryCount,jdbcType=DECIMAL}, #{dnoTaxAmount,jdbcType=DECIMAL}, 
      #{dhaveTaxAmount,jdbcType=DECIMAL}, #{dhaveTaxPrice,jdbcType=DECIMAL}, #{dnoTaxPrice,jdbcType=DECIMAL}, 
      #{maySettleCount,jdbcType=DECIMAL}, #{warehouse,jdbcType=VARCHAR}, #{warehouseName,jdbcType=VARCHAR}, 
      #{goodsCode,jdbcType=VARCHAR}, #{priceUnit,jdbcType=VARCHAR}, #{batchNo,jdbcType=VARCHAR}, 
      #{noTaxAmount,jdbcType=DECIMAL}, #{haveTaxAmount,jdbcType=DECIMAL}, #{purchaseTaxRate,jdbcType=VARCHAR}, 
      #{taxRate,jdbcType=VARCHAR}, #{taxDiffer,jdbcType=VARCHAR}, #{noTaxPrice,jdbcType=DECIMAL}, 
      #{haveTaxPrice,jdbcType=DECIMAL}, #{payCode,jdbcType=VARCHAR}, #{moveType,jdbcType=INTEGER}, 
      #{orderType,jdbcType=INTEGER}, #{checkStatus,jdbcType=INTEGER}, #{checkTime,jdbcType=TIMESTAMP}, 
      #{ableType,jdbcType=INTEGER}, #{invoiceNo,jdbcType=VARCHAR}, #{refuseQuantity,jdbcType=DECIMAL}, 
      #{refuseReason,jdbcType=VARCHAR}, #{sendNo,jdbcType=VARCHAR}, #{sendId,jdbcType=INTEGER}, 
      #{insureNo,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SettleCheckAccountDetail">
    insert into settle_check_account_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deliveryNo != null">
        delivery_no,
      </if>
      <if test="deliveryId != null">
        delivery_id,
      </if>
      <if test="deliveryDate != null">
        delivery_date,
      </if>
      <if test="deliveryYear != null">
        delivery_year,
      </if>
      <if test="deliveryKey != null">
        delivery_key,
      </if>
      <if test="sdeliveryNo != null">
        sdelivery_no,
      </if>
      <if test="sdeliveryId != null">
        sdelivery_id,
      </if>
      <if test="sdeliveryYear != null">
        sdelivery_year,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="purchaseOrderNo != null">
        purchase_order_no,
      </if>
      <if test="purchaseOrderId != null">
        purchase_order_id,
      </if>
      <if test="purchaseOrgNo != null">
        purchase_org_no,
      </if>
      <if test="purchaseOrgName != null">
        purchase_org_name,
      </if>
      <if test="purchasePerson != null">
        purchase_person,
      </if>
      <if test="purchaseUnit != null">
        purchase_unit,
      </if>
      <if test="baseUnit != null">
        base_unit,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="compnyCode != null">
        compny_code,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="supplierNo != null">
        supplier_no,
      </if>
      <if test="supplierSalesmanCode != null">
        supplier_salesman_code,
      </if>
      <if test="srmGroup != null">
        srm_group,
      </if>
      <if test="eventType != null">
        event_type,
      </if>
      <if test="deliveryCount != null">
        delivery_count,
      </if>
      <if test="dnoTaxAmount != null">
        dno_tax_amount,
      </if>
      <if test="dhaveTaxAmount != null">
        dhave_tax_amount,
      </if>
      <if test="dhaveTaxPrice != null">
        dhave_tax_price,
      </if>
      <if test="dnoTaxPrice != null">
        dno_tax_price,
      </if>
      <if test="maySettleCount != null">
        may_settle_count,
      </if>
      <if test="warehouse != null">
        warehouse,
      </if>
      <if test="warehouseName != null">
        warehouse_name,
      </if>
      <if test="goodsCode != null">
        goods_code,
      </if>
      <if test="priceUnit != null">
        price_unit,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount,
      </if>
      <if test="haveTaxAmount != null">
        have_tax_amount,
      </if>
      <if test="purchaseTaxRate != null">
        purchase_tax_rate,
      </if>
      <if test="taxRate != null">
        tax_rate,
      </if>
      <if test="taxDiffer != null">
        tax_differ,
      </if>
      <if test="noTaxPrice != null">
        no_tax_price,
      </if>
      <if test="haveTaxPrice != null">
        have_tax_price,
      </if>
      <if test="payCode != null">
        pay_code,
      </if>
      <if test="moveType != null">
        move_type,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="checkStatus != null">
        check_status,
      </if>
      <if test="checkTime != null">
        check_time,
      </if>
      <if test="ableType != null">
        able_type,
      </if>
      <if test="invoiceNo != null">
        invoice_no,
      </if>
      <if test="refuseQuantity != null">
        refuse_quantity,
      </if>
      <if test="refuseReason != null">
        refuse_reason,
      </if>
      <if test="sendNo != null">
        send_no,
      </if>
      <if test="sendId != null">
        send_id,
      </if>
      <if test="insureNo != null">
        insure_no,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deliveryNo != null">
        #{deliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryId != null">
        #{deliveryId,jdbcType=VARCHAR},
      </if>
      <if test="deliveryDate != null">
        #{deliveryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryYear != null">
        #{deliveryYear,jdbcType=VARCHAR},
      </if>
      <if test="deliveryKey != null">
        #{deliveryKey,jdbcType=VARCHAR},
      </if>
      <if test="sdeliveryNo != null">
        #{sdeliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="sdeliveryId != null">
        #{sdeliveryId,jdbcType=VARCHAR},
      </if>
      <if test="sdeliveryYear != null">
        #{sdeliveryYear,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderNo != null">
        #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderId != null">
        #{purchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrgNo != null">
        #{purchaseOrgNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrgName != null">
        #{purchaseOrgName,jdbcType=VARCHAR},
      </if>
      <if test="purchasePerson != null">
        #{purchasePerson,jdbcType=VARCHAR},
      </if>
      <if test="purchaseUnit != null">
        #{purchaseUnit,jdbcType=VARCHAR},
      </if>
      <if test="baseUnit != null">
        #{baseUnit,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="compnyCode != null">
        #{compnyCode,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null">
        #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierSalesmanCode != null">
        #{supplierSalesmanCode,jdbcType=VARCHAR},
      </if>
      <if test="srmGroup != null">
        #{srmGroup,jdbcType=VARCHAR},
      </if>
      <if test="eventType != null">
        #{eventType,jdbcType=VARCHAR},
      </if>
      <if test="deliveryCount != null">
        #{deliveryCount,jdbcType=DECIMAL},
      </if>
      <if test="dnoTaxAmount != null">
        #{dnoTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="dhaveTaxAmount != null">
        #{dhaveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="dhaveTaxPrice != null">
        #{dhaveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="dnoTaxPrice != null">
        #{dnoTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="maySettleCount != null">
        #{maySettleCount,jdbcType=DECIMAL},
      </if>
      <if test="warehouse != null">
        #{warehouse,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="priceUnit != null">
        #{priceUnit,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="noTaxAmount != null">
        #{noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="haveTaxAmount != null">
        #{haveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="purchaseTaxRate != null">
        #{purchaseTaxRate,jdbcType=VARCHAR},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=VARCHAR},
      </if>
      <if test="taxDiffer != null">
        #{taxDiffer,jdbcType=VARCHAR},
      </if>
      <if test="noTaxPrice != null">
        #{noTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="haveTaxPrice != null">
        #{haveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="payCode != null">
        #{payCode,jdbcType=VARCHAR},
      </if>
      <if test="moveType != null">
        #{moveType,jdbcType=INTEGER},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="checkStatus != null">
        #{checkStatus,jdbcType=INTEGER},
      </if>
      <if test="checkTime != null">
        #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ableType != null">
        #{ableType,jdbcType=INTEGER},
      </if>
      <if test="invoiceNo != null">
        #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="refuseQuantity != null">
        #{refuseQuantity,jdbcType=DECIMAL},
      </if>
      <if test="refuseReason != null">
        #{refuseReason,jdbcType=VARCHAR},
      </if>
      <if test="sendNo != null">
        #{sendNo,jdbcType=VARCHAR},
      </if>
      <if test="sendId != null">
        #{sendId,jdbcType=INTEGER},
      </if>
      <if test="insureNo != null">
        #{insureNo,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SettleCheckAccountDetailExample" resultType="java.lang.Long">
    select count(*) from settle_check_account_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update settle_check_account_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.deliveryNo != null">
        delivery_no = #{record.deliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryId != null">
        delivery_id = #{record.deliveryId,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryDate != null">
        delivery_date = #{record.deliveryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deliveryYear != null">
        delivery_year = #{record.deliveryYear,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryKey != null">
        delivery_key = #{record.deliveryKey,jdbcType=VARCHAR},
      </if>
      <if test="record.sdeliveryNo != null">
        sdelivery_no = #{record.sdeliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="record.sdeliveryId != null">
        sdelivery_id = #{record.sdeliveryId,jdbcType=VARCHAR},
      </if>
      <if test="record.sdeliveryYear != null">
        sdelivery_year = #{record.sdeliveryYear,jdbcType=VARCHAR},
      </if>
      <if test="record.businessType != null">
        business_type = #{record.businessType,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrderNo != null">
        purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrderId != null">
        purchase_order_id = #{record.purchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrgNo != null">
        purchase_org_no = #{record.purchaseOrgNo,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrgName != null">
        purchase_org_name = #{record.purchaseOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.purchasePerson != null">
        purchase_person = #{record.purchasePerson,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseUnit != null">
        purchase_unit = #{record.purchaseUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.baseUnit != null">
        base_unit = #{record.baseUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null">
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=VARCHAR},
      </if>
      <if test="record.compnyCode != null">
        compny_code = #{record.compnyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierNo != null">
        supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierSalesmanCode != null">
        supplier_salesman_code = #{record.supplierSalesmanCode,jdbcType=VARCHAR},
      </if>
      <if test="record.srmGroup != null">
        srm_group = #{record.srmGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.eventType != null">
        event_type = #{record.eventType,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryCount != null">
        delivery_count = #{record.deliveryCount,jdbcType=DECIMAL},
      </if>
      <if test="record.dnoTaxAmount != null">
        dno_tax_amount = #{record.dnoTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.dhaveTaxAmount != null">
        dhave_tax_amount = #{record.dhaveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.dhaveTaxPrice != null">
        dhave_tax_price = #{record.dhaveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.dnoTaxPrice != null">
        dno_tax_price = #{record.dnoTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.maySettleCount != null">
        may_settle_count = #{record.maySettleCount,jdbcType=DECIMAL},
      </if>
      <if test="record.warehouse != null">
        warehouse = #{record.warehouse,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseName != null">
        warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCode != null">
        goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.priceUnit != null">
        price_unit = #{record.priceUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.noTaxAmount != null">
        no_tax_amount = #{record.noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.haveTaxAmount != null">
        have_tax_amount = #{record.haveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.purchaseTaxRate != null">
        purchase_tax_rate = #{record.purchaseTaxRate,jdbcType=VARCHAR},
      </if>
      <if test="record.taxRate != null">
        tax_rate = #{record.taxRate,jdbcType=VARCHAR},
      </if>
      <if test="record.taxDiffer != null">
        tax_differ = #{record.taxDiffer,jdbcType=VARCHAR},
      </if>
      <if test="record.noTaxPrice != null">
        no_tax_price = #{record.noTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.haveTaxPrice != null">
        have_tax_price = #{record.haveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.payCode != null">
        pay_code = #{record.payCode,jdbcType=VARCHAR},
      </if>
      <if test="record.moveType != null">
        move_type = #{record.moveType,jdbcType=INTEGER},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=INTEGER},
      </if>
      <if test="record.checkStatus != null">
        check_status = #{record.checkStatus,jdbcType=INTEGER},
      </if>
      <if test="record.checkTime != null">
        check_time = #{record.checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ableType != null">
        able_type = #{record.ableType,jdbcType=INTEGER},
      </if>
      <if test="record.invoiceNo != null">
        invoice_no = #{record.invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="record.refuseQuantity != null">
        refuse_quantity = #{record.refuseQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.refuseReason != null">
        refuse_reason = #{record.refuseReason,jdbcType=VARCHAR},
      </if>
      <if test="record.sendNo != null">
        send_no = #{record.sendNo,jdbcType=VARCHAR},
      </if>
      <if test="record.sendId != null">
        send_id = #{record.sendId,jdbcType=INTEGER},
      </if>
      <if test="record.insureNo != null">
        insure_no = #{record.insureNo,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update settle_check_account_detail
    set id = #{record.id,jdbcType=INTEGER},
      delivery_no = #{record.deliveryNo,jdbcType=VARCHAR},
      delivery_id = #{record.deliveryId,jdbcType=VARCHAR},
      delivery_date = #{record.deliveryDate,jdbcType=TIMESTAMP},
      delivery_year = #{record.deliveryYear,jdbcType=VARCHAR},
      delivery_key = #{record.deliveryKey,jdbcType=VARCHAR},
      sdelivery_no = #{record.sdeliveryNo,jdbcType=VARCHAR},
      sdelivery_id = #{record.sdeliveryId,jdbcType=VARCHAR},
      sdelivery_year = #{record.sdeliveryYear,jdbcType=VARCHAR},
      business_type = #{record.businessType,jdbcType=VARCHAR},
      purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      purchase_order_id = #{record.purchaseOrderId,jdbcType=VARCHAR},
      purchase_org_no = #{record.purchaseOrgNo,jdbcType=VARCHAR},
      purchase_org_name = #{record.purchaseOrgName,jdbcType=VARCHAR},
      purchase_person = #{record.purchasePerson,jdbcType=VARCHAR},
      purchase_unit = #{record.purchaseUnit,jdbcType=VARCHAR},
      base_unit = #{record.baseUnit,jdbcType=VARCHAR},
      create_date = #{record.createDate,jdbcType=TIMESTAMP},
      org_id = #{record.orgId,jdbcType=VARCHAR},
      compny_code = #{record.compnyCode,jdbcType=VARCHAR},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      supplier_salesman_code = #{record.supplierSalesmanCode,jdbcType=VARCHAR},
      srm_group = #{record.srmGroup,jdbcType=VARCHAR},
      event_type = #{record.eventType,jdbcType=VARCHAR},
      delivery_count = #{record.deliveryCount,jdbcType=DECIMAL},
      dno_tax_amount = #{record.dnoTaxAmount,jdbcType=DECIMAL},
      dhave_tax_amount = #{record.dhaveTaxAmount,jdbcType=DECIMAL},
      dhave_tax_price = #{record.dhaveTaxPrice,jdbcType=DECIMAL},
      dno_tax_price = #{record.dnoTaxPrice,jdbcType=DECIMAL},
      may_settle_count = #{record.maySettleCount,jdbcType=DECIMAL},
      warehouse = #{record.warehouse,jdbcType=VARCHAR},
      warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      price_unit = #{record.priceUnit,jdbcType=VARCHAR},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      no_tax_amount = #{record.noTaxAmount,jdbcType=DECIMAL},
      have_tax_amount = #{record.haveTaxAmount,jdbcType=DECIMAL},
      purchase_tax_rate = #{record.purchaseTaxRate,jdbcType=VARCHAR},
      tax_rate = #{record.taxRate,jdbcType=VARCHAR},
      tax_differ = #{record.taxDiffer,jdbcType=VARCHAR},
      no_tax_price = #{record.noTaxPrice,jdbcType=DECIMAL},
      have_tax_price = #{record.haveTaxPrice,jdbcType=DECIMAL},
      pay_code = #{record.payCode,jdbcType=VARCHAR},
      move_type = #{record.moveType,jdbcType=INTEGER},
      order_type = #{record.orderType,jdbcType=INTEGER},
      check_status = #{record.checkStatus,jdbcType=INTEGER},
      check_time = #{record.checkTime,jdbcType=TIMESTAMP},
      able_type = #{record.ableType,jdbcType=INTEGER},
      invoice_no = #{record.invoiceNo,jdbcType=VARCHAR},
      refuse_quantity = #{record.refuseQuantity,jdbcType=DECIMAL},
      refuse_reason = #{record.refuseReason,jdbcType=VARCHAR},
      send_no = #{record.sendNo,jdbcType=VARCHAR},
      send_id = #{record.sendId,jdbcType=INTEGER},
      insure_no = #{record.insureNo,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SettleCheckAccountDetail">
    update settle_check_account_detail
    <set>
      <if test="deliveryNo != null">
        delivery_no = #{deliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryId != null">
        delivery_id = #{deliveryId,jdbcType=VARCHAR},
      </if>
      <if test="deliveryDate != null">
        delivery_date = #{deliveryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="deliveryYear != null">
        delivery_year = #{deliveryYear,jdbcType=VARCHAR},
      </if>
      <if test="deliveryKey != null">
        delivery_key = #{deliveryKey,jdbcType=VARCHAR},
      </if>
      <if test="sdeliveryNo != null">
        sdelivery_no = #{sdeliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="sdeliveryId != null">
        sdelivery_id = #{sdeliveryId,jdbcType=VARCHAR},
      </if>
      <if test="sdeliveryYear != null">
        sdelivery_year = #{sdeliveryYear,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderNo != null">
        purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderId != null">
        purchase_order_id = #{purchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrgNo != null">
        purchase_org_no = #{purchaseOrgNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrgName != null">
        purchase_org_name = #{purchaseOrgName,jdbcType=VARCHAR},
      </if>
      <if test="purchasePerson != null">
        purchase_person = #{purchasePerson,jdbcType=VARCHAR},
      </if>
      <if test="purchaseUnit != null">
        purchase_unit = #{purchaseUnit,jdbcType=VARCHAR},
      </if>
      <if test="baseUnit != null">
        base_unit = #{baseUnit,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=VARCHAR},
      </if>
      <if test="compnyCode != null">
        compny_code = #{compnyCode,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null">
        supplier_no = #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierSalesmanCode != null">
        supplier_salesman_code = #{supplierSalesmanCode,jdbcType=VARCHAR},
      </if>
      <if test="srmGroup != null">
        srm_group = #{srmGroup,jdbcType=VARCHAR},
      </if>
      <if test="eventType != null">
        event_type = #{eventType,jdbcType=VARCHAR},
      </if>
      <if test="deliveryCount != null">
        delivery_count = #{deliveryCount,jdbcType=DECIMAL},
      </if>
      <if test="dnoTaxAmount != null">
        dno_tax_amount = #{dnoTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="dhaveTaxAmount != null">
        dhave_tax_amount = #{dhaveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="dhaveTaxPrice != null">
        dhave_tax_price = #{dhaveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="dnoTaxPrice != null">
        dno_tax_price = #{dnoTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="maySettleCount != null">
        may_settle_count = #{maySettleCount,jdbcType=DECIMAL},
      </if>
      <if test="warehouse != null">
        warehouse = #{warehouse,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        goods_code = #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="priceUnit != null">
        price_unit = #{priceUnit,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount = #{noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="haveTaxAmount != null">
        have_tax_amount = #{haveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="purchaseTaxRate != null">
        purchase_tax_rate = #{purchaseTaxRate,jdbcType=VARCHAR},
      </if>
      <if test="taxRate != null">
        tax_rate = #{taxRate,jdbcType=VARCHAR},
      </if>
      <if test="taxDiffer != null">
        tax_differ = #{taxDiffer,jdbcType=VARCHAR},
      </if>
      <if test="noTaxPrice != null">
        no_tax_price = #{noTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="haveTaxPrice != null">
        have_tax_price = #{haveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="payCode != null">
        pay_code = #{payCode,jdbcType=VARCHAR},
      </if>
      <if test="moveType != null">
        move_type = #{moveType,jdbcType=INTEGER},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="checkStatus != null">
        check_status = #{checkStatus,jdbcType=INTEGER},
      </if>
      <if test="checkTime != null">
        check_time = #{checkTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ableType != null">
        able_type = #{ableType,jdbcType=INTEGER},
      </if>
      <if test="invoiceNo != null">
        invoice_no = #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="refuseQuantity != null">
        refuse_quantity = #{refuseQuantity,jdbcType=DECIMAL},
      </if>
      <if test="refuseReason != null">
        refuse_reason = #{refuseReason,jdbcType=VARCHAR},
      </if>
      <if test="sendNo != null">
        send_no = #{sendNo,jdbcType=VARCHAR},
      </if>
      <if test="sendId != null">
        send_id = #{sendId,jdbcType=INTEGER},
      </if>
      <if test="insureNo != null">
        insure_no = #{insureNo,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SettleCheckAccountDetail">
    update settle_check_account_detail
    set delivery_no = #{deliveryNo,jdbcType=VARCHAR},
      delivery_id = #{deliveryId,jdbcType=VARCHAR},
      delivery_date = #{deliveryDate,jdbcType=TIMESTAMP},
      delivery_year = #{deliveryYear,jdbcType=VARCHAR},
      delivery_key = #{deliveryKey,jdbcType=VARCHAR},
      sdelivery_no = #{sdeliveryNo,jdbcType=VARCHAR},
      sdelivery_id = #{sdeliveryId,jdbcType=VARCHAR},
      sdelivery_year = #{sdeliveryYear,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=VARCHAR},
      purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      purchase_order_id = #{purchaseOrderId,jdbcType=VARCHAR},
      purchase_org_no = #{purchaseOrgNo,jdbcType=VARCHAR},
      purchase_org_name = #{purchaseOrgName,jdbcType=VARCHAR},
      purchase_person = #{purchasePerson,jdbcType=VARCHAR},
      purchase_unit = #{purchaseUnit,jdbcType=VARCHAR},
      base_unit = #{baseUnit,jdbcType=VARCHAR},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      org_id = #{orgId,jdbcType=VARCHAR},
      compny_code = #{compnyCode,jdbcType=VARCHAR},
      org_name = #{orgName,jdbcType=VARCHAR},
      supplier_no = #{supplierNo,jdbcType=VARCHAR},
      supplier_salesman_code = #{supplierSalesmanCode,jdbcType=VARCHAR},
      srm_group = #{srmGroup,jdbcType=VARCHAR},
      event_type = #{eventType,jdbcType=VARCHAR},
      delivery_count = #{deliveryCount,jdbcType=DECIMAL},
      dno_tax_amount = #{dnoTaxAmount,jdbcType=DECIMAL},
      dhave_tax_amount = #{dhaveTaxAmount,jdbcType=DECIMAL},
      dhave_tax_price = #{dhaveTaxPrice,jdbcType=DECIMAL},
      dno_tax_price = #{dnoTaxPrice,jdbcType=DECIMAL},
      may_settle_count = #{maySettleCount,jdbcType=DECIMAL},
      warehouse = #{warehouse,jdbcType=VARCHAR},
      warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      goods_code = #{goodsCode,jdbcType=VARCHAR},
      price_unit = #{priceUnit,jdbcType=VARCHAR},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      no_tax_amount = #{noTaxAmount,jdbcType=DECIMAL},
      have_tax_amount = #{haveTaxAmount,jdbcType=DECIMAL},
      purchase_tax_rate = #{purchaseTaxRate,jdbcType=VARCHAR},
      tax_rate = #{taxRate,jdbcType=VARCHAR},
      tax_differ = #{taxDiffer,jdbcType=VARCHAR},
      no_tax_price = #{noTaxPrice,jdbcType=DECIMAL},
      have_tax_price = #{haveTaxPrice,jdbcType=DECIMAL},
      pay_code = #{payCode,jdbcType=VARCHAR},
      move_type = #{moveType,jdbcType=INTEGER},
      order_type = #{orderType,jdbcType=INTEGER},
      check_status = #{checkStatus,jdbcType=INTEGER},
      check_time = #{checkTime,jdbcType=TIMESTAMP},
      able_type = #{ableType,jdbcType=INTEGER},
      invoice_no = #{invoiceNo,jdbcType=VARCHAR},
      refuse_quantity = #{refuseQuantity,jdbcType=DECIMAL},
      refuse_reason = #{refuseReason,jdbcType=VARCHAR},
      send_no = #{sendNo,jdbcType=VARCHAR},
      send_id = #{sendId,jdbcType=INTEGER},
      insure_no = #{insureNo,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>