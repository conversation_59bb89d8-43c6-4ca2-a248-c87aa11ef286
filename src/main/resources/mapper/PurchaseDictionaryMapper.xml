<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.PurchaseDictionaryMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.PurchaseDictionary">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="purchase_channel" jdbcType="TINYINT" property="purchaseChannel" />
    <result column="dict_type" jdbcType="VARCHAR" property="dictType" />
    <result column="dict_type_name" jdbcType="VARCHAR" property="dictTypeName" />
    <result column="dict_key" jdbcType="VARCHAR" property="dictKey" />
    <result column="dict_value" jdbcType="VARCHAR" property="dictValue" />
    <result column="dict_desc" jdbcType="VARCHAR" property="dictDesc" />
    <result column="dict_days" jdbcType="INTEGER" property="dictDays" />
    <result column="dict_extend" jdbcType="VARCHAR" property="dictExtend" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="operation_name" jdbcType="VARCHAR" property="operationName" />
    <result column="operation_by" jdbcType="BIGINT" property="operationBy" />
    <result column="use_bdp" jdbcType="TINYINT" property="useBdp" />
    <result column="dict_flag" jdbcType="VARCHAR" property="dictFlag" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, org_id, org_name, purchase_channel, dict_type, dict_type_name, dict_key, dict_value, 
    dict_desc, dict_days, dict_extend, create_by, create_name, operation_name, operation_by, 
    use_bdp, dict_flag, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.PurchaseDictionaryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from purchase_dictionary
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from purchase_dictionary
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from purchase_dictionary
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.PurchaseDictionaryExample">
    delete from purchase_dictionary
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.PurchaseDictionary">
    insert into purchase_dictionary (id, org_id, org_name, 
      purchase_channel, dict_type, dict_type_name, 
      dict_key, dict_value, dict_desc, 
      dict_days, dict_extend, create_by, 
      create_name, operation_name, operation_by, 
      use_bdp, dict_flag, gmt_create, 
      gmt_update)
    values (#{id,jdbcType=BIGINT}, #{orgId,jdbcType=BIGINT}, #{orgName,jdbcType=VARCHAR}, 
      #{purchaseChannel,jdbcType=TINYINT}, #{dictType,jdbcType=VARCHAR}, #{dictTypeName,jdbcType=VARCHAR}, 
      #{dictKey,jdbcType=VARCHAR}, #{dictValue,jdbcType=VARCHAR}, #{dictDesc,jdbcType=VARCHAR}, 
      #{dictDays,jdbcType=INTEGER}, #{dictExtend,jdbcType=VARCHAR}, #{createBy,jdbcType=BIGINT}, 
      #{createName,jdbcType=VARCHAR}, #{operationName,jdbcType=VARCHAR}, #{operationBy,jdbcType=BIGINT}, 
      #{useBdp,jdbcType=TINYINT}, #{dictFlag,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.PurchaseDictionary">
    insert into purchase_dictionary
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="purchaseChannel != null">
        purchase_channel,
      </if>
      <if test="dictType != null">
        dict_type,
      </if>
      <if test="dictTypeName != null">
        dict_type_name,
      </if>
      <if test="dictKey != null">
        dict_key,
      </if>
      <if test="dictValue != null">
        dict_value,
      </if>
      <if test="dictDesc != null">
        dict_desc,
      </if>
      <if test="dictDays != null">
        dict_days,
      </if>
      <if test="dictExtend != null">
        dict_extend,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="operationName != null">
        operation_name,
      </if>
      <if test="operationBy != null">
        operation_by,
      </if>
      <if test="useBdp != null">
        use_bdp,
      </if>
      <if test="dictFlag != null">
        dict_flag,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="purchaseChannel != null">
        #{purchaseChannel,jdbcType=TINYINT},
      </if>
      <if test="dictType != null">
        #{dictType,jdbcType=VARCHAR},
      </if>
      <if test="dictTypeName != null">
        #{dictTypeName,jdbcType=VARCHAR},
      </if>
      <if test="dictKey != null">
        #{dictKey,jdbcType=VARCHAR},
      </if>
      <if test="dictValue != null">
        #{dictValue,jdbcType=VARCHAR},
      </if>
      <if test="dictDesc != null">
        #{dictDesc,jdbcType=VARCHAR},
      </if>
      <if test="dictDays != null">
        #{dictDays,jdbcType=INTEGER},
      </if>
      <if test="dictExtend != null">
        #{dictExtend,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="operationName != null">
        #{operationName,jdbcType=VARCHAR},
      </if>
      <if test="operationBy != null">
        #{operationBy,jdbcType=BIGINT},
      </if>
      <if test="useBdp != null">
        #{useBdp,jdbcType=TINYINT},
      </if>
      <if test="dictFlag != null">
        #{dictFlag,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.PurchaseDictionaryExample" resultType="java.lang.Long">
    select count(*) from purchase_dictionary
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update purchase_dictionary
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseChannel != null">
        purchase_channel = #{record.purchaseChannel,jdbcType=TINYINT},
      </if>
      <if test="record.dictType != null">
        dict_type = #{record.dictType,jdbcType=VARCHAR},
      </if>
      <if test="record.dictTypeName != null">
        dict_type_name = #{record.dictTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.dictKey != null">
        dict_key = #{record.dictKey,jdbcType=VARCHAR},
      </if>
      <if test="record.dictValue != null">
        dict_value = #{record.dictValue,jdbcType=VARCHAR},
      </if>
      <if test="record.dictDesc != null">
        dict_desc = #{record.dictDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.dictDays != null">
        dict_days = #{record.dictDays,jdbcType=INTEGER},
      </if>
      <if test="record.dictExtend != null">
        dict_extend = #{record.dictExtend,jdbcType=VARCHAR},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=BIGINT},
      </if>
      <if test="record.createName != null">
        create_name = #{record.createName,jdbcType=VARCHAR},
      </if>
      <if test="record.operationName != null">
        operation_name = #{record.operationName,jdbcType=VARCHAR},
      </if>
      <if test="record.operationBy != null">
        operation_by = #{record.operationBy,jdbcType=BIGINT},
      </if>
      <if test="record.useBdp != null">
        use_bdp = #{record.useBdp,jdbcType=TINYINT},
      </if>
      <if test="record.dictFlag != null">
        dict_flag = #{record.dictFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update purchase_dictionary
    set id = #{record.id,jdbcType=BIGINT},
      org_id = #{record.orgId,jdbcType=BIGINT},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      purchase_channel = #{record.purchaseChannel,jdbcType=TINYINT},
      dict_type = #{record.dictType,jdbcType=VARCHAR},
      dict_type_name = #{record.dictTypeName,jdbcType=VARCHAR},
      dict_key = #{record.dictKey,jdbcType=VARCHAR},
      dict_value = #{record.dictValue,jdbcType=VARCHAR},
      dict_desc = #{record.dictDesc,jdbcType=VARCHAR},
      dict_days = #{record.dictDays,jdbcType=INTEGER},
      dict_extend = #{record.dictExtend,jdbcType=VARCHAR},
      create_by = #{record.createBy,jdbcType=BIGINT},
      create_name = #{record.createName,jdbcType=VARCHAR},
      operation_name = #{record.operationName,jdbcType=VARCHAR},
      operation_by = #{record.operationBy,jdbcType=BIGINT},
      use_bdp = #{record.useBdp,jdbcType=TINYINT},
      dict_flag = #{record.dictFlag,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.PurchaseDictionary">
    update purchase_dictionary
    <set>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="purchaseChannel != null">
        purchase_channel = #{purchaseChannel,jdbcType=TINYINT},
      </if>
      <if test="dictType != null">
        dict_type = #{dictType,jdbcType=VARCHAR},
      </if>
      <if test="dictTypeName != null">
        dict_type_name = #{dictTypeName,jdbcType=VARCHAR},
      </if>
      <if test="dictKey != null">
        dict_key = #{dictKey,jdbcType=VARCHAR},
      </if>
      <if test="dictValue != null">
        dict_value = #{dictValue,jdbcType=VARCHAR},
      </if>
      <if test="dictDesc != null">
        dict_desc = #{dictDesc,jdbcType=VARCHAR},
      </if>
      <if test="dictDays != null">
        dict_days = #{dictDays,jdbcType=INTEGER},
      </if>
      <if test="dictExtend != null">
        dict_extend = #{dictExtend,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="operationName != null">
        operation_name = #{operationName,jdbcType=VARCHAR},
      </if>
      <if test="operationBy != null">
        operation_by = #{operationBy,jdbcType=BIGINT},
      </if>
      <if test="useBdp != null">
        use_bdp = #{useBdp,jdbcType=TINYINT},
      </if>
      <if test="dictFlag != null">
        dict_flag = #{dictFlag,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.PurchaseDictionary">
    update purchase_dictionary
    set org_id = #{orgId,jdbcType=BIGINT},
      org_name = #{orgName,jdbcType=VARCHAR},
      purchase_channel = #{purchaseChannel,jdbcType=TINYINT},
      dict_type = #{dictType,jdbcType=VARCHAR},
      dict_type_name = #{dictTypeName,jdbcType=VARCHAR},
      dict_key = #{dictKey,jdbcType=VARCHAR},
      dict_value = #{dictValue,jdbcType=VARCHAR},
      dict_desc = #{dictDesc,jdbcType=VARCHAR},
      dict_days = #{dictDays,jdbcType=INTEGER},
      dict_extend = #{dictExtend,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=BIGINT},
      create_name = #{createName,jdbcType=VARCHAR},
      operation_name = #{operationName,jdbcType=VARCHAR},
      operation_by = #{operationBy,jdbcType=BIGINT},
      use_bdp = #{useBdp,jdbcType=TINYINT},
      dict_flag = #{dictFlag,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>