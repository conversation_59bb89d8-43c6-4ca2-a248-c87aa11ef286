<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.BdpWarehouseReceiptInfoTmpMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.BdpWarehouseReceiptInfoTmp">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="tran_type" jdbcType="VARCHAR" property="tranType" />
    <result column="tran_date" jdbcType="VARCHAR" property="tranDate" />
    <result column="sup_code" jdbcType="VARCHAR" property="supCode" />
    <result column="sup_name" jdbcType="VARCHAR" property="supName" />
    <result column="mat_code" jdbcType="VARCHAR" property="matCode" />
    <result column="mat_name" jdbcType="VARCHAR" property="matName" />
    <result column="mat_spec" jdbcType="VARCHAR" property="matSpec" />
    <result column="quatity" jdbcType="INTEGER" property="quatity" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="manu" jdbcType="VARCHAR" property="manu" />
    <result column="batch_code" jdbcType="VARCHAR" property="batchCode" />
    <result column="pur_company_code" jdbcType="VARCHAR" property="purCompanyCode" />
    <result column="pur_company" jdbcType="VARCHAR" property="purCompany" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="werks_code" jdbcType="VARCHAR" property="werksCode" />
    <result column="werks_name" jdbcType="VARCHAR" property="werksName" />
    <result column="buyer" jdbcType="VARCHAR" property="buyer" />
    <result column="eff_date" jdbcType="VARCHAR" property="effDate" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="source_code" jdbcType="INTEGER" property="sourceCode" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_no, tran_type, tran_date, sup_code, sup_name, mat_code, mat_name, mat_spec, 
    quatity, price, amount, manu, batch_code, pur_company_code, pur_company, company_code, 
    company_name, werks_code, werks_name, buyer, eff_date, gmt_create, gmt_update, source_code
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.BdpWarehouseReceiptInfoTmpExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from bdp_warehouse_receipt_info_tmp
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bdp_warehouse_receipt_info_tmp
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bdp_warehouse_receipt_info_tmp
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.BdpWarehouseReceiptInfoTmpExample">
    delete from bdp_warehouse_receipt_info_tmp
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.BdpWarehouseReceiptInfoTmp">
    insert into bdp_warehouse_receipt_info_tmp (id, order_no, tran_type, 
      tran_date, sup_code, sup_name, 
      mat_code, mat_name, mat_spec, 
      quatity, price, amount, 
      manu, batch_code, pur_company_code, 
      pur_company, company_code, company_name, 
      werks_code, werks_name, buyer, 
      eff_date, gmt_create, gmt_update, 
      source_code)
    values (#{id,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, #{tranType,jdbcType=VARCHAR}, 
      #{tranDate,jdbcType=VARCHAR}, #{supCode,jdbcType=VARCHAR}, #{supName,jdbcType=VARCHAR}, 
      #{matCode,jdbcType=VARCHAR}, #{matName,jdbcType=VARCHAR}, #{matSpec,jdbcType=VARCHAR}, 
      #{quatity,jdbcType=INTEGER}, #{price,jdbcType=DECIMAL}, #{amount,jdbcType=DECIMAL}, 
      #{manu,jdbcType=VARCHAR}, #{batchCode,jdbcType=VARCHAR}, #{purCompanyCode,jdbcType=VARCHAR}, 
      #{purCompany,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, 
      #{werksCode,jdbcType=VARCHAR}, #{werksName,jdbcType=VARCHAR}, #{buyer,jdbcType=VARCHAR}, 
      #{effDate,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, 
      #{sourceCode,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.BdpWarehouseReceiptInfoTmp">
    insert into bdp_warehouse_receipt_info_tmp
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="tranType != null">
        tran_type,
      </if>
      <if test="tranDate != null">
        tran_date,
      </if>
      <if test="supCode != null">
        sup_code,
      </if>
      <if test="supName != null">
        sup_name,
      </if>
      <if test="matCode != null">
        mat_code,
      </if>
      <if test="matName != null">
        mat_name,
      </if>
      <if test="matSpec != null">
        mat_spec,
      </if>
      <if test="quatity != null">
        quatity,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="manu != null">
        manu,
      </if>
      <if test="batchCode != null">
        batch_code,
      </if>
      <if test="purCompanyCode != null">
        pur_company_code,
      </if>
      <if test="purCompany != null">
        pur_company,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="werksCode != null">
        werks_code,
      </if>
      <if test="werksName != null">
        werks_name,
      </if>
      <if test="buyer != null">
        buyer,
      </if>
      <if test="effDate != null">
        eff_date,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="sourceCode != null">
        source_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="tranType != null">
        #{tranType,jdbcType=VARCHAR},
      </if>
      <if test="tranDate != null">
        #{tranDate,jdbcType=VARCHAR},
      </if>
      <if test="supCode != null">
        #{supCode,jdbcType=VARCHAR},
      </if>
      <if test="supName != null">
        #{supName,jdbcType=VARCHAR},
      </if>
      <if test="matCode != null">
        #{matCode,jdbcType=VARCHAR},
      </if>
      <if test="matName != null">
        #{matName,jdbcType=VARCHAR},
      </if>
      <if test="matSpec != null">
        #{matSpec,jdbcType=VARCHAR},
      </if>
      <if test="quatity != null">
        #{quatity,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="manu != null">
        #{manu,jdbcType=VARCHAR},
      </if>
      <if test="batchCode != null">
        #{batchCode,jdbcType=VARCHAR},
      </if>
      <if test="purCompanyCode != null">
        #{purCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="purCompany != null">
        #{purCompany,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="werksCode != null">
        #{werksCode,jdbcType=VARCHAR},
      </if>
      <if test="werksName != null">
        #{werksName,jdbcType=VARCHAR},
      </if>
      <if test="buyer != null">
        #{buyer,jdbcType=VARCHAR},
      </if>
      <if test="effDate != null">
        #{effDate,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="sourceCode != null">
        #{sourceCode,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.BdpWarehouseReceiptInfoTmpExample" resultType="java.lang.Long">
    select count(*) from bdp_warehouse_receipt_info_tmp
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update bdp_warehouse_receipt_info_tmp
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orderNo != null">
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.tranType != null">
        tran_type = #{record.tranType,jdbcType=VARCHAR},
      </if>
      <if test="record.tranDate != null">
        tran_date = #{record.tranDate,jdbcType=VARCHAR},
      </if>
      <if test="record.supCode != null">
        sup_code = #{record.supCode,jdbcType=VARCHAR},
      </if>
      <if test="record.supName != null">
        sup_name = #{record.supName,jdbcType=VARCHAR},
      </if>
      <if test="record.matCode != null">
        mat_code = #{record.matCode,jdbcType=VARCHAR},
      </if>
      <if test="record.matName != null">
        mat_name = #{record.matName,jdbcType=VARCHAR},
      </if>
      <if test="record.matSpec != null">
        mat_spec = #{record.matSpec,jdbcType=VARCHAR},
      </if>
      <if test="record.quatity != null">
        quatity = #{record.quatity,jdbcType=INTEGER},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=DECIMAL},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=DECIMAL},
      </if>
      <if test="record.manu != null">
        manu = #{record.manu,jdbcType=VARCHAR},
      </if>
      <if test="record.batchCode != null">
        batch_code = #{record.batchCode,jdbcType=VARCHAR},
      </if>
      <if test="record.purCompanyCode != null">
        pur_company_code = #{record.purCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.purCompany != null">
        pur_company = #{record.purCompany,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.werksCode != null">
        werks_code = #{record.werksCode,jdbcType=VARCHAR},
      </if>
      <if test="record.werksName != null">
        werks_name = #{record.werksName,jdbcType=VARCHAR},
      </if>
      <if test="record.buyer != null">
        buyer = #{record.buyer,jdbcType=VARCHAR},
      </if>
      <if test="record.effDate != null">
        eff_date = #{record.effDate,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.sourceCode != null">
        source_code = #{record.sourceCode,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update bdp_warehouse_receipt_info_tmp
    set id = #{record.id,jdbcType=BIGINT},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      tran_type = #{record.tranType,jdbcType=VARCHAR},
      tran_date = #{record.tranDate,jdbcType=VARCHAR},
      sup_code = #{record.supCode,jdbcType=VARCHAR},
      sup_name = #{record.supName,jdbcType=VARCHAR},
      mat_code = #{record.matCode,jdbcType=VARCHAR},
      mat_name = #{record.matName,jdbcType=VARCHAR},
      mat_spec = #{record.matSpec,jdbcType=VARCHAR},
      quatity = #{record.quatity,jdbcType=INTEGER},
      price = #{record.price,jdbcType=DECIMAL},
      amount = #{record.amount,jdbcType=DECIMAL},
      manu = #{record.manu,jdbcType=VARCHAR},
      batch_code = #{record.batchCode,jdbcType=VARCHAR},
      pur_company_code = #{record.purCompanyCode,jdbcType=VARCHAR},
      pur_company = #{record.purCompany,jdbcType=VARCHAR},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      werks_code = #{record.werksCode,jdbcType=VARCHAR},
      werks_name = #{record.werksName,jdbcType=VARCHAR},
      buyer = #{record.buyer,jdbcType=VARCHAR},
      eff_date = #{record.effDate,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      source_code = #{record.sourceCode,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.BdpWarehouseReceiptInfoTmp">
    update bdp_warehouse_receipt_info_tmp
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="tranType != null">
        tran_type = #{tranType,jdbcType=VARCHAR},
      </if>
      <if test="tranDate != null">
        tran_date = #{tranDate,jdbcType=VARCHAR},
      </if>
      <if test="supCode != null">
        sup_code = #{supCode,jdbcType=VARCHAR},
      </if>
      <if test="supName != null">
        sup_name = #{supName,jdbcType=VARCHAR},
      </if>
      <if test="matCode != null">
        mat_code = #{matCode,jdbcType=VARCHAR},
      </if>
      <if test="matName != null">
        mat_name = #{matName,jdbcType=VARCHAR},
      </if>
      <if test="matSpec != null">
        mat_spec = #{matSpec,jdbcType=VARCHAR},
      </if>
      <if test="quatity != null">
        quatity = #{quatity,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="manu != null">
        manu = #{manu,jdbcType=VARCHAR},
      </if>
      <if test="batchCode != null">
        batch_code = #{batchCode,jdbcType=VARCHAR},
      </if>
      <if test="purCompanyCode != null">
        pur_company_code = #{purCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="purCompany != null">
        pur_company = #{purCompany,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="werksCode != null">
        werks_code = #{werksCode,jdbcType=VARCHAR},
      </if>
      <if test="werksName != null">
        werks_name = #{werksName,jdbcType=VARCHAR},
      </if>
      <if test="buyer != null">
        buyer = #{buyer,jdbcType=VARCHAR},
      </if>
      <if test="effDate != null">
        eff_date = #{effDate,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="sourceCode != null">
        source_code = #{sourceCode,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.BdpWarehouseReceiptInfoTmp">
    update bdp_warehouse_receipt_info_tmp
    set order_no = #{orderNo,jdbcType=VARCHAR},
      tran_type = #{tranType,jdbcType=VARCHAR},
      tran_date = #{tranDate,jdbcType=VARCHAR},
      sup_code = #{supCode,jdbcType=VARCHAR},
      sup_name = #{supName,jdbcType=VARCHAR},
      mat_code = #{matCode,jdbcType=VARCHAR},
      mat_name = #{matName,jdbcType=VARCHAR},
      mat_spec = #{matSpec,jdbcType=VARCHAR},
      quatity = #{quatity,jdbcType=INTEGER},
      price = #{price,jdbcType=DECIMAL},
      amount = #{amount,jdbcType=DECIMAL},
      manu = #{manu,jdbcType=VARCHAR},
      batch_code = #{batchCode,jdbcType=VARCHAR},
      pur_company_code = #{purCompanyCode,jdbcType=VARCHAR},
      pur_company = #{purCompany,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      werks_code = #{werksCode,jdbcType=VARCHAR},
      werks_name = #{werksName,jdbcType=VARCHAR},
      buyer = #{buyer,jdbcType=VARCHAR},
      eff_date = #{effDate,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      source_code = #{sourceCode,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>