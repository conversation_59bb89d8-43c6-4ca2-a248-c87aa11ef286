<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmBillOrderExtendMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmBillOrderExtend">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="bill_order_no" jdbcType="VARCHAR" property="billOrderNo" />
    <result column="proof_no" jdbcType="VARCHAR" property="proofNo" />
    <result column="proof_date" jdbcType="DATE" property="proofDate" />
    <result column="post_date" jdbcType="DATE" property="postDate" />
    <result column="fiscal_year" jdbcType="VARCHAR" property="fiscalYear" />
    <result column="trade_type" jdbcType="VARCHAR" property="tradeType" />
    <result column="commons" jdbcType="VARCHAR" property="commons" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, bill_order_no, proof_no, proof_date, post_date, fiscal_year, trade_type, commons, 
    `status`, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmBillOrderExtendExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_bill_order_extend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_bill_order_extend
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from srm_bill_order_extend
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmBillOrderExtendExample">
    delete from srm_bill_order_extend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmBillOrderExtend">
    insert into srm_bill_order_extend (id, bill_order_no, proof_no, 
      proof_date, post_date, fiscal_year, 
      trade_type, commons, `status`, 
      gmt_create, gmt_update)
    values (#{id,jdbcType=INTEGER}, #{billOrderNo,jdbcType=VARCHAR}, #{proofNo,jdbcType=VARCHAR}, 
      #{proofDate,jdbcType=DATE}, #{postDate,jdbcType=DATE}, #{fiscalYear,jdbcType=VARCHAR}, 
      #{tradeType,jdbcType=VARCHAR}, #{commons,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmBillOrderExtend">
    insert into srm_bill_order_extend
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="billOrderNo != null">
        bill_order_no,
      </if>
      <if test="proofNo != null">
        proof_no,
      </if>
      <if test="proofDate != null">
        proof_date,
      </if>
      <if test="postDate != null">
        post_date,
      </if>
      <if test="fiscalYear != null">
        fiscal_year,
      </if>
      <if test="tradeType != null">
        trade_type,
      </if>
      <if test="commons != null">
        commons,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="billOrderNo != null">
        #{billOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="proofNo != null">
        #{proofNo,jdbcType=VARCHAR},
      </if>
      <if test="proofDate != null">
        #{proofDate,jdbcType=DATE},
      </if>
      <if test="postDate != null">
        #{postDate,jdbcType=DATE},
      </if>
      <if test="fiscalYear != null">
        #{fiscalYear,jdbcType=VARCHAR},
      </if>
      <if test="tradeType != null">
        #{tradeType,jdbcType=VARCHAR},
      </if>
      <if test="commons != null">
        #{commons,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmBillOrderExtendExample" resultType="java.lang.Long">
    select count(*) from srm_bill_order_extend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_bill_order_extend
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.billOrderNo != null">
        bill_order_no = #{record.billOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.proofNo != null">
        proof_no = #{record.proofNo,jdbcType=VARCHAR},
      </if>
      <if test="record.proofDate != null">
        proof_date = #{record.proofDate,jdbcType=DATE},
      </if>
      <if test="record.postDate != null">
        post_date = #{record.postDate,jdbcType=DATE},
      </if>
      <if test="record.fiscalYear != null">
        fiscal_year = #{record.fiscalYear,jdbcType=VARCHAR},
      </if>
      <if test="record.tradeType != null">
        trade_type = #{record.tradeType,jdbcType=VARCHAR},
      </if>
      <if test="record.commons != null">
        commons = #{record.commons,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_bill_order_extend
    set id = #{record.id,jdbcType=INTEGER},
      bill_order_no = #{record.billOrderNo,jdbcType=VARCHAR},
      proof_no = #{record.proofNo,jdbcType=VARCHAR},
      proof_date = #{record.proofDate,jdbcType=DATE},
      post_date = #{record.postDate,jdbcType=DATE},
      fiscal_year = #{record.fiscalYear,jdbcType=VARCHAR},
      trade_type = #{record.tradeType,jdbcType=VARCHAR},
      commons = #{record.commons,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmBillOrderExtend">
    update srm_bill_order_extend
    <set>
      <if test="billOrderNo != null">
        bill_order_no = #{billOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="proofNo != null">
        proof_no = #{proofNo,jdbcType=VARCHAR},
      </if>
      <if test="proofDate != null">
        proof_date = #{proofDate,jdbcType=DATE},
      </if>
      <if test="postDate != null">
        post_date = #{postDate,jdbcType=DATE},
      </if>
      <if test="fiscalYear != null">
        fiscal_year = #{fiscalYear,jdbcType=VARCHAR},
      </if>
      <if test="tradeType != null">
        trade_type = #{tradeType,jdbcType=VARCHAR},
      </if>
      <if test="commons != null">
        commons = #{commons,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmBillOrderExtend">
    update srm_bill_order_extend
    set bill_order_no = #{billOrderNo,jdbcType=VARCHAR},
      proof_no = #{proofNo,jdbcType=VARCHAR},
      proof_date = #{proofDate,jdbcType=DATE},
      post_date = #{postDate,jdbcType=DATE},
      fiscal_year = #{fiscalYear,jdbcType=VARCHAR},
      trade_type = #{tradeType,jdbcType=VARCHAR},
      commons = #{commons,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>