<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmPermAccountOperateLogMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmPermAccountOperateLog">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_no" jdbcType="VARCHAR" property="accountNo" />
    <result column="basic_detail" jdbcType="VARCHAR" property="basicDetail" />
    <result column="product_scope" jdbcType="VARCHAR" property="productScope" />
    <result column="company_scope" jdbcType="VARCHAR" property="companyScope" />
    <result column="operate_no" jdbcType="VARCHAR" property="operateNo" />
    <result column="operate_name" jdbcType="VARCHAR" property="operateName" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_no, basic_detail, product_scope, company_scope, operate_no, operate_name, 
    org_id, org_name, gmt_create, gmt_update, `status`, extend, version
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmPermAccountOperateLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_perm_account_operate_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_perm_account_operate_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from srm_perm_account_operate_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmPermAccountOperateLogExample">
    delete from srm_perm_account_operate_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmPermAccountOperateLog">
    insert into srm_perm_account_operate_log (id, account_no, basic_detail, 
      product_scope, company_scope, operate_no, 
      operate_name, org_id, org_name, 
      gmt_create, gmt_update, `status`, 
      extend, version)
    values (#{id,jdbcType=INTEGER}, #{accountNo,jdbcType=VARCHAR}, #{basicDetail,jdbcType=VARCHAR}, 
      #{productScope,jdbcType=VARCHAR}, #{companyScope,jdbcType=VARCHAR}, #{operateNo,jdbcType=VARCHAR}, 
      #{operateName,jdbcType=VARCHAR}, #{orgId,jdbcType=BIGINT}, #{orgName,jdbcType=VARCHAR}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}, 
      #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmPermAccountOperateLog">
    insert into srm_perm_account_operate_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="accountNo != null">
        account_no,
      </if>
      <if test="basicDetail != null">
        basic_detail,
      </if>
      <if test="productScope != null">
        product_scope,
      </if>
      <if test="companyScope != null">
        company_scope,
      </if>
      <if test="operateNo != null">
        operate_no,
      </if>
      <if test="operateName != null">
        operate_name,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="accountNo != null">
        #{accountNo,jdbcType=VARCHAR},
      </if>
      <if test="basicDetail != null">
        #{basicDetail,jdbcType=VARCHAR},
      </if>
      <if test="productScope != null">
        #{productScope,jdbcType=VARCHAR},
      </if>
      <if test="companyScope != null">
        #{companyScope,jdbcType=VARCHAR},
      </if>
      <if test="operateNo != null">
        #{operateNo,jdbcType=VARCHAR},
      </if>
      <if test="operateName != null">
        #{operateName,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmPermAccountOperateLogExample" resultType="java.lang.Long">
    select count(*) from srm_perm_account_operate_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_perm_account_operate_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountNo != null">
        account_no = #{record.accountNo,jdbcType=VARCHAR},
      </if>
      <if test="record.basicDetail != null">
        basic_detail = #{record.basicDetail,jdbcType=VARCHAR},
      </if>
      <if test="record.productScope != null">
        product_scope = #{record.productScope,jdbcType=VARCHAR},
      </if>
      <if test="record.companyScope != null">
        company_scope = #{record.companyScope,jdbcType=VARCHAR},
      </if>
      <if test="record.operateNo != null">
        operate_no = #{record.operateNo,jdbcType=VARCHAR},
      </if>
      <if test="record.operateName != null">
        operate_name = #{record.operateName,jdbcType=VARCHAR},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_perm_account_operate_log
    set id = #{record.id,jdbcType=INTEGER},
      account_no = #{record.accountNo,jdbcType=VARCHAR},
      basic_detail = #{record.basicDetail,jdbcType=VARCHAR},
      product_scope = #{record.productScope,jdbcType=VARCHAR},
      company_scope = #{record.companyScope,jdbcType=VARCHAR},
      operate_no = #{record.operateNo,jdbcType=VARCHAR},
      operate_name = #{record.operateName,jdbcType=VARCHAR},
      org_id = #{record.orgId,jdbcType=BIGINT},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      `status` = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmPermAccountOperateLog">
    update srm_perm_account_operate_log
    <set>
      <if test="accountNo != null">
        account_no = #{accountNo,jdbcType=VARCHAR},
      </if>
      <if test="basicDetail != null">
        basic_detail = #{basicDetail,jdbcType=VARCHAR},
      </if>
      <if test="productScope != null">
        product_scope = #{productScope,jdbcType=VARCHAR},
      </if>
      <if test="companyScope != null">
        company_scope = #{companyScope,jdbcType=VARCHAR},
      </if>
      <if test="operateNo != null">
        operate_no = #{operateNo,jdbcType=VARCHAR},
      </if>
      <if test="operateName != null">
        operate_name = #{operateName,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmPermAccountOperateLog">
    update srm_perm_account_operate_log
    set account_no = #{accountNo,jdbcType=VARCHAR},
      basic_detail = #{basicDetail,jdbcType=VARCHAR},
      product_scope = #{productScope,jdbcType=VARCHAR},
      company_scope = #{companyScope,jdbcType=VARCHAR},
      operate_no = #{operateNo,jdbcType=VARCHAR},
      operate_name = #{operateName,jdbcType=VARCHAR},
      org_id = #{orgId,jdbcType=BIGINT},
      org_name = #{orgName,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>