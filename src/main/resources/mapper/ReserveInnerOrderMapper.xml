<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.ReserveInnerOrderMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.ReserveInnerOrder">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="reserve_order_no" jdbcType="VARCHAR" property="reserveOrderNo" />
    <result column="deliver_code" jdbcType="VARCHAR" property="deliverCode" />
    <result column="reserve_deliver_date" jdbcType="TIMESTAMP" property="reserveDeliverDate" />
    <result column="reserve_time_interval" jdbcType="VARCHAR" property="reserveTimeInterval" />
    <result column="dc_deploy_no" jdbcType="VARCHAR" property="dcDeployNo" />
    <result column="reserve_status" jdbcType="INTEGER" property="reserveStatus" />
    <result column="complete_time" jdbcType="VARCHAR" property="completeTime" />
    <result column="goods_kind_quantity" jdbcType="INTEGER" property="goodsKindQuantity" />
    <result column="goods_quantity" jdbcType="DECIMAL" property="goodsQuantity" />
    <result column="send_no" jdbcType="VARCHAR" property="sendNo" />
    <result column="purchase_order_no" jdbcType="VARCHAR" property="purchaseOrderNo" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="warehouse_no" jdbcType="VARCHAR" property="warehouseNo" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="warehouse_address" jdbcType="VARCHAR" property="warehouseAddress" />
    <result column="supplier_company_code" jdbcType="VARCHAR" property="supplierCompanyCode" />
    <result column="supplier_company_name" jdbcType="VARCHAR" property="supplierCompanyName" />
    <result column="supplier_warehouse_no" jdbcType="VARCHAR" property="supplierWarehouseNo" />
    <result column="supplier_warehouse_name" jdbcType="VARCHAR" property="supplierWarehouseName" />
    <result column="supplier_person" jdbcType="VARCHAR" property="supplierPerson" />
    <result column="supplier_phone" jdbcType="VARCHAR" property="supplierPhone" />
    <result column="advice_reserve_time" jdbcType="VARCHAR" property="adviceReserveTime" />
    <result column="spec_desc" jdbcType="VARCHAR" property="specDesc" />
    <result column="result_desc" jdbcType="VARCHAR" property="resultDesc" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, reserve_order_no, deliver_code, reserve_deliver_date, reserve_time_interval, 
    dc_deploy_no, reserve_status, complete_time, goods_kind_quantity, goods_quantity, 
    send_no, purchase_order_no, company_code, company_name, warehouse_no, warehouse_name, 
    warehouse_address, supplier_company_code, supplier_company_name, supplier_warehouse_no, 
    supplier_warehouse_name, supplier_person, supplier_phone, advice_reserve_time, spec_desc, 
    result_desc, `comment`, `status`, extend, version, created_by, created_name, updated_by, 
    updated_name, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.ReserveInnerOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from reserve_inner_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from reserve_inner_order
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from reserve_inner_order
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.ReserveInnerOrderExample">
    delete from reserve_inner_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.ReserveInnerOrder">
    insert into reserve_inner_order (id, reserve_order_no, deliver_code, 
      reserve_deliver_date, reserve_time_interval, 
      dc_deploy_no, reserve_status, complete_time, 
      goods_kind_quantity, goods_quantity, send_no, 
      purchase_order_no, company_code, company_name, 
      warehouse_no, warehouse_name, warehouse_address, 
      supplier_company_code, supplier_company_name, 
      supplier_warehouse_no, supplier_warehouse_name, 
      supplier_person, supplier_phone, advice_reserve_time, 
      spec_desc, result_desc, `comment`, 
      `status`, extend, version, 
      created_by, created_name, updated_by, 
      updated_name, gmt_create, gmt_update
      )
    values (#{id,jdbcType=INTEGER}, #{reserveOrderNo,jdbcType=VARCHAR}, #{deliverCode,jdbcType=VARCHAR}, 
      #{reserveDeliverDate,jdbcType=TIMESTAMP}, #{reserveTimeInterval,jdbcType=VARCHAR}, 
      #{dcDeployNo,jdbcType=VARCHAR}, #{reserveStatus,jdbcType=INTEGER}, #{completeTime,jdbcType=VARCHAR}, 
      #{goodsKindQuantity,jdbcType=INTEGER}, #{goodsQuantity,jdbcType=DECIMAL}, #{sendNo,jdbcType=VARCHAR}, 
      #{purchaseOrderNo,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, 
      #{warehouseNo,jdbcType=VARCHAR}, #{warehouseName,jdbcType=VARCHAR}, #{warehouseAddress,jdbcType=VARCHAR}, 
      #{supplierCompanyCode,jdbcType=VARCHAR}, #{supplierCompanyName,jdbcType=VARCHAR}, 
      #{supplierWarehouseNo,jdbcType=VARCHAR}, #{supplierWarehouseName,jdbcType=VARCHAR}, 
      #{supplierPerson,jdbcType=VARCHAR}, #{supplierPhone,jdbcType=VARCHAR}, #{adviceReserveTime,jdbcType=VARCHAR}, 
      #{specDesc,jdbcType=VARCHAR}, #{resultDesc,jdbcType=VARCHAR}, #{comment,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, 
      #{updatedName,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.ReserveInnerOrder">
    insert into reserve_inner_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="reserveOrderNo != null">
        reserve_order_no,
      </if>
      <if test="deliverCode != null">
        deliver_code,
      </if>
      <if test="reserveDeliverDate != null">
        reserve_deliver_date,
      </if>
      <if test="reserveTimeInterval != null">
        reserve_time_interval,
      </if>
      <if test="dcDeployNo != null">
        dc_deploy_no,
      </if>
      <if test="reserveStatus != null">
        reserve_status,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="goodsKindQuantity != null">
        goods_kind_quantity,
      </if>
      <if test="goodsQuantity != null">
        goods_quantity,
      </if>
      <if test="sendNo != null">
        send_no,
      </if>
      <if test="purchaseOrderNo != null">
        purchase_order_no,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="warehouseNo != null">
        warehouse_no,
      </if>
      <if test="warehouseName != null">
        warehouse_name,
      </if>
      <if test="warehouseAddress != null">
        warehouse_address,
      </if>
      <if test="supplierCompanyCode != null">
        supplier_company_code,
      </if>
      <if test="supplierCompanyName != null">
        supplier_company_name,
      </if>
      <if test="supplierWarehouseNo != null">
        supplier_warehouse_no,
      </if>
      <if test="supplierWarehouseName != null">
        supplier_warehouse_name,
      </if>
      <if test="supplierPerson != null">
        supplier_person,
      </if>
      <if test="supplierPhone != null">
        supplier_phone,
      </if>
      <if test="adviceReserveTime != null">
        advice_reserve_time,
      </if>
      <if test="specDesc != null">
        spec_desc,
      </if>
      <if test="resultDesc != null">
        result_desc,
      </if>
      <if test="comment != null">
        `comment`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="reserveOrderNo != null">
        #{reserveOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="deliverCode != null">
        #{deliverCode,jdbcType=VARCHAR},
      </if>
      <if test="reserveDeliverDate != null">
        #{reserveDeliverDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reserveTimeInterval != null">
        #{reserveTimeInterval,jdbcType=VARCHAR},
      </if>
      <if test="dcDeployNo != null">
        #{dcDeployNo,jdbcType=VARCHAR},
      </if>
      <if test="reserveStatus != null">
        #{reserveStatus,jdbcType=INTEGER},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=VARCHAR},
      </if>
      <if test="goodsKindQuantity != null">
        #{goodsKindQuantity,jdbcType=INTEGER},
      </if>
      <if test="goodsQuantity != null">
        #{goodsQuantity,jdbcType=DECIMAL},
      </if>
      <if test="sendNo != null">
        #{sendNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderNo != null">
        #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseNo != null">
        #{warehouseNo,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseAddress != null">
        #{warehouseAddress,jdbcType=VARCHAR},
      </if>
      <if test="supplierCompanyCode != null">
        #{supplierCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierCompanyName != null">
        #{supplierCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="supplierWarehouseNo != null">
        #{supplierWarehouseNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierWarehouseName != null">
        #{supplierWarehouseName,jdbcType=VARCHAR},
      </if>
      <if test="supplierPerson != null">
        #{supplierPerson,jdbcType=VARCHAR},
      </if>
      <if test="supplierPhone != null">
        #{supplierPhone,jdbcType=VARCHAR},
      </if>
      <if test="adviceReserveTime != null">
        #{adviceReserveTime,jdbcType=VARCHAR},
      </if>
      <if test="specDesc != null">
        #{specDesc,jdbcType=VARCHAR},
      </if>
      <if test="resultDesc != null">
        #{resultDesc,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        #{comment,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.ReserveInnerOrderExample" resultType="java.lang.Long">
    select count(*) from reserve_inner_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update reserve_inner_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.reserveOrderNo != null">
        reserve_order_no = #{record.reserveOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.deliverCode != null">
        deliver_code = #{record.deliverCode,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveDeliverDate != null">
        reserve_deliver_date = #{record.reserveDeliverDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.reserveTimeInterval != null">
        reserve_time_interval = #{record.reserveTimeInterval,jdbcType=VARCHAR},
      </if>
      <if test="record.dcDeployNo != null">
        dc_deploy_no = #{record.dcDeployNo,jdbcType=VARCHAR},
      </if>
      <if test="record.reserveStatus != null">
        reserve_status = #{record.reserveStatus,jdbcType=INTEGER},
      </if>
      <if test="record.completeTime != null">
        complete_time = #{record.completeTime,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsKindQuantity != null">
        goods_kind_quantity = #{record.goodsKindQuantity,jdbcType=INTEGER},
      </if>
      <if test="record.goodsQuantity != null">
        goods_quantity = #{record.goodsQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.sendNo != null">
        send_no = #{record.sendNo,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrderNo != null">
        purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseNo != null">
        warehouse_no = #{record.warehouseNo,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseName != null">
        warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseAddress != null">
        warehouse_address = #{record.warehouseAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierCompanyCode != null">
        supplier_company_code = #{record.supplierCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierCompanyName != null">
        supplier_company_name = #{record.supplierCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierWarehouseNo != null">
        supplier_warehouse_no = #{record.supplierWarehouseNo,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierWarehouseName != null">
        supplier_warehouse_name = #{record.supplierWarehouseName,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierPerson != null">
        supplier_person = #{record.supplierPerson,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierPhone != null">
        supplier_phone = #{record.supplierPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.adviceReserveTime != null">
        advice_reserve_time = #{record.adviceReserveTime,jdbcType=VARCHAR},
      </if>
      <if test="record.specDesc != null">
        spec_desc = #{record.specDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.resultDesc != null">
        result_desc = #{record.resultDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.comment != null">
        `comment` = #{record.comment,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update reserve_inner_order
    set id = #{record.id,jdbcType=INTEGER},
      reserve_order_no = #{record.reserveOrderNo,jdbcType=VARCHAR},
      deliver_code = #{record.deliverCode,jdbcType=VARCHAR},
      reserve_deliver_date = #{record.reserveDeliverDate,jdbcType=TIMESTAMP},
      reserve_time_interval = #{record.reserveTimeInterval,jdbcType=VARCHAR},
      dc_deploy_no = #{record.dcDeployNo,jdbcType=VARCHAR},
      reserve_status = #{record.reserveStatus,jdbcType=INTEGER},
      complete_time = #{record.completeTime,jdbcType=VARCHAR},
      goods_kind_quantity = #{record.goodsKindQuantity,jdbcType=INTEGER},
      goods_quantity = #{record.goodsQuantity,jdbcType=DECIMAL},
      send_no = #{record.sendNo,jdbcType=VARCHAR},
      purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      warehouse_no = #{record.warehouseNo,jdbcType=VARCHAR},
      warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      warehouse_address = #{record.warehouseAddress,jdbcType=VARCHAR},
      supplier_company_code = #{record.supplierCompanyCode,jdbcType=VARCHAR},
      supplier_company_name = #{record.supplierCompanyName,jdbcType=VARCHAR},
      supplier_warehouse_no = #{record.supplierWarehouseNo,jdbcType=VARCHAR},
      supplier_warehouse_name = #{record.supplierWarehouseName,jdbcType=VARCHAR},
      supplier_person = #{record.supplierPerson,jdbcType=VARCHAR},
      supplier_phone = #{record.supplierPhone,jdbcType=VARCHAR},
      advice_reserve_time = #{record.adviceReserveTime,jdbcType=VARCHAR},
      spec_desc = #{record.specDesc,jdbcType=VARCHAR},
      result_desc = #{record.resultDesc,jdbcType=VARCHAR},
      `comment` = #{record.comment,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.ReserveInnerOrder">
    update reserve_inner_order
    <set>
      <if test="reserveOrderNo != null">
        reserve_order_no = #{reserveOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="deliverCode != null">
        deliver_code = #{deliverCode,jdbcType=VARCHAR},
      </if>
      <if test="reserveDeliverDate != null">
        reserve_deliver_date = #{reserveDeliverDate,jdbcType=TIMESTAMP},
      </if>
      <if test="reserveTimeInterval != null">
        reserve_time_interval = #{reserveTimeInterval,jdbcType=VARCHAR},
      </if>
      <if test="dcDeployNo != null">
        dc_deploy_no = #{dcDeployNo,jdbcType=VARCHAR},
      </if>
      <if test="reserveStatus != null">
        reserve_status = #{reserveStatus,jdbcType=INTEGER},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=VARCHAR},
      </if>
      <if test="goodsKindQuantity != null">
        goods_kind_quantity = #{goodsKindQuantity,jdbcType=INTEGER},
      </if>
      <if test="goodsQuantity != null">
        goods_quantity = #{goodsQuantity,jdbcType=DECIMAL},
      </if>
      <if test="sendNo != null">
        send_no = #{sendNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderNo != null">
        purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseNo != null">
        warehouse_no = #{warehouseNo,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="warehouseAddress != null">
        warehouse_address = #{warehouseAddress,jdbcType=VARCHAR},
      </if>
      <if test="supplierCompanyCode != null">
        supplier_company_code = #{supplierCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierCompanyName != null">
        supplier_company_name = #{supplierCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="supplierWarehouseNo != null">
        supplier_warehouse_no = #{supplierWarehouseNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierWarehouseName != null">
        supplier_warehouse_name = #{supplierWarehouseName,jdbcType=VARCHAR},
      </if>
      <if test="supplierPerson != null">
        supplier_person = #{supplierPerson,jdbcType=VARCHAR},
      </if>
      <if test="supplierPhone != null">
        supplier_phone = #{supplierPhone,jdbcType=VARCHAR},
      </if>
      <if test="adviceReserveTime != null">
        advice_reserve_time = #{adviceReserveTime,jdbcType=VARCHAR},
      </if>
      <if test="specDesc != null">
        spec_desc = #{specDesc,jdbcType=VARCHAR},
      </if>
      <if test="resultDesc != null">
        result_desc = #{resultDesc,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        `comment` = #{comment,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.ReserveInnerOrder">
    update reserve_inner_order
    set reserve_order_no = #{reserveOrderNo,jdbcType=VARCHAR},
      deliver_code = #{deliverCode,jdbcType=VARCHAR},
      reserve_deliver_date = #{reserveDeliverDate,jdbcType=TIMESTAMP},
      reserve_time_interval = #{reserveTimeInterval,jdbcType=VARCHAR},
      dc_deploy_no = #{dcDeployNo,jdbcType=VARCHAR},
      reserve_status = #{reserveStatus,jdbcType=INTEGER},
      complete_time = #{completeTime,jdbcType=VARCHAR},
      goods_kind_quantity = #{goodsKindQuantity,jdbcType=INTEGER},
      goods_quantity = #{goodsQuantity,jdbcType=DECIMAL},
      send_no = #{sendNo,jdbcType=VARCHAR},
      purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      warehouse_no = #{warehouseNo,jdbcType=VARCHAR},
      warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      warehouse_address = #{warehouseAddress,jdbcType=VARCHAR},
      supplier_company_code = #{supplierCompanyCode,jdbcType=VARCHAR},
      supplier_company_name = #{supplierCompanyName,jdbcType=VARCHAR},
      supplier_warehouse_no = #{supplierWarehouseNo,jdbcType=VARCHAR},
      supplier_warehouse_name = #{supplierWarehouseName,jdbcType=VARCHAR},
      supplier_person = #{supplierPerson,jdbcType=VARCHAR},
      supplier_phone = #{supplierPhone,jdbcType=VARCHAR},
      advice_reserve_time = #{adviceReserveTime,jdbcType=VARCHAR},
      spec_desc = #{specDesc,jdbcType=VARCHAR},
      result_desc = #{resultDesc,jdbcType=VARCHAR},
      `comment` = #{comment,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>