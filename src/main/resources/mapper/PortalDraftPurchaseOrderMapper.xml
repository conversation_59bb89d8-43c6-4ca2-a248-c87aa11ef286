<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.PortalDraftPurchaseOrderMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.PortalDraftPurchaseOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="draft_no" jdbcType="VARCHAR" property="draftNo" />
    <result column="order_type" jdbcType="VARCHAR" property="orderType" />
    <result column="purchase_business" jdbcType="INTEGER" property="purchaseBusiness" />
    <result column="supplier_no" jdbcType="VARCHAR" property="supplierNo" />
    <result column="supplier_warehouse_code" jdbcType="VARCHAR" property="supplierWarehouseCode" />
    <result column="expiry_date" jdbcType="TIMESTAMP" property="expiryDate" />
    <result column="purchase_org" jdbcType="VARCHAR" property="purchaseOrg" />
    <result column="purchase_warehouse_code" jdbcType="VARCHAR" property="purchaseWarehouseCode" />
    <result column="purchase_company_code" jdbcType="VARCHAR" property="purchaseCompanyCode" />
    <result column="emp_code" jdbcType="VARCHAR" property="empCode" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, draft_no, order_type, purchase_business, supplier_no, supplier_warehouse_code, 
    expiry_date, purchase_org, purchase_warehouse_code, purchase_company_code, emp_code, 
    gmt_create, gmt_update, `status`, extend, version, created_by, created_name, updated_by, 
    updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.PortalDraftPurchaseOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from portal_draft_purchase_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from portal_draft_purchase_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from portal_draft_purchase_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.PortalDraftPurchaseOrderExample">
    delete from portal_draft_purchase_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.PortalDraftPurchaseOrder">
    insert into portal_draft_purchase_order (id, draft_no, order_type, 
      purchase_business, supplier_no, supplier_warehouse_code, 
      expiry_date, purchase_org, purchase_warehouse_code, 
      purchase_company_code, emp_code, gmt_create, 
      gmt_update, `status`, extend, 
      version, created_by, created_name, 
      updated_by, updated_name)
    values (#{id,jdbcType=BIGINT}, #{draftNo,jdbcType=VARCHAR}, #{orderType,jdbcType=VARCHAR}, 
      #{purchaseBusiness,jdbcType=INTEGER}, #{supplierNo,jdbcType=VARCHAR}, #{supplierWarehouseCode,jdbcType=VARCHAR}, 
      #{expiryDate,jdbcType=TIMESTAMP}, #{purchaseOrg,jdbcType=VARCHAR}, #{purchaseWarehouseCode,jdbcType=VARCHAR}, 
      #{purchaseCompanyCode,jdbcType=VARCHAR}, #{empCode,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, 
      #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.PortalDraftPurchaseOrder">
    insert into portal_draft_purchase_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="draftNo != null">
        draft_no,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="purchaseBusiness != null">
        purchase_business,
      </if>
      <if test="supplierNo != null">
        supplier_no,
      </if>
      <if test="supplierWarehouseCode != null">
        supplier_warehouse_code,
      </if>
      <if test="expiryDate != null">
        expiry_date,
      </if>
      <if test="purchaseOrg != null">
        purchase_org,
      </if>
      <if test="purchaseWarehouseCode != null">
        purchase_warehouse_code,
      </if>
      <if test="purchaseCompanyCode != null">
        purchase_company_code,
      </if>
      <if test="empCode != null">
        emp_code,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="draftNo != null">
        #{draftNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="purchaseBusiness != null">
        #{purchaseBusiness,jdbcType=INTEGER},
      </if>
      <if test="supplierNo != null">
        #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierWarehouseCode != null">
        #{supplierWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="expiryDate != null">
        #{expiryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="purchaseOrg != null">
        #{purchaseOrg,jdbcType=VARCHAR},
      </if>
      <if test="purchaseWarehouseCode != null">
        #{purchaseWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="purchaseCompanyCode != null">
        #{purchaseCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="empCode != null">
        #{empCode,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.PortalDraftPurchaseOrderExample" resultType="java.lang.Long">
    select count(*) from portal_draft_purchase_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update portal_draft_purchase_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.draftNo != null">
        draft_no = #{record.draftNo,jdbcType=VARCHAR},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseBusiness != null">
        purchase_business = #{record.purchaseBusiness,jdbcType=INTEGER},
      </if>
      <if test="record.supplierNo != null">
        supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierWarehouseCode != null">
        supplier_warehouse_code = #{record.supplierWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.expiryDate != null">
        expiry_date = #{record.expiryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.purchaseOrg != null">
        purchase_org = #{record.purchaseOrg,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseWarehouseCode != null">
        purchase_warehouse_code = #{record.purchaseWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseCompanyCode != null">
        purchase_company_code = #{record.purchaseCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.empCode != null">
        emp_code = #{record.empCode,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update portal_draft_purchase_order
    set id = #{record.id,jdbcType=BIGINT},
      draft_no = #{record.draftNo,jdbcType=VARCHAR},
      order_type = #{record.orderType,jdbcType=VARCHAR},
      purchase_business = #{record.purchaseBusiness,jdbcType=INTEGER},
      supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      supplier_warehouse_code = #{record.supplierWarehouseCode,jdbcType=VARCHAR},
      expiry_date = #{record.expiryDate,jdbcType=TIMESTAMP},
      purchase_org = #{record.purchaseOrg,jdbcType=VARCHAR},
      purchase_warehouse_code = #{record.purchaseWarehouseCode,jdbcType=VARCHAR},
      purchase_company_code = #{record.purchaseCompanyCode,jdbcType=VARCHAR},
      emp_code = #{record.empCode,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      `status` = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.PortalDraftPurchaseOrder">
    update portal_draft_purchase_order
    <set>
      <if test="draftNo != null">
        draft_no = #{draftNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="purchaseBusiness != null">
        purchase_business = #{purchaseBusiness,jdbcType=INTEGER},
      </if>
      <if test="supplierNo != null">
        supplier_no = #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierWarehouseCode != null">
        supplier_warehouse_code = #{supplierWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="expiryDate != null">
        expiry_date = #{expiryDate,jdbcType=TIMESTAMP},
      </if>
      <if test="purchaseOrg != null">
        purchase_org = #{purchaseOrg,jdbcType=VARCHAR},
      </if>
      <if test="purchaseWarehouseCode != null">
        purchase_warehouse_code = #{purchaseWarehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="purchaseCompanyCode != null">
        purchase_company_code = #{purchaseCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="empCode != null">
        emp_code = #{empCode,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.PortalDraftPurchaseOrder">
    update portal_draft_purchase_order
    set draft_no = #{draftNo,jdbcType=VARCHAR},
      order_type = #{orderType,jdbcType=VARCHAR},
      purchase_business = #{purchaseBusiness,jdbcType=INTEGER},
      supplier_no = #{supplierNo,jdbcType=VARCHAR},
      supplier_warehouse_code = #{supplierWarehouseCode,jdbcType=VARCHAR},
      expiry_date = #{expiryDate,jdbcType=TIMESTAMP},
      purchase_org = #{purchaseOrg,jdbcType=VARCHAR},
      purchase_warehouse_code = #{purchaseWarehouseCode,jdbcType=VARCHAR},
      purchase_company_code = #{purchaseCompanyCode,jdbcType=VARCHAR},
      emp_code = #{empCode,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>