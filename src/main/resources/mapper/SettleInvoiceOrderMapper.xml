<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SettleInvoiceOrderMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SettleInvoiceOrder">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="invoice_order_no" jdbcType="VARCHAR" property="invoiceOrderNo" />
    <result column="invoice_code" jdbcType="VARCHAR" property="invoiceCode" />
    <result column="invoice_no" jdbcType="VARCHAR" property="invoiceNo" />
    <result column="settle_order_no" jdbcType="VARCHAR" property="settleOrderNo" />
    <result column="invoice_class" jdbcType="TINYINT" property="invoiceClass" />
    <result column="invoice_type" jdbcType="TINYINT" property="invoiceType" />
    <result column="invoice_source" jdbcType="TINYINT" property="invoiceSource" />
    <result column="invoice_group" jdbcType="TINYINT" property="invoiceGroup" />
    <result column="relate_settle_order" jdbcType="TINYINT" property="relateSettleOrder" />
    <result column="invoice_date" jdbcType="DATE" property="invoiceDate" />
    <result column="invoice_status" jdbcType="TINYINT" property="invoiceStatus" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="supplier_no" jdbcType="VARCHAR" property="supplierNo" />
    <result column="sales_taxpayer_no" jdbcType="VARCHAR" property="salesTaxpayerNo" />
    <result column="sales_taxpayer" jdbcType="VARCHAR" property="salesTaxpayer" />
    <result column="sales_addr_tel" jdbcType="VARCHAR" property="salesAddrTel" />
    <result column="sales_bank_account" jdbcType="VARCHAR" property="salesBankAccount" />
    <result column="purchase_taxpayer_no" jdbcType="VARCHAR" property="purchaseTaxpayerNo" />
    <result column="purchase_taxpayer" jdbcType="VARCHAR" property="purchaseTaxpayer" />
    <result column="purchase_addr_tel" jdbcType="VARCHAR" property="purchaseAddrTel" />
    <result column="purchase_bank_account" jdbcType="VARCHAR" property="purchaseBankAccount" />
    <result column="no_tax_amount" jdbcType="DECIMAL" property="noTaxAmount" />
    <result column="tax_amount" jdbcType="DECIMAL" property="taxAmount" />
    <result column="price_tax_amount" jdbcType="DECIMAL" property="priceTaxAmount" />
    <result column="deduct_blue_no" jdbcType="VARCHAR" property="deductBlueNo" />
    <result column="deduct_blue_code" jdbcType="VARCHAR" property="deductBlueCode" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="invoice_url" jdbcType="VARCHAR" property="invoiceUrl" />
    <result column="invoice_file_source" jdbcType="TINYINT" property="invoiceFileSource" />
    <result column="sale_list_source" jdbcType="TINYINT" property="saleListSource" />
    <result column="sale_list_able" jdbcType="TINYINT" property="saleListAble" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, invoice_order_no, invoice_code, invoice_no, settle_order_no, invoice_class, invoice_type, 
    invoice_source, invoice_group, relate_settle_order, invoice_date, invoice_status, 
    org_id, company_code, org_name, supplier_no, sales_taxpayer_no, sales_taxpayer, sales_addr_tel, 
    sales_bank_account, purchase_taxpayer_no, purchase_taxpayer, purchase_addr_tel, purchase_bank_account, 
    no_tax_amount, tax_amount, price_tax_amount, deduct_blue_no, deduct_blue_code, memo, 
    invoice_url, invoice_file_source, sale_list_source, sale_list_able, `status`, gmt_create, 
    gmt_update, extend, version, created_by, created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SettleInvoiceOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from settle_invoice_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from settle_invoice_order
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from settle_invoice_order
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SettleInvoiceOrderExample">
    delete from settle_invoice_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SettleInvoiceOrder">
    insert into settle_invoice_order (id, invoice_order_no, invoice_code, 
      invoice_no, settle_order_no, invoice_class, 
      invoice_type, invoice_source, invoice_group, 
      relate_settle_order, invoice_date, invoice_status, 
      org_id, company_code, org_name, 
      supplier_no, sales_taxpayer_no, sales_taxpayer, 
      sales_addr_tel, sales_bank_account, purchase_taxpayer_no, 
      purchase_taxpayer, purchase_addr_tel, purchase_bank_account, 
      no_tax_amount, tax_amount, price_tax_amount, 
      deduct_blue_no, deduct_blue_code, memo, 
      invoice_url, invoice_file_source, sale_list_source, 
      sale_list_able, `status`, gmt_create, 
      gmt_update, extend, version, 
      created_by, created_name, updated_by, 
      updated_name)
    values (#{id,jdbcType=INTEGER}, #{invoiceOrderNo,jdbcType=VARCHAR}, #{invoiceCode,jdbcType=VARCHAR}, 
      #{invoiceNo,jdbcType=VARCHAR}, #{settleOrderNo,jdbcType=VARCHAR}, #{invoiceClass,jdbcType=TINYINT}, 
      #{invoiceType,jdbcType=TINYINT}, #{invoiceSource,jdbcType=TINYINT}, #{invoiceGroup,jdbcType=TINYINT}, 
      #{relateSettleOrder,jdbcType=TINYINT}, #{invoiceDate,jdbcType=DATE}, #{invoiceStatus,jdbcType=TINYINT}, 
      #{orgId,jdbcType=BIGINT}, #{companyCode,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR}, 
      #{supplierNo,jdbcType=VARCHAR}, #{salesTaxpayerNo,jdbcType=VARCHAR}, #{salesTaxpayer,jdbcType=VARCHAR}, 
      #{salesAddrTel,jdbcType=VARCHAR}, #{salesBankAccount,jdbcType=VARCHAR}, #{purchaseTaxpayerNo,jdbcType=VARCHAR}, 
      #{purchaseTaxpayer,jdbcType=VARCHAR}, #{purchaseAddrTel,jdbcType=VARCHAR}, #{purchaseBankAccount,jdbcType=VARCHAR}, 
      #{noTaxAmount,jdbcType=DECIMAL}, #{taxAmount,jdbcType=DECIMAL}, #{priceTaxAmount,jdbcType=DECIMAL}, 
      #{deductBlueNo,jdbcType=VARCHAR}, #{deductBlueCode,jdbcType=VARCHAR}, #{memo,jdbcType=VARCHAR}, 
      #{invoiceUrl,jdbcType=VARCHAR}, #{invoiceFileSource,jdbcType=TINYINT}, #{saleListSource,jdbcType=TINYINT}, 
      #{saleListAble,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, 
      #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SettleInvoiceOrder">
    insert into settle_invoice_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="invoiceOrderNo != null">
        invoice_order_no,
      </if>
      <if test="invoiceCode != null">
        invoice_code,
      </if>
      <if test="invoiceNo != null">
        invoice_no,
      </if>
      <if test="settleOrderNo != null">
        settle_order_no,
      </if>
      <if test="invoiceClass != null">
        invoice_class,
      </if>
      <if test="invoiceType != null">
        invoice_type,
      </if>
      <if test="invoiceSource != null">
        invoice_source,
      </if>
      <if test="invoiceGroup != null">
        invoice_group,
      </if>
      <if test="relateSettleOrder != null">
        relate_settle_order,
      </if>
      <if test="invoiceDate != null">
        invoice_date,
      </if>
      <if test="invoiceStatus != null">
        invoice_status,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="supplierNo != null">
        supplier_no,
      </if>
      <if test="salesTaxpayerNo != null">
        sales_taxpayer_no,
      </if>
      <if test="salesTaxpayer != null">
        sales_taxpayer,
      </if>
      <if test="salesAddrTel != null">
        sales_addr_tel,
      </if>
      <if test="salesBankAccount != null">
        sales_bank_account,
      </if>
      <if test="purchaseTaxpayerNo != null">
        purchase_taxpayer_no,
      </if>
      <if test="purchaseTaxpayer != null">
        purchase_taxpayer,
      </if>
      <if test="purchaseAddrTel != null">
        purchase_addr_tel,
      </if>
      <if test="purchaseBankAccount != null">
        purchase_bank_account,
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount,
      </if>
      <if test="taxAmount != null">
        tax_amount,
      </if>
      <if test="priceTaxAmount != null">
        price_tax_amount,
      </if>
      <if test="deductBlueNo != null">
        deduct_blue_no,
      </if>
      <if test="deductBlueCode != null">
        deduct_blue_code,
      </if>
      <if test="memo != null">
        memo,
      </if>
      <if test="invoiceUrl != null">
        invoice_url,
      </if>
      <if test="invoiceFileSource != null">
        invoice_file_source,
      </if>
      <if test="saleListSource != null">
        sale_list_source,
      </if>
      <if test="saleListAble != null">
        sale_list_able,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="invoiceOrderNo != null">
        #{invoiceOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceCode != null">
        #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNo != null">
        #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="settleOrderNo != null">
        #{settleOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceClass != null">
        #{invoiceClass,jdbcType=TINYINT},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=TINYINT},
      </if>
      <if test="invoiceSource != null">
        #{invoiceSource,jdbcType=TINYINT},
      </if>
      <if test="invoiceGroup != null">
        #{invoiceGroup,jdbcType=TINYINT},
      </if>
      <if test="relateSettleOrder != null">
        #{relateSettleOrder,jdbcType=TINYINT},
      </if>
      <if test="invoiceDate != null">
        #{invoiceDate,jdbcType=DATE},
      </if>
      <if test="invoiceStatus != null">
        #{invoiceStatus,jdbcType=TINYINT},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null">
        #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="salesTaxpayerNo != null">
        #{salesTaxpayerNo,jdbcType=VARCHAR},
      </if>
      <if test="salesTaxpayer != null">
        #{salesTaxpayer,jdbcType=VARCHAR},
      </if>
      <if test="salesAddrTel != null">
        #{salesAddrTel,jdbcType=VARCHAR},
      </if>
      <if test="salesBankAccount != null">
        #{salesBankAccount,jdbcType=VARCHAR},
      </if>
      <if test="purchaseTaxpayerNo != null">
        #{purchaseTaxpayerNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseTaxpayer != null">
        #{purchaseTaxpayer,jdbcType=VARCHAR},
      </if>
      <if test="purchaseAddrTel != null">
        #{purchaseAddrTel,jdbcType=VARCHAR},
      </if>
      <if test="purchaseBankAccount != null">
        #{purchaseBankAccount,jdbcType=VARCHAR},
      </if>
      <if test="noTaxAmount != null">
        #{noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="priceTaxAmount != null">
        #{priceTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="deductBlueNo != null">
        #{deductBlueNo,jdbcType=VARCHAR},
      </if>
      <if test="deductBlueCode != null">
        #{deductBlueCode,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceUrl != null">
        #{invoiceUrl,jdbcType=VARCHAR},
      </if>
      <if test="invoiceFileSource != null">
        #{invoiceFileSource,jdbcType=TINYINT},
      </if>
      <if test="saleListSource != null">
        #{saleListSource,jdbcType=TINYINT},
      </if>
      <if test="saleListAble != null">
        #{saleListAble,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SettleInvoiceOrderExample" resultType="java.lang.Long">
    select count(*) from settle_invoice_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update settle_invoice_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.invoiceOrderNo != null">
        invoice_order_no = #{record.invoiceOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceCode != null">
        invoice_code = #{record.invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceNo != null">
        invoice_no = #{record.invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="record.settleOrderNo != null">
        settle_order_no = #{record.settleOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceClass != null">
        invoice_class = #{record.invoiceClass,jdbcType=TINYINT},
      </if>
      <if test="record.invoiceType != null">
        invoice_type = #{record.invoiceType,jdbcType=TINYINT},
      </if>
      <if test="record.invoiceSource != null">
        invoice_source = #{record.invoiceSource,jdbcType=TINYINT},
      </if>
      <if test="record.invoiceGroup != null">
        invoice_group = #{record.invoiceGroup,jdbcType=TINYINT},
      </if>
      <if test="record.relateSettleOrder != null">
        relate_settle_order = #{record.relateSettleOrder,jdbcType=TINYINT},
      </if>
      <if test="record.invoiceDate != null">
        invoice_date = #{record.invoiceDate,jdbcType=DATE},
      </if>
      <if test="record.invoiceStatus != null">
        invoice_status = #{record.invoiceStatus,jdbcType=TINYINT},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierNo != null">
        supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="record.salesTaxpayerNo != null">
        sales_taxpayer_no = #{record.salesTaxpayerNo,jdbcType=VARCHAR},
      </if>
      <if test="record.salesTaxpayer != null">
        sales_taxpayer = #{record.salesTaxpayer,jdbcType=VARCHAR},
      </if>
      <if test="record.salesAddrTel != null">
        sales_addr_tel = #{record.salesAddrTel,jdbcType=VARCHAR},
      </if>
      <if test="record.salesBankAccount != null">
        sales_bank_account = #{record.salesBankAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseTaxpayerNo != null">
        purchase_taxpayer_no = #{record.purchaseTaxpayerNo,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseTaxpayer != null">
        purchase_taxpayer = #{record.purchaseTaxpayer,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseAddrTel != null">
        purchase_addr_tel = #{record.purchaseAddrTel,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseBankAccount != null">
        purchase_bank_account = #{record.purchaseBankAccount,jdbcType=VARCHAR},
      </if>
      <if test="record.noTaxAmount != null">
        no_tax_amount = #{record.noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.taxAmount != null">
        tax_amount = #{record.taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.priceTaxAmount != null">
        price_tax_amount = #{record.priceTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.deductBlueNo != null">
        deduct_blue_no = #{record.deductBlueNo,jdbcType=VARCHAR},
      </if>
      <if test="record.deductBlueCode != null">
        deduct_blue_code = #{record.deductBlueCode,jdbcType=VARCHAR},
      </if>
      <if test="record.memo != null">
        memo = #{record.memo,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceUrl != null">
        invoice_url = #{record.invoiceUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceFileSource != null">
        invoice_file_source = #{record.invoiceFileSource,jdbcType=TINYINT},
      </if>
      <if test="record.saleListSource != null">
        sale_list_source = #{record.saleListSource,jdbcType=TINYINT},
      </if>
      <if test="record.saleListAble != null">
        sale_list_able = #{record.saleListAble,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update settle_invoice_order
    set id = #{record.id,jdbcType=INTEGER},
      invoice_order_no = #{record.invoiceOrderNo,jdbcType=VARCHAR},
      invoice_code = #{record.invoiceCode,jdbcType=VARCHAR},
      invoice_no = #{record.invoiceNo,jdbcType=VARCHAR},
      settle_order_no = #{record.settleOrderNo,jdbcType=VARCHAR},
      invoice_class = #{record.invoiceClass,jdbcType=TINYINT},
      invoice_type = #{record.invoiceType,jdbcType=TINYINT},
      invoice_source = #{record.invoiceSource,jdbcType=TINYINT},
      invoice_group = #{record.invoiceGroup,jdbcType=TINYINT},
      relate_settle_order = #{record.relateSettleOrder,jdbcType=TINYINT},
      invoice_date = #{record.invoiceDate,jdbcType=DATE},
      invoice_status = #{record.invoiceStatus,jdbcType=TINYINT},
      org_id = #{record.orgId,jdbcType=BIGINT},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      sales_taxpayer_no = #{record.salesTaxpayerNo,jdbcType=VARCHAR},
      sales_taxpayer = #{record.salesTaxpayer,jdbcType=VARCHAR},
      sales_addr_tel = #{record.salesAddrTel,jdbcType=VARCHAR},
      sales_bank_account = #{record.salesBankAccount,jdbcType=VARCHAR},
      purchase_taxpayer_no = #{record.purchaseTaxpayerNo,jdbcType=VARCHAR},
      purchase_taxpayer = #{record.purchaseTaxpayer,jdbcType=VARCHAR},
      purchase_addr_tel = #{record.purchaseAddrTel,jdbcType=VARCHAR},
      purchase_bank_account = #{record.purchaseBankAccount,jdbcType=VARCHAR},
      no_tax_amount = #{record.noTaxAmount,jdbcType=DECIMAL},
      tax_amount = #{record.taxAmount,jdbcType=DECIMAL},
      price_tax_amount = #{record.priceTaxAmount,jdbcType=DECIMAL},
      deduct_blue_no = #{record.deductBlueNo,jdbcType=VARCHAR},
      deduct_blue_code = #{record.deductBlueCode,jdbcType=VARCHAR},
      memo = #{record.memo,jdbcType=VARCHAR},
      invoice_url = #{record.invoiceUrl,jdbcType=VARCHAR},
      invoice_file_source = #{record.invoiceFileSource,jdbcType=TINYINT},
      sale_list_source = #{record.saleListSource,jdbcType=TINYINT},
      sale_list_able = #{record.saleListAble,jdbcType=TINYINT},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SettleInvoiceOrder">
    update settle_invoice_order
    <set>
      <if test="invoiceOrderNo != null">
        invoice_order_no = #{invoiceOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceCode != null">
        invoice_code = #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNo != null">
        invoice_no = #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="settleOrderNo != null">
        settle_order_no = #{settleOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceClass != null">
        invoice_class = #{invoiceClass,jdbcType=TINYINT},
      </if>
      <if test="invoiceType != null">
        invoice_type = #{invoiceType,jdbcType=TINYINT},
      </if>
      <if test="invoiceSource != null">
        invoice_source = #{invoiceSource,jdbcType=TINYINT},
      </if>
      <if test="invoiceGroup != null">
        invoice_group = #{invoiceGroup,jdbcType=TINYINT},
      </if>
      <if test="relateSettleOrder != null">
        relate_settle_order = #{relateSettleOrder,jdbcType=TINYINT},
      </if>
      <if test="invoiceDate != null">
        invoice_date = #{invoiceDate,jdbcType=DATE},
      </if>
      <if test="invoiceStatus != null">
        invoice_status = #{invoiceStatus,jdbcType=TINYINT},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null">
        supplier_no = #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="salesTaxpayerNo != null">
        sales_taxpayer_no = #{salesTaxpayerNo,jdbcType=VARCHAR},
      </if>
      <if test="salesTaxpayer != null">
        sales_taxpayer = #{salesTaxpayer,jdbcType=VARCHAR},
      </if>
      <if test="salesAddrTel != null">
        sales_addr_tel = #{salesAddrTel,jdbcType=VARCHAR},
      </if>
      <if test="salesBankAccount != null">
        sales_bank_account = #{salesBankAccount,jdbcType=VARCHAR},
      </if>
      <if test="purchaseTaxpayerNo != null">
        purchase_taxpayer_no = #{purchaseTaxpayerNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseTaxpayer != null">
        purchase_taxpayer = #{purchaseTaxpayer,jdbcType=VARCHAR},
      </if>
      <if test="purchaseAddrTel != null">
        purchase_addr_tel = #{purchaseAddrTel,jdbcType=VARCHAR},
      </if>
      <if test="purchaseBankAccount != null">
        purchase_bank_account = #{purchaseBankAccount,jdbcType=VARCHAR},
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount = #{noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        tax_amount = #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="priceTaxAmount != null">
        price_tax_amount = #{priceTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="deductBlueNo != null">
        deduct_blue_no = #{deductBlueNo,jdbcType=VARCHAR},
      </if>
      <if test="deductBlueCode != null">
        deduct_blue_code = #{deductBlueCode,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceUrl != null">
        invoice_url = #{invoiceUrl,jdbcType=VARCHAR},
      </if>
      <if test="invoiceFileSource != null">
        invoice_file_source = #{invoiceFileSource,jdbcType=TINYINT},
      </if>
      <if test="saleListSource != null">
        sale_list_source = #{saleListSource,jdbcType=TINYINT},
      </if>
      <if test="saleListAble != null">
        sale_list_able = #{saleListAble,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SettleInvoiceOrder">
    update settle_invoice_order
    set invoice_order_no = #{invoiceOrderNo,jdbcType=VARCHAR},
      invoice_code = #{invoiceCode,jdbcType=VARCHAR},
      invoice_no = #{invoiceNo,jdbcType=VARCHAR},
      settle_order_no = #{settleOrderNo,jdbcType=VARCHAR},
      invoice_class = #{invoiceClass,jdbcType=TINYINT},
      invoice_type = #{invoiceType,jdbcType=TINYINT},
      invoice_source = #{invoiceSource,jdbcType=TINYINT},
      invoice_group = #{invoiceGroup,jdbcType=TINYINT},
      relate_settle_order = #{relateSettleOrder,jdbcType=TINYINT},
      invoice_date = #{invoiceDate,jdbcType=DATE},
      invoice_status = #{invoiceStatus,jdbcType=TINYINT},
      org_id = #{orgId,jdbcType=BIGINT},
      company_code = #{companyCode,jdbcType=VARCHAR},
      org_name = #{orgName,jdbcType=VARCHAR},
      supplier_no = #{supplierNo,jdbcType=VARCHAR},
      sales_taxpayer_no = #{salesTaxpayerNo,jdbcType=VARCHAR},
      sales_taxpayer = #{salesTaxpayer,jdbcType=VARCHAR},
      sales_addr_tel = #{salesAddrTel,jdbcType=VARCHAR},
      sales_bank_account = #{salesBankAccount,jdbcType=VARCHAR},
      purchase_taxpayer_no = #{purchaseTaxpayerNo,jdbcType=VARCHAR},
      purchase_taxpayer = #{purchaseTaxpayer,jdbcType=VARCHAR},
      purchase_addr_tel = #{purchaseAddrTel,jdbcType=VARCHAR},
      purchase_bank_account = #{purchaseBankAccount,jdbcType=VARCHAR},
      no_tax_amount = #{noTaxAmount,jdbcType=DECIMAL},
      tax_amount = #{taxAmount,jdbcType=DECIMAL},
      price_tax_amount = #{priceTaxAmount,jdbcType=DECIMAL},
      deduct_blue_no = #{deductBlueNo,jdbcType=VARCHAR},
      deduct_blue_code = #{deductBlueCode,jdbcType=VARCHAR},
      memo = #{memo,jdbcType=VARCHAR},
      invoice_url = #{invoiceUrl,jdbcType=VARCHAR},
      invoice_file_source = #{invoiceFileSource,jdbcType=TINYINT},
      sale_list_source = #{saleListSource,jdbcType=TINYINT},
      sale_list_able = #{saleListAble,jdbcType=TINYINT},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>