<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.HangingNetDirectoryMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.HangingNetDirectory">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="goods_variety_large" jdbcType="VARCHAR" property="goodsVarietyLarge" />
    <result column="goods_variety_middle" jdbcType="VARCHAR" property="goodsVarietyMiddle" />
    <result column="push_level" jdbcType="VARCHAR" property="pushLevel" />
    <result column="hanging_status" jdbcType="TINYINT" property="hangingStatus" />
    <result column="execute_code" jdbcType="VARCHAR" property="executeCode" />
    <result column="forbid_start_date" jdbcType="TIMESTAMP" property="forbidStartDate" />
    <result column="forbid_end_date" jdbcType="TIMESTAMP" property="forbidEndDate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="creater_org_id" jdbcType="BIGINT" property="createrOrgId" />
    <result column="creater_org_name" jdbcType="VARCHAR" property="createrOrgName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="updater_org_id" jdbcType="BIGINT" property="updaterOrgId" />
    <result column="updater_org_name" jdbcType="VARCHAR" property="updaterOrgName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, org_id, org_name, type, goods_code, bar_code, goods_common_name, goods_name, 
    description, approval_number, specifications, dosage_form, manufacturer, goods_variety_large, 
    goods_variety_middle, push_level, hanging_status, execute_code, forbid_start_date, 
    forbid_end_date, status, gmt_create, gmt_update, extend, version, created_by, created_name, 
    creater_org_id, creater_org_name, updated_by, updated_name, updater_org_id, updater_org_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.HangingNetDirectoryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from hanging_net_directory
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from hanging_net_directory
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from hanging_net_directory
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.HangingNetDirectoryExample">
    delete from hanging_net_directory
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.HangingNetDirectory">
    insert into hanging_net_directory (id, org_id, org_name, 
      type, goods_code, bar_code, 
      goods_common_name, goods_name, description, 
      approval_number, specifications, dosage_form, 
      manufacturer, goods_variety_large, goods_variety_middle, 
      push_level, hanging_status, execute_code, 
      forbid_start_date, forbid_end_date, status, 
      gmt_create, gmt_update, extend, 
      version, created_by, created_name, 
      creater_org_id, creater_org_name, updated_by, 
      updated_name, updater_org_id, updater_org_name
      )
    values (#{id,jdbcType=BIGINT}, #{orgId,jdbcType=BIGINT}, #{orgName,jdbcType=VARCHAR}, 
      #{type,jdbcType=TINYINT}, #{goodsCode,jdbcType=VARCHAR}, #{barCode,jdbcType=VARCHAR}, 
      #{goodsCommonName,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{approvalNumber,jdbcType=VARCHAR}, #{specifications,jdbcType=VARCHAR}, #{dosageForm,jdbcType=VARCHAR}, 
      #{manufacturer,jdbcType=VARCHAR}, #{goodsVarietyLarge,jdbcType=VARCHAR}, #{goodsVarietyMiddle,jdbcType=VARCHAR}, 
      #{pushLevel,jdbcType=VARCHAR}, #{hangingStatus,jdbcType=TINYINT}, #{executeCode,jdbcType=VARCHAR}, 
      #{forbidStartDate,jdbcType=TIMESTAMP}, #{forbidEndDate,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, 
      #{createrOrgId,jdbcType=BIGINT}, #{createrOrgName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, 
      #{updatedName,jdbcType=VARCHAR}, #{updaterOrgId,jdbcType=BIGINT}, #{updaterOrgName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.HangingNetDirectory">
    insert into hanging_net_directory
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="goodsCode != null">
        goods_code,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="goodsCommonName != null">
        goods_common_name,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="approvalNumber != null">
        approval_number,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="dosageForm != null">
        dosage_form,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="goodsVarietyLarge != null">
        goods_variety_large,
      </if>
      <if test="goodsVarietyMiddle != null">
        goods_variety_middle,
      </if>
      <if test="pushLevel != null">
        push_level,
      </if>
      <if test="hangingStatus != null">
        hanging_status,
      </if>
      <if test="executeCode != null">
        execute_code,
      </if>
      <if test="forbidStartDate != null">
        forbid_start_date,
      </if>
      <if test="forbidEndDate != null">
        forbid_end_date,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="createrOrgId != null">
        creater_org_id,
      </if>
      <if test="createrOrgName != null">
        creater_org_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
      <if test="updaterOrgId != null">
        updater_org_id,
      </if>
      <if test="updaterOrgName != null">
        updater_org_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="goodsCode != null">
        #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="approvalNumber != null">
        #{approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="goodsVarietyLarge != null">
        #{goodsVarietyLarge,jdbcType=VARCHAR},
      </if>
      <if test="goodsVarietyMiddle != null">
        #{goodsVarietyMiddle,jdbcType=VARCHAR},
      </if>
      <if test="pushLevel != null">
        #{pushLevel,jdbcType=VARCHAR},
      </if>
      <if test="hangingStatus != null">
        #{hangingStatus,jdbcType=TINYINT},
      </if>
      <if test="executeCode != null">
        #{executeCode,jdbcType=VARCHAR},
      </if>
      <if test="forbidStartDate != null">
        #{forbidStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="forbidEndDate != null">
        #{forbidEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="createrOrgId != null">
        #{createrOrgId,jdbcType=BIGINT},
      </if>
      <if test="createrOrgName != null">
        #{createrOrgName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="updaterOrgId != null">
        #{updaterOrgId,jdbcType=BIGINT},
      </if>
      <if test="updaterOrgName != null">
        #{updaterOrgName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.HangingNetDirectoryExample" resultType="java.lang.Long">
    select count(*) from hanging_net_directory
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update hanging_net_directory
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=TINYINT},
      </if>
      <if test="record.goodsCode != null">
        goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCommonName != null">
        goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.approvalNumber != null">
        approval_number = #{record.approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.dosageForm != null">
        dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsVarietyLarge != null">
        goods_variety_large = #{record.goodsVarietyLarge,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsVarietyMiddle != null">
        goods_variety_middle = #{record.goodsVarietyMiddle,jdbcType=VARCHAR},
      </if>
      <if test="record.pushLevel != null">
        push_level = #{record.pushLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.hangingStatus != null">
        hanging_status = #{record.hangingStatus,jdbcType=TINYINT},
      </if>
      <if test="record.executeCode != null">
        execute_code = #{record.executeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.forbidStartDate != null">
        forbid_start_date = #{record.forbidStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.forbidEndDate != null">
        forbid_end_date = #{record.forbidEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.createrOrgId != null">
        creater_org_id = #{record.createrOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.createrOrgName != null">
        creater_org_name = #{record.createrOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
      <if test="record.updaterOrgId != null">
        updater_org_id = #{record.updaterOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.updaterOrgName != null">
        updater_org_name = #{record.updaterOrgName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update hanging_net_directory
    set id = #{record.id,jdbcType=BIGINT},
      org_id = #{record.orgId,jdbcType=BIGINT},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=TINYINT},
      goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      approval_number = #{record.approvalNumber,jdbcType=VARCHAR},
      specifications = #{record.specifications,jdbcType=VARCHAR},
      dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      goods_variety_large = #{record.goodsVarietyLarge,jdbcType=VARCHAR},
      goods_variety_middle = #{record.goodsVarietyMiddle,jdbcType=VARCHAR},
      push_level = #{record.pushLevel,jdbcType=VARCHAR},
      hanging_status = #{record.hangingStatus,jdbcType=TINYINT},
      execute_code = #{record.executeCode,jdbcType=VARCHAR},
      forbid_start_date = #{record.forbidStartDate,jdbcType=TIMESTAMP},
      forbid_end_date = #{record.forbidEndDate,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      creater_org_id = #{record.createrOrgId,jdbcType=BIGINT},
      creater_org_name = #{record.createrOrgName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR},
      updater_org_id = #{record.updaterOrgId,jdbcType=BIGINT},
      updater_org_name = #{record.updaterOrgName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.HangingNetDirectory">
    update hanging_net_directory
    <set>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="goodsCode != null">
        goods_code = #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="approvalNumber != null">
        approval_number = #{approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        dosage_form = #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="goodsVarietyLarge != null">
        goods_variety_large = #{goodsVarietyLarge,jdbcType=VARCHAR},
      </if>
      <if test="goodsVarietyMiddle != null">
        goods_variety_middle = #{goodsVarietyMiddle,jdbcType=VARCHAR},
      </if>
      <if test="pushLevel != null">
        push_level = #{pushLevel,jdbcType=VARCHAR},
      </if>
      <if test="hangingStatus != null">
        hanging_status = #{hangingStatus,jdbcType=TINYINT},
      </if>
      <if test="executeCode != null">
        execute_code = #{executeCode,jdbcType=VARCHAR},
      </if>
      <if test="forbidStartDate != null">
        forbid_start_date = #{forbidStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="forbidEndDate != null">
        forbid_end_date = #{forbidEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="createrOrgId != null">
        creater_org_id = #{createrOrgId,jdbcType=BIGINT},
      </if>
      <if test="createrOrgName != null">
        creater_org_name = #{createrOrgName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="updaterOrgId != null">
        updater_org_id = #{updaterOrgId,jdbcType=BIGINT},
      </if>
      <if test="updaterOrgName != null">
        updater_org_name = #{updaterOrgName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.HangingNetDirectory">
    update hanging_net_directory
    set org_id = #{orgId,jdbcType=BIGINT},
      org_name = #{orgName,jdbcType=VARCHAR},
      type = #{type,jdbcType=TINYINT},
      goods_code = #{goodsCode,jdbcType=VARCHAR},
      bar_code = #{barCode,jdbcType=VARCHAR},
      goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      approval_number = #{approvalNumber,jdbcType=VARCHAR},
      specifications = #{specifications,jdbcType=VARCHAR},
      dosage_form = #{dosageForm,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      goods_variety_large = #{goodsVarietyLarge,jdbcType=VARCHAR},
      goods_variety_middle = #{goodsVarietyMiddle,jdbcType=VARCHAR},
      push_level = #{pushLevel,jdbcType=VARCHAR},
      hanging_status = #{hangingStatus,jdbcType=TINYINT},
      execute_code = #{executeCode,jdbcType=VARCHAR},
      forbid_start_date = #{forbidStartDate,jdbcType=TIMESTAMP},
      forbid_end_date = #{forbidEndDate,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      creater_org_id = #{createrOrgId,jdbcType=BIGINT},
      creater_org_name = #{createrOrgName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR},
      updater_org_id = #{updaterOrgId,jdbcType=BIGINT},
      updater_org_name = #{updaterOrgName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>