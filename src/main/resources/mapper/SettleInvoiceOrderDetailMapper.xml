<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SettleInvoiceOrderDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SettleInvoiceOrderDetail">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="invoice_order_no" jdbcType="VARCHAR" property="invoiceOrderNo" />
    <result column="row_no" jdbcType="VARCHAR" property="rowNo" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="quantity" jdbcType="DECIMAL" property="quantity" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="no_tax_price" jdbcType="DECIMAL" property="noTaxPrice" />
    <result column="no_tax_amount" jdbcType="DECIMAL" property="noTaxAmount" />
    <result column="tax_amount" jdbcType="DECIMAL" property="taxAmount" />
    <result column="tax_rate" jdbcType="DECIMAL" property="taxRate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, invoice_order_no, row_no, goods_code, goods_name, specifications, quantity, unit, 
    no_tax_price, no_tax_amount, tax_amount, tax_rate, status, gmt_create, gmt_update, 
    extend, version, created_by, created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SettleInvoiceOrderDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from settle_invoice_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from settle_invoice_order_detail
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from settle_invoice_order_detail
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SettleInvoiceOrderDetailExample">
    delete from settle_invoice_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SettleInvoiceOrderDetail">
    insert into settle_invoice_order_detail (id, invoice_order_no, row_no, 
      goods_code, goods_name, specifications, 
      quantity, unit, no_tax_price, 
      no_tax_amount, tax_amount, tax_rate, 
      status, gmt_create, gmt_update, 
      extend, version, created_by, 
      created_name, updated_by, updated_name
      )
    values (#{id,jdbcType=INTEGER}, #{invoiceOrderNo,jdbcType=VARCHAR}, #{rowNo,jdbcType=VARCHAR}, 
      #{goodsCode,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, #{specifications,jdbcType=VARCHAR}, 
      #{quantity,jdbcType=DECIMAL}, #{unit,jdbcType=VARCHAR}, #{noTaxPrice,jdbcType=DECIMAL}, 
      #{noTaxAmount,jdbcType=DECIMAL}, #{taxAmount,jdbcType=DECIMAL}, #{taxRate,jdbcType=DECIMAL}, 
      #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, 
      #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, 
      #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SettleInvoiceOrderDetail">
    insert into settle_invoice_order_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="invoiceOrderNo != null">
        invoice_order_no,
      </if>
      <if test="rowNo != null">
        row_no,
      </if>
      <if test="goodsCode != null">
        goods_code,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="noTaxPrice != null">
        no_tax_price,
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount,
      </if>
      <if test="taxAmount != null">
        tax_amount,
      </if>
      <if test="taxRate != null">
        tax_rate,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="invoiceOrderNo != null">
        #{invoiceOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="rowNo != null">
        #{rowNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=DECIMAL},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="noTaxPrice != null">
        #{noTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="noTaxAmount != null">
        #{noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SettleInvoiceOrderDetailExample" resultType="java.lang.Long">
    select count(*) from settle_invoice_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update settle_invoice_order_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.invoiceOrderNo != null">
        invoice_order_no = #{record.invoiceOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.rowNo != null">
        row_no = #{record.rowNo,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCode != null">
        goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.quantity != null">
        quantity = #{record.quantity,jdbcType=DECIMAL},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.noTaxPrice != null">
        no_tax_price = #{record.noTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.noTaxAmount != null">
        no_tax_amount = #{record.noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.taxAmount != null">
        tax_amount = #{record.taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.taxRate != null">
        tax_rate = #{record.taxRate,jdbcType=DECIMAL},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update settle_invoice_order_detail
    set id = #{record.id,jdbcType=INTEGER},
      invoice_order_no = #{record.invoiceOrderNo,jdbcType=VARCHAR},
      row_no = #{record.rowNo,jdbcType=VARCHAR},
      goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      specifications = #{record.specifications,jdbcType=VARCHAR},
      quantity = #{record.quantity,jdbcType=DECIMAL},
      unit = #{record.unit,jdbcType=VARCHAR},
      no_tax_price = #{record.noTaxPrice,jdbcType=DECIMAL},
      no_tax_amount = #{record.noTaxAmount,jdbcType=DECIMAL},
      tax_amount = #{record.taxAmount,jdbcType=DECIMAL},
      tax_rate = #{record.taxRate,jdbcType=DECIMAL},
      status = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SettleInvoiceOrderDetail">
    update settle_invoice_order_detail
    <set>
      <if test="invoiceOrderNo != null">
        invoice_order_no = #{invoiceOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="rowNo != null">
        row_no = #{rowNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        goods_code = #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=DECIMAL},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="noTaxPrice != null">
        no_tax_price = #{noTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount = #{noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        tax_amount = #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxRate != null">
        tax_rate = #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SettleInvoiceOrderDetail">
    update settle_invoice_order_detail
    set invoice_order_no = #{invoiceOrderNo,jdbcType=VARCHAR},
      row_no = #{rowNo,jdbcType=VARCHAR},
      goods_code = #{goodsCode,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      specifications = #{specifications,jdbcType=VARCHAR},
      quantity = #{quantity,jdbcType=DECIMAL},
      unit = #{unit,jdbcType=VARCHAR},
      no_tax_price = #{noTaxPrice,jdbcType=DECIMAL},
      no_tax_amount = #{noTaxAmount,jdbcType=DECIMAL},
      tax_amount = #{taxAmount,jdbcType=DECIMAL},
      tax_rate = #{taxRate,jdbcType=DECIMAL},
      status = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>