<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmPermAccountSysRelationMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmPermAccountSysRelation">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_no" jdbcType="VARCHAR" property="accountNo" />
    <result column="account_class" jdbcType="INTEGER" property="accountClass" />
    <result column="account_business_type" jdbcType="INTEGER" property="accountBusinessType" />
    <result column="data_expiration_type" jdbcType="INTEGER" property="dataExpirationType" />
    <result column="data_expire_start" jdbcType="TIMESTAMP" property="dataExpireStart" />
    <result column="data_expire_end" jdbcType="TIMESTAMP" property="dataExpireEnd" />
    <result column="purchase_type_scope" jdbcType="VARCHAR" property="purchaseTypeScope" />
    <result column="supplier_salesman_code" jdbcType="VARCHAR" property="supplierSalesmanCode" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_no, account_class, account_business_type, data_expiration_type, data_expire_start, 
    data_expire_end, purchase_type_scope, supplier_salesman_code, gmt_create, gmt_update, 
    status, extend, version
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmPermAccountSysRelationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_perm_account_sys_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_perm_account_sys_relation
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from srm_perm_account_sys_relation
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmPermAccountSysRelationExample">
    delete from srm_perm_account_sys_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmPermAccountSysRelation">
    insert into srm_perm_account_sys_relation (id, account_no, account_class, 
      account_business_type, data_expiration_type, 
      data_expire_start, data_expire_end, purchase_type_scope, 
      supplier_salesman_code, gmt_create, gmt_update, 
      status, extend, version
      )
    values (#{id,jdbcType=INTEGER}, #{accountNo,jdbcType=VARCHAR}, #{accountClass,jdbcType=INTEGER}, 
      #{accountBusinessType,jdbcType=INTEGER}, #{dataExpirationType,jdbcType=INTEGER}, 
      #{dataExpireStart,jdbcType=TIMESTAMP}, #{dataExpireEnd,jdbcType=TIMESTAMP}, #{purchaseTypeScope,jdbcType=VARCHAR}, 
      #{supplierSalesmanCode,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmPermAccountSysRelation">
    insert into srm_perm_account_sys_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="accountNo != null">
        account_no,
      </if>
      <if test="accountClass != null">
        account_class,
      </if>
      <if test="accountBusinessType != null">
        account_business_type,
      </if>
      <if test="dataExpirationType != null">
        data_expiration_type,
      </if>
      <if test="dataExpireStart != null">
        data_expire_start,
      </if>
      <if test="dataExpireEnd != null">
        data_expire_end,
      </if>
      <if test="purchaseTypeScope != null">
        purchase_type_scope,
      </if>
      <if test="supplierSalesmanCode != null">
        supplier_salesman_code,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="accountNo != null">
        #{accountNo,jdbcType=VARCHAR},
      </if>
      <if test="accountClass != null">
        #{accountClass,jdbcType=INTEGER},
      </if>
      <if test="accountBusinessType != null">
        #{accountBusinessType,jdbcType=INTEGER},
      </if>
      <if test="dataExpirationType != null">
        #{dataExpirationType,jdbcType=INTEGER},
      </if>
      <if test="dataExpireStart != null">
        #{dataExpireStart,jdbcType=TIMESTAMP},
      </if>
      <if test="dataExpireEnd != null">
        #{dataExpireEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="purchaseTypeScope != null">
        #{purchaseTypeScope,jdbcType=VARCHAR},
      </if>
      <if test="supplierSalesmanCode != null">
        #{supplierSalesmanCode,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmPermAccountSysRelationExample" resultType="java.lang.Long">
    select count(*) from srm_perm_account_sys_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_perm_account_sys_relation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountNo != null">
        account_no = #{record.accountNo,jdbcType=VARCHAR},
      </if>
      <if test="record.accountClass != null">
        account_class = #{record.accountClass,jdbcType=INTEGER},
      </if>
      <if test="record.accountBusinessType != null">
        account_business_type = #{record.accountBusinessType,jdbcType=INTEGER},
      </if>
      <if test="record.dataExpirationType != null">
        data_expiration_type = #{record.dataExpirationType,jdbcType=INTEGER},
      </if>
      <if test="record.dataExpireStart != null">
        data_expire_start = #{record.dataExpireStart,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dataExpireEnd != null">
        data_expire_end = #{record.dataExpireEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="record.purchaseTypeScope != null">
        purchase_type_scope = #{record.purchaseTypeScope,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierSalesmanCode != null">
        supplier_salesman_code = #{record.supplierSalesmanCode,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_perm_account_sys_relation
    set id = #{record.id,jdbcType=INTEGER},
      account_no = #{record.accountNo,jdbcType=VARCHAR},
      account_class = #{record.accountClass,jdbcType=INTEGER},
      account_business_type = #{record.accountBusinessType,jdbcType=INTEGER},
      data_expiration_type = #{record.dataExpirationType,jdbcType=INTEGER},
      data_expire_start = #{record.dataExpireStart,jdbcType=TIMESTAMP},
      data_expire_end = #{record.dataExpireEnd,jdbcType=TIMESTAMP},
      purchase_type_scope = #{record.purchaseTypeScope,jdbcType=VARCHAR},
      supplier_salesman_code = #{record.supplierSalesmanCode,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmPermAccountSysRelation">
    update srm_perm_account_sys_relation
    <set>
      <if test="accountNo != null">
        account_no = #{accountNo,jdbcType=VARCHAR},
      </if>
      <if test="accountClass != null">
        account_class = #{accountClass,jdbcType=INTEGER},
      </if>
      <if test="accountBusinessType != null">
        account_business_type = #{accountBusinessType,jdbcType=INTEGER},
      </if>
      <if test="dataExpirationType != null">
        data_expiration_type = #{dataExpirationType,jdbcType=INTEGER},
      </if>
      <if test="dataExpireStart != null">
        data_expire_start = #{dataExpireStart,jdbcType=TIMESTAMP},
      </if>
      <if test="dataExpireEnd != null">
        data_expire_end = #{dataExpireEnd,jdbcType=TIMESTAMP},
      </if>
      <if test="purchaseTypeScope != null">
        purchase_type_scope = #{purchaseTypeScope,jdbcType=VARCHAR},
      </if>
      <if test="supplierSalesmanCode != null">
        supplier_salesman_code = #{supplierSalesmanCode,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmPermAccountSysRelation">
    update srm_perm_account_sys_relation
    set account_no = #{accountNo,jdbcType=VARCHAR},
      account_class = #{accountClass,jdbcType=INTEGER},
      account_business_type = #{accountBusinessType,jdbcType=INTEGER},
      data_expiration_type = #{dataExpirationType,jdbcType=INTEGER},
      data_expire_start = #{dataExpireStart,jdbcType=TIMESTAMP},
      data_expire_end = #{dataExpireEnd,jdbcType=TIMESTAMP},
      purchase_type_scope = #{purchaseTypeScope,jdbcType=VARCHAR},
      supplier_salesman_code = #{supplierSalesmanCode,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>