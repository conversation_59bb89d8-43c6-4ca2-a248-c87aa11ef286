<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.PurchaseGoodsListMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.PurchaseGoodsList">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="execute_code" jdbcType="VARCHAR" property="executeCode" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="purchase_type" jdbcType="TINYINT" property="purchaseType" />
    <result column="purchase_mode" jdbcType="TINYINT" property="purchaseMode" />
    <result column="purchase_status" jdbcType="TINYINT" property="purchaseStatus" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="supplier_org_id" jdbcType="VARCHAR" property="supplierOrgId" />
    <result column="supplier_org_name" jdbcType="VARCHAR" property="supplierOrgName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, code, execute_code, org_id, org_name, purchase_type, purchase_mode, purchase_status, 
    goods_code, bar_code, goods_common_name, supplier_org_id, supplier_org_name, status, 
    gmt_create, gmt_update, extend, version
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.PurchaseGoodsListExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from purchase_goods_list
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from purchase_goods_list
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from purchase_goods_list
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.PurchaseGoodsListExample">
    delete from purchase_goods_list
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.PurchaseGoodsList">
    insert into purchase_goods_list (id, code, execute_code, 
      org_id, org_name, purchase_type, 
      purchase_mode, purchase_status, goods_code, 
      bar_code, goods_common_name, supplier_org_id, 
      supplier_org_name, status, gmt_create, 
      gmt_update, extend, version
      )
    values (#{id,jdbcType=INTEGER}, #{code,jdbcType=VARCHAR}, #{executeCode,jdbcType=VARCHAR}, 
      #{orgId,jdbcType=BIGINT}, #{orgName,jdbcType=VARCHAR}, #{purchaseType,jdbcType=TINYINT}, 
      #{purchaseMode,jdbcType=TINYINT}, #{purchaseStatus,jdbcType=TINYINT}, #{goodsCode,jdbcType=VARCHAR}, 
      #{barCode,jdbcType=VARCHAR}, #{goodsCommonName,jdbcType=VARCHAR}, #{supplierOrgId,jdbcType=VARCHAR}, 
      #{supplierOrgName,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.PurchaseGoodsList">
    insert into purchase_goods_list
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="executeCode != null">
        execute_code,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="purchaseType != null">
        purchase_type,
      </if>
      <if test="purchaseMode != null">
        purchase_mode,
      </if>
      <if test="purchaseStatus != null">
        purchase_status,
      </if>
      <if test="goodsCode != null">
        goods_code,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="goodsCommonName != null">
        goods_common_name,
      </if>
      <if test="supplierOrgId != null">
        supplier_org_id,
      </if>
      <if test="supplierOrgName != null">
        supplier_org_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="executeCode != null">
        #{executeCode,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="purchaseType != null">
        #{purchaseType,jdbcType=TINYINT},
      </if>
      <if test="purchaseMode != null">
        #{purchaseMode,jdbcType=TINYINT},
      </if>
      <if test="purchaseStatus != null">
        #{purchaseStatus,jdbcType=TINYINT},
      </if>
      <if test="goodsCode != null">
        #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="supplierOrgId != null">
        #{supplierOrgId,jdbcType=VARCHAR},
      </if>
      <if test="supplierOrgName != null">
        #{supplierOrgName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.PurchaseGoodsListExample" resultType="java.lang.Long">
    select count(*) from purchase_goods_list
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update purchase_goods_list
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.code != null">
        code = #{record.code,jdbcType=VARCHAR},
      </if>
      <if test="record.executeCode != null">
        execute_code = #{record.executeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseType != null">
        purchase_type = #{record.purchaseType,jdbcType=TINYINT},
      </if>
      <if test="record.purchaseMode != null">
        purchase_mode = #{record.purchaseMode,jdbcType=TINYINT},
      </if>
      <if test="record.purchaseStatus != null">
        purchase_status = #{record.purchaseStatus,jdbcType=TINYINT},
      </if>
      <if test="record.goodsCode != null">
        goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCommonName != null">
        goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierOrgId != null">
        supplier_org_id = #{record.supplierOrgId,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierOrgName != null">
        supplier_org_name = #{record.supplierOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update purchase_goods_list
    set id = #{record.id,jdbcType=INTEGER},
      code = #{record.code,jdbcType=VARCHAR},
      execute_code = #{record.executeCode,jdbcType=VARCHAR},
      org_id = #{record.orgId,jdbcType=BIGINT},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      purchase_type = #{record.purchaseType,jdbcType=TINYINT},
      purchase_mode = #{record.purchaseMode,jdbcType=TINYINT},
      purchase_status = #{record.purchaseStatus,jdbcType=TINYINT},
      goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      supplier_org_id = #{record.supplierOrgId,jdbcType=VARCHAR},
      supplier_org_name = #{record.supplierOrgName,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.PurchaseGoodsList">
    update purchase_goods_list
    <set>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="executeCode != null">
        execute_code = #{executeCode,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="purchaseType != null">
        purchase_type = #{purchaseType,jdbcType=TINYINT},
      </if>
      <if test="purchaseMode != null">
        purchase_mode = #{purchaseMode,jdbcType=TINYINT},
      </if>
      <if test="purchaseStatus != null">
        purchase_status = #{purchaseStatus,jdbcType=TINYINT},
      </if>
      <if test="goodsCode != null">
        goods_code = #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="supplierOrgId != null">
        supplier_org_id = #{supplierOrgId,jdbcType=VARCHAR},
      </if>
      <if test="supplierOrgName != null">
        supplier_org_name = #{supplierOrgName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.PurchaseGoodsList">
    update purchase_goods_list
    set code = #{code,jdbcType=VARCHAR},
      execute_code = #{executeCode,jdbcType=VARCHAR},
      org_id = #{orgId,jdbcType=BIGINT},
      org_name = #{orgName,jdbcType=VARCHAR},
      purchase_type = #{purchaseType,jdbcType=TINYINT},
      purchase_mode = #{purchaseMode,jdbcType=TINYINT},
      purchase_status = #{purchaseStatus,jdbcType=TINYINT},
      goods_code = #{goodsCode,jdbcType=VARCHAR},
      bar_code = #{barCode,jdbcType=VARCHAR},
      goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      supplier_org_id = #{supplierOrgId,jdbcType=VARCHAR},
      supplier_org_name = #{supplierOrgName,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>