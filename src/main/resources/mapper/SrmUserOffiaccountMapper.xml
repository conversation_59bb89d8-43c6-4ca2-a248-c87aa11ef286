<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmUserOffiaccountMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmUserOffiaccount">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_no" jdbcType="VARCHAR" property="accountNo" />
    <result column="offi_open_id" jdbcType="VARCHAR" property="offiOpenId" />
    <result column="account_type" jdbcType="TINYINT" property="accountType" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_no, offi_open_id, account_type, `status`, extend, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmUserOffiaccountExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_user_offiaccount
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_user_offiaccount
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from srm_user_offiaccount
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmUserOffiaccountExample">
    delete from srm_user_offiaccount
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmUserOffiaccount">
    insert into srm_user_offiaccount (id, account_no, offi_open_id, 
      account_type, `status`, extend, 
      gmt_create, gmt_update)
    values (#{id,jdbcType=INTEGER}, #{accountNo,jdbcType=VARCHAR}, #{offiOpenId,jdbcType=VARCHAR}, 
      #{accountType,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmUserOffiaccount">
    insert into srm_user_offiaccount
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="accountNo != null">
        account_no,
      </if>
      <if test="offiOpenId != null">
        offi_open_id,
      </if>
      <if test="accountType != null">
        account_type,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="accountNo != null">
        #{accountNo,jdbcType=VARCHAR},
      </if>
      <if test="offiOpenId != null">
        #{offiOpenId,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmUserOffiaccountExample" resultType="java.lang.Long">
    select count(*) from srm_user_offiaccount
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_user_offiaccount
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountNo != null">
        account_no = #{record.accountNo,jdbcType=VARCHAR},
      </if>
      <if test="record.offiOpenId != null">
        offi_open_id = #{record.offiOpenId,jdbcType=VARCHAR},
      </if>
      <if test="record.accountType != null">
        account_type = #{record.accountType,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_user_offiaccount
    set id = #{record.id,jdbcType=INTEGER},
      account_no = #{record.accountNo,jdbcType=VARCHAR},
      offi_open_id = #{record.offiOpenId,jdbcType=VARCHAR},
      account_type = #{record.accountType,jdbcType=TINYINT},
      `status` = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmUserOffiaccount">
    update srm_user_offiaccount
    <set>
      <if test="accountNo != null">
        account_no = #{accountNo,jdbcType=VARCHAR},
      </if>
      <if test="offiOpenId != null">
        offi_open_id = #{offiOpenId,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        account_type = #{accountType,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmUserOffiaccount">
    update srm_user_offiaccount
    set account_no = #{accountNo,jdbcType=VARCHAR},
      offi_open_id = #{offiOpenId,jdbcType=VARCHAR},
      account_type = #{accountType,jdbcType=TINYINT},
      `status` = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>