<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.UnifiedPurchaseOrderGoodsMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.UnifiedPurchaseOrderGoods">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="purchase_order_no" jdbcType="VARCHAR" property="purchaseOrderNo" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="row_no" jdbcType="BIGINT" property="rowNo" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="apply_count" jdbcType="INTEGER" property="applyCount" />
    <result column="receiving_count" jdbcType="INTEGER" property="receivingCount" />
    <result column="habitat" jdbcType="VARCHAR" property="habitat" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="stock_use_ratio" jdbcType="DECIMAL" property="stockUseRatio" />
    <result column="confirm_count" jdbcType="INTEGER" property="confirmCount" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="buying_price" jdbcType="DECIMAL" property="buyingPrice" />
    <result column="apprdocno" jdbcType="VARCHAR" property="apprdocno" />
    <result column="deliver_gather_count" jdbcType="INTEGER" property="deliverGatherCount" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, purchase_order_no, goods_code, bar_code, goods_common_name, goods_name, row_no, 
    specifications, dosage_form, apply_count, receiving_count, habitat, manufacturer, 
    gmt_create, gmt_update, status, extend, version, created_by, update_by, stock_use_ratio, 
    confirm_count, unit, buying_price, apprdocno, deliver_gather_count
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.UnifiedPurchaseOrderGoodsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from unified_purchase_order_goods
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from unified_purchase_order_goods
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from unified_purchase_order_goods
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.UnifiedPurchaseOrderGoodsExample">
    delete from unified_purchase_order_goods
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.UnifiedPurchaseOrderGoods">
    insert into unified_purchase_order_goods (id, purchase_order_no, goods_code, 
      bar_code, goods_common_name, goods_name, 
      row_no, specifications, dosage_form, 
      apply_count, receiving_count, habitat, 
      manufacturer, gmt_create, gmt_update, 
      status, extend, version, 
      created_by, update_by, stock_use_ratio, 
      confirm_count, unit, buying_price, 
      apprdocno, deliver_gather_count)
    values (#{id,jdbcType=INTEGER}, #{purchaseOrderNo,jdbcType=VARCHAR}, #{goodsCode,jdbcType=VARCHAR}, 
      #{barCode,jdbcType=VARCHAR}, #{goodsCommonName,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, 
      #{rowNo,jdbcType=BIGINT}, #{specifications,jdbcType=VARCHAR}, #{dosageForm,jdbcType=VARCHAR}, 
      #{applyCount,jdbcType=INTEGER}, #{receivingCount,jdbcType=INTEGER}, #{habitat,jdbcType=VARCHAR}, 
      #{manufacturer,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, #{stockUseRatio,jdbcType=DECIMAL}, 
      #{confirmCount,jdbcType=INTEGER}, #{unit,jdbcType=VARCHAR}, #{buyingPrice,jdbcType=DECIMAL}, 
      #{apprdocno,jdbcType=VARCHAR}, #{deliverGatherCount,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.UnifiedPurchaseOrderGoods">
    insert into unified_purchase_order_goods
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="purchaseOrderNo != null">
        purchase_order_no,
      </if>
      <if test="goodsCode != null">
        goods_code,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="goodsCommonName != null">
        goods_common_name,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="rowNo != null">
        row_no,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="dosageForm != null">
        dosage_form,
      </if>
      <if test="applyCount != null">
        apply_count,
      </if>
      <if test="receivingCount != null">
        receiving_count,
      </if>
      <if test="habitat != null">
        habitat,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="stockUseRatio != null">
        stock_use_ratio,
      </if>
      <if test="confirmCount != null">
        confirm_count,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="buyingPrice != null">
        buying_price,
      </if>
      <if test="apprdocno != null">
        apprdocno,
      </if>
      <if test="deliverGatherCount != null">
        deliver_gather_count,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="purchaseOrderNo != null">
        #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="rowNo != null">
        #{rowNo,jdbcType=BIGINT},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="applyCount != null">
        #{applyCount,jdbcType=INTEGER},
      </if>
      <if test="receivingCount != null">
        #{receivingCount,jdbcType=INTEGER},
      </if>
      <if test="habitat != null">
        #{habitat,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="stockUseRatio != null">
        #{stockUseRatio,jdbcType=DECIMAL},
      </if>
      <if test="confirmCount != null">
        #{confirmCount,jdbcType=INTEGER},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="buyingPrice != null">
        #{buyingPrice,jdbcType=DECIMAL},
      </if>
      <if test="apprdocno != null">
        #{apprdocno,jdbcType=VARCHAR},
      </if>
      <if test="deliverGatherCount != null">
        #{deliverGatherCount,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.UnifiedPurchaseOrderGoodsExample" resultType="java.lang.Long">
    select count(*) from unified_purchase_order_goods
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update unified_purchase_order_goods
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.purchaseOrderNo != null">
        purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCode != null">
        goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCommonName != null">
        goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.rowNo != null">
        row_no = #{record.rowNo,jdbcType=BIGINT},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.dosageForm != null">
        dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="record.applyCount != null">
        apply_count = #{record.applyCount,jdbcType=INTEGER},
      </if>
      <if test="record.receivingCount != null">
        receiving_count = #{record.receivingCount,jdbcType=INTEGER},
      </if>
      <if test="record.habitat != null">
        habitat = #{record.habitat,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=BIGINT},
      </if>
      <if test="record.stockUseRatio != null">
        stock_use_ratio = #{record.stockUseRatio,jdbcType=DECIMAL},
      </if>
      <if test="record.confirmCount != null">
        confirm_count = #{record.confirmCount,jdbcType=INTEGER},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.buyingPrice != null">
        buying_price = #{record.buyingPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.apprdocno != null">
        apprdocno = #{record.apprdocno,jdbcType=VARCHAR},
      </if>
      <if test="record.deliverGatherCount != null">
        deliver_gather_count = #{record.deliverGatherCount,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update unified_purchase_order_goods
    set id = #{record.id,jdbcType=INTEGER},
      purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      row_no = #{record.rowNo,jdbcType=BIGINT},
      specifications = #{record.specifications,jdbcType=VARCHAR},
      dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      apply_count = #{record.applyCount,jdbcType=INTEGER},
      receiving_count = #{record.receivingCount,jdbcType=INTEGER},
      habitat = #{record.habitat,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      update_by = #{record.updateBy,jdbcType=BIGINT},
      stock_use_ratio = #{record.stockUseRatio,jdbcType=DECIMAL},
      confirm_count = #{record.confirmCount,jdbcType=INTEGER},
      unit = #{record.unit,jdbcType=VARCHAR},
      buying_price = #{record.buyingPrice,jdbcType=DECIMAL},
      apprdocno = #{record.apprdocno,jdbcType=VARCHAR},
      deliver_gather_count = #{record.deliverGatherCount,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.UnifiedPurchaseOrderGoods">
    update unified_purchase_order_goods
    <set>
      <if test="purchaseOrderNo != null">
        purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        goods_code = #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="rowNo != null">
        row_no = #{rowNo,jdbcType=BIGINT},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        dosage_form = #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="applyCount != null">
        apply_count = #{applyCount,jdbcType=INTEGER},
      </if>
      <if test="receivingCount != null">
        receiving_count = #{receivingCount,jdbcType=INTEGER},
      </if>
      <if test="habitat != null">
        habitat = #{habitat,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="stockUseRatio != null">
        stock_use_ratio = #{stockUseRatio,jdbcType=DECIMAL},
      </if>
      <if test="confirmCount != null">
        confirm_count = #{confirmCount,jdbcType=INTEGER},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="buyingPrice != null">
        buying_price = #{buyingPrice,jdbcType=DECIMAL},
      </if>
      <if test="apprdocno != null">
        apprdocno = #{apprdocno,jdbcType=VARCHAR},
      </if>
      <if test="deliverGatherCount != null">
        deliver_gather_count = #{deliverGatherCount,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.UnifiedPurchaseOrderGoods">
    update unified_purchase_order_goods
    set purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      goods_code = #{goodsCode,jdbcType=VARCHAR},
      bar_code = #{barCode,jdbcType=VARCHAR},
      goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      row_no = #{rowNo,jdbcType=BIGINT},
      specifications = #{specifications,jdbcType=VARCHAR},
      dosage_form = #{dosageForm,jdbcType=VARCHAR},
      apply_count = #{applyCount,jdbcType=INTEGER},
      receiving_count = #{receivingCount,jdbcType=INTEGER},
      habitat = #{habitat,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      stock_use_ratio = #{stockUseRatio,jdbcType=DECIMAL},
      confirm_count = #{confirmCount,jdbcType=INTEGER},
      unit = #{unit,jdbcType=VARCHAR},
      buying_price = #{buyingPrice,jdbcType=DECIMAL},
      apprdocno = #{apprdocno,jdbcType=VARCHAR},
      deliver_gather_count = #{deliverGatherCount,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>