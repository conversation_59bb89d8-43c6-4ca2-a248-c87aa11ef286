<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.UnifiedPurchaseApplicationMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.UnifiedPurchaseApplication">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="purchase_application_no" jdbcType="VARCHAR" property="purchaseApplicationNo" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="user_mdm_code" jdbcType="VARCHAR" property="userMdmCode" />
    <result column="document_status" jdbcType="TINYINT" property="documentStatus" />
    <result column="audit_status" jdbcType="TINYINT" property="auditStatus" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="operate_time" jdbcType="TIMESTAMP" property="operateTime" />
    <result column="express_model" jdbcType="INTEGER" property="expressModel" />
    <result column="unified_company_no" jdbcType="VARCHAR" property="unifiedCompanyNo" />
    <result column="unified_company_name" jdbcType="VARCHAR" property="unifiedCompanyName" />
    <result column="supplier_repertory_no" jdbcType="VARCHAR" property="supplierRepertoryNo" />
    <result column="supplier_repertory_name" jdbcType="VARCHAR" property="supplierRepertoryName" />
    <result column="receipt_repertory_name" jdbcType="VARCHAR" property="receiptRepertoryName" />
    <result column="receipt_repertory_no" jdbcType="VARCHAR" property="receiptRepertoryNo" />
    <result column="receipt_repertory_id" jdbcType="BIGINT" property="receiptRepertoryId" />
    <result column="want_receipt_date" jdbcType="TIMESTAMP" property="wantReceiptDate" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="purchase_channel" jdbcType="TINYINT" property="purchaseChannel" />
    <result column="flowType" jdbcType="TINYINT" property="flowtype" />
    <result column="supplier_repertory_address" jdbcType="VARCHAR" property="supplierRepertoryAddress" />
    <result column="submit_time" jdbcType="TIMESTAMP" property="submitTime" />
    <result column="approved_time" jdbcType="TIMESTAMP" property="approvedTime" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, purchase_application_no, business_id, business_name, create_time, create_name, 
    user_mdm_code, document_status, audit_status, operator, operate_time, express_model, 
    unified_company_no, unified_company_name, supplier_repertory_no, supplier_repertory_name, 
    receipt_repertory_name, receipt_repertory_no, receipt_repertory_id, want_receipt_date, 
    gmt_create, gmt_update, status, extend, version, created_by, update_by, purchase_channel, 
    flowType, supplier_repertory_address, submit_time, approved_time, org_id, org_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.UnifiedPurchaseApplicationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from unified_purchase_application
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from unified_purchase_application
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from unified_purchase_application
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.UnifiedPurchaseApplicationExample">
    delete from unified_purchase_application
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.UnifiedPurchaseApplication">
    insert into unified_purchase_application (id, purchase_application_no, business_id, 
      business_name, create_time, create_name, 
      user_mdm_code, document_status, audit_status, 
      operator, operate_time, express_model, 
      unified_company_no, unified_company_name, supplier_repertory_no, 
      supplier_repertory_name, receipt_repertory_name, 
      receipt_repertory_no, receipt_repertory_id, 
      want_receipt_date, gmt_create, gmt_update, 
      status, extend, version, 
      created_by, update_by, purchase_channel, 
      flowType, supplier_repertory_address, submit_time, 
      approved_time, org_id, org_name
      )
    values (#{id,jdbcType=INTEGER}, #{purchaseApplicationNo,jdbcType=VARCHAR}, #{businessId,jdbcType=BIGINT}, 
      #{businessName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createName,jdbcType=VARCHAR}, 
      #{userMdmCode,jdbcType=VARCHAR}, #{documentStatus,jdbcType=TINYINT}, #{auditStatus,jdbcType=TINYINT}, 
      #{operator,jdbcType=VARCHAR}, #{operateTime,jdbcType=TIMESTAMP}, #{expressModel,jdbcType=INTEGER}, 
      #{unifiedCompanyNo,jdbcType=VARCHAR}, #{unifiedCompanyName,jdbcType=VARCHAR}, #{supplierRepertoryNo,jdbcType=VARCHAR}, 
      #{supplierRepertoryName,jdbcType=VARCHAR}, #{receiptRepertoryName,jdbcType=VARCHAR}, 
      #{receiptRepertoryNo,jdbcType=VARCHAR}, #{receiptRepertoryId,jdbcType=BIGINT}, 
      #{wantReceiptDate,jdbcType=TIMESTAMP}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, #{purchaseChannel,jdbcType=TINYINT}, 
      #{flowtype,jdbcType=TINYINT}, #{supplierRepertoryAddress,jdbcType=VARCHAR}, #{submitTime,jdbcType=TIMESTAMP}, 
      #{approvedTime,jdbcType=TIMESTAMP}, #{orgId,jdbcType=BIGINT}, #{orgName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.UnifiedPurchaseApplication">
    insert into unified_purchase_application
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="purchaseApplicationNo != null">
        purchase_application_no,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="businessName != null">
        business_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="userMdmCode != null">
        user_mdm_code,
      </if>
      <if test="documentStatus != null">
        document_status,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="operateTime != null">
        operate_time,
      </if>
      <if test="expressModel != null">
        express_model,
      </if>
      <if test="unifiedCompanyNo != null">
        unified_company_no,
      </if>
      <if test="unifiedCompanyName != null">
        unified_company_name,
      </if>
      <if test="supplierRepertoryNo != null">
        supplier_repertory_no,
      </if>
      <if test="supplierRepertoryName != null">
        supplier_repertory_name,
      </if>
      <if test="receiptRepertoryName != null">
        receipt_repertory_name,
      </if>
      <if test="receiptRepertoryNo != null">
        receipt_repertory_no,
      </if>
      <if test="receiptRepertoryId != null">
        receipt_repertory_id,
      </if>
      <if test="wantReceiptDate != null">
        want_receipt_date,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="purchaseChannel != null">
        purchase_channel,
      </if>
      <if test="flowtype != null">
        flowType,
      </if>
      <if test="supplierRepertoryAddress != null">
        supplier_repertory_address,
      </if>
      <if test="submitTime != null">
        submit_time,
      </if>
      <if test="approvedTime != null">
        approved_time,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="purchaseApplicationNo != null">
        #{purchaseApplicationNo,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessName != null">
        #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="userMdmCode != null">
        #{userMdmCode,jdbcType=VARCHAR},
      </if>
      <if test="documentStatus != null">
        #{documentStatus,jdbcType=TINYINT},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operateTime != null">
        #{operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expressModel != null">
        #{expressModel,jdbcType=INTEGER},
      </if>
      <if test="unifiedCompanyNo != null">
        #{unifiedCompanyNo,jdbcType=VARCHAR},
      </if>
      <if test="unifiedCompanyName != null">
        #{unifiedCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="supplierRepertoryNo != null">
        #{supplierRepertoryNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierRepertoryName != null">
        #{supplierRepertoryName,jdbcType=VARCHAR},
      </if>
      <if test="receiptRepertoryName != null">
        #{receiptRepertoryName,jdbcType=VARCHAR},
      </if>
      <if test="receiptRepertoryNo != null">
        #{receiptRepertoryNo,jdbcType=VARCHAR},
      </if>
      <if test="receiptRepertoryId != null">
        #{receiptRepertoryId,jdbcType=BIGINT},
      </if>
      <if test="wantReceiptDate != null">
        #{wantReceiptDate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="purchaseChannel != null">
        #{purchaseChannel,jdbcType=TINYINT},
      </if>
      <if test="flowtype != null">
        #{flowtype,jdbcType=TINYINT},
      </if>
      <if test="supplierRepertoryAddress != null">
        #{supplierRepertoryAddress,jdbcType=VARCHAR},
      </if>
      <if test="submitTime != null">
        #{submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="approvedTime != null">
        #{approvedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.UnifiedPurchaseApplicationExample" resultType="java.lang.Long">
    select count(*) from unified_purchase_application
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update unified_purchase_application
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.purchaseApplicationNo != null">
        purchase_application_no = #{record.purchaseApplicationNo,jdbcType=VARCHAR},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.businessName != null">
        business_name = #{record.businessName,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createName != null">
        create_name = #{record.createName,jdbcType=VARCHAR},
      </if>
      <if test="record.userMdmCode != null">
        user_mdm_code = #{record.userMdmCode,jdbcType=VARCHAR},
      </if>
      <if test="record.documentStatus != null">
        document_status = #{record.documentStatus,jdbcType=TINYINT},
      </if>
      <if test="record.auditStatus != null">
        audit_status = #{record.auditStatus,jdbcType=TINYINT},
      </if>
      <if test="record.operator != null">
        operator = #{record.operator,jdbcType=VARCHAR},
      </if>
      <if test="record.operateTime != null">
        operate_time = #{record.operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.expressModel != null">
        express_model = #{record.expressModel,jdbcType=INTEGER},
      </if>
      <if test="record.unifiedCompanyNo != null">
        unified_company_no = #{record.unifiedCompanyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.unifiedCompanyName != null">
        unified_company_name = #{record.unifiedCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierRepertoryNo != null">
        supplier_repertory_no = #{record.supplierRepertoryNo,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierRepertoryName != null">
        supplier_repertory_name = #{record.supplierRepertoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.receiptRepertoryName != null">
        receipt_repertory_name = #{record.receiptRepertoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.receiptRepertoryNo != null">
        receipt_repertory_no = #{record.receiptRepertoryNo,jdbcType=VARCHAR},
      </if>
      <if test="record.receiptRepertoryId != null">
        receipt_repertory_id = #{record.receiptRepertoryId,jdbcType=BIGINT},
      </if>
      <if test="record.wantReceiptDate != null">
        want_receipt_date = #{record.wantReceiptDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=BIGINT},
      </if>
      <if test="record.purchaseChannel != null">
        purchase_channel = #{record.purchaseChannel,jdbcType=TINYINT},
      </if>
      <if test="record.flowtype != null">
        flowType = #{record.flowtype,jdbcType=TINYINT},
      </if>
      <if test="record.supplierRepertoryAddress != null">
        supplier_repertory_address = #{record.supplierRepertoryAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.submitTime != null">
        submit_time = #{record.submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.approvedTime != null">
        approved_time = #{record.approvedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update unified_purchase_application
    set id = #{record.id,jdbcType=INTEGER},
      purchase_application_no = #{record.purchaseApplicationNo,jdbcType=VARCHAR},
      business_id = #{record.businessId,jdbcType=BIGINT},
      business_name = #{record.businessName,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_name = #{record.createName,jdbcType=VARCHAR},
      user_mdm_code = #{record.userMdmCode,jdbcType=VARCHAR},
      document_status = #{record.documentStatus,jdbcType=TINYINT},
      audit_status = #{record.auditStatus,jdbcType=TINYINT},
      operator = #{record.operator,jdbcType=VARCHAR},
      operate_time = #{record.operateTime,jdbcType=TIMESTAMP},
      express_model = #{record.expressModel,jdbcType=INTEGER},
      unified_company_no = #{record.unifiedCompanyNo,jdbcType=VARCHAR},
      unified_company_name = #{record.unifiedCompanyName,jdbcType=VARCHAR},
      supplier_repertory_no = #{record.supplierRepertoryNo,jdbcType=VARCHAR},
      supplier_repertory_name = #{record.supplierRepertoryName,jdbcType=VARCHAR},
      receipt_repertory_name = #{record.receiptRepertoryName,jdbcType=VARCHAR},
      receipt_repertory_no = #{record.receiptRepertoryNo,jdbcType=VARCHAR},
      receipt_repertory_id = #{record.receiptRepertoryId,jdbcType=BIGINT},
      want_receipt_date = #{record.wantReceiptDate,jdbcType=TIMESTAMP},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      update_by = #{record.updateBy,jdbcType=BIGINT},
      purchase_channel = #{record.purchaseChannel,jdbcType=TINYINT},
      flowType = #{record.flowtype,jdbcType=TINYINT},
      supplier_repertory_address = #{record.supplierRepertoryAddress,jdbcType=VARCHAR},
      submit_time = #{record.submitTime,jdbcType=TIMESTAMP},
      approved_time = #{record.approvedTime,jdbcType=TIMESTAMP},
      org_id = #{record.orgId,jdbcType=BIGINT},
      org_name = #{record.orgName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.UnifiedPurchaseApplication">
    update unified_purchase_application
    <set>
      <if test="purchaseApplicationNo != null">
        purchase_application_no = #{purchaseApplicationNo,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessName != null">
        business_name = #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="userMdmCode != null">
        user_mdm_code = #{userMdmCode,jdbcType=VARCHAR},
      </if>
      <if test="documentStatus != null">
        document_status = #{documentStatus,jdbcType=TINYINT},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="operateTime != null">
        operate_time = #{operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expressModel != null">
        express_model = #{expressModel,jdbcType=INTEGER},
      </if>
      <if test="unifiedCompanyNo != null">
        unified_company_no = #{unifiedCompanyNo,jdbcType=VARCHAR},
      </if>
      <if test="unifiedCompanyName != null">
        unified_company_name = #{unifiedCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="supplierRepertoryNo != null">
        supplier_repertory_no = #{supplierRepertoryNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierRepertoryName != null">
        supplier_repertory_name = #{supplierRepertoryName,jdbcType=VARCHAR},
      </if>
      <if test="receiptRepertoryName != null">
        receipt_repertory_name = #{receiptRepertoryName,jdbcType=VARCHAR},
      </if>
      <if test="receiptRepertoryNo != null">
        receipt_repertory_no = #{receiptRepertoryNo,jdbcType=VARCHAR},
      </if>
      <if test="receiptRepertoryId != null">
        receipt_repertory_id = #{receiptRepertoryId,jdbcType=BIGINT},
      </if>
      <if test="wantReceiptDate != null">
        want_receipt_date = #{wantReceiptDate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="purchaseChannel != null">
        purchase_channel = #{purchaseChannel,jdbcType=TINYINT},
      </if>
      <if test="flowtype != null">
        flowType = #{flowtype,jdbcType=TINYINT},
      </if>
      <if test="supplierRepertoryAddress != null">
        supplier_repertory_address = #{supplierRepertoryAddress,jdbcType=VARCHAR},
      </if>
      <if test="submitTime != null">
        submit_time = #{submitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="approvedTime != null">
        approved_time = #{approvedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.UnifiedPurchaseApplication">
    update unified_purchase_application
    set purchase_application_no = #{purchaseApplicationNo,jdbcType=VARCHAR},
      business_id = #{businessId,jdbcType=BIGINT},
      business_name = #{businessName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_name = #{createName,jdbcType=VARCHAR},
      user_mdm_code = #{userMdmCode,jdbcType=VARCHAR},
      document_status = #{documentStatus,jdbcType=TINYINT},
      audit_status = #{auditStatus,jdbcType=TINYINT},
      operator = #{operator,jdbcType=VARCHAR},
      operate_time = #{operateTime,jdbcType=TIMESTAMP},
      express_model = #{expressModel,jdbcType=INTEGER},
      unified_company_no = #{unifiedCompanyNo,jdbcType=VARCHAR},
      unified_company_name = #{unifiedCompanyName,jdbcType=VARCHAR},
      supplier_repertory_no = #{supplierRepertoryNo,jdbcType=VARCHAR},
      supplier_repertory_name = #{supplierRepertoryName,jdbcType=VARCHAR},
      receipt_repertory_name = #{receiptRepertoryName,jdbcType=VARCHAR},
      receipt_repertory_no = #{receiptRepertoryNo,jdbcType=VARCHAR},
      receipt_repertory_id = #{receiptRepertoryId,jdbcType=BIGINT},
      want_receipt_date = #{wantReceiptDate,jdbcType=TIMESTAMP},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      purchase_channel = #{purchaseChannel,jdbcType=TINYINT},
      flowType = #{flowtype,jdbcType=TINYINT},
      supplier_repertory_address = #{supplierRepertoryAddress,jdbcType=VARCHAR},
      submit_time = #{submitTime,jdbcType=TIMESTAMP},
      approved_time = #{approvedTime,jdbcType=TIMESTAMP},
      org_id = #{orgId,jdbcType=BIGINT},
      org_name = #{orgName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>