<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SettleCheckAccountRecordMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SettleCheckAccountRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="delivery_no" jdbcType="VARCHAR" property="deliveryNo" />
    <result column="delivery_id" jdbcType="VARCHAR" property="deliveryId" />
    <result column="delivery_year" jdbcType="VARCHAR" property="deliveryYear" />
    <result column="delivery_key" jdbcType="VARCHAR" property="deliveryKey" />
    <result column="result_status" jdbcType="VARCHAR" property="resultStatus" />
    <result column="check_action" jdbcType="INTEGER" property="checkAction" />
    <result column="result_msg" jdbcType="VARCHAR" property="resultMsg" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, delivery_no, delivery_id, delivery_year, delivery_key, result_status, check_action, 
    result_msg, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SettleCheckAccountRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from settle_check_account_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from settle_check_account_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from settle_check_account_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SettleCheckAccountRecordExample">
    delete from settle_check_account_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SettleCheckAccountRecord">
    insert into settle_check_account_record (id, delivery_no, delivery_id, 
      delivery_year, delivery_key, result_status, 
      check_action, result_msg, gmt_create, 
      gmt_update)
    values (#{id,jdbcType=INTEGER}, #{deliveryNo,jdbcType=VARCHAR}, #{deliveryId,jdbcType=VARCHAR}, 
      #{deliveryYear,jdbcType=VARCHAR}, #{deliveryKey,jdbcType=VARCHAR}, #{resultStatus,jdbcType=VARCHAR}, 
      #{checkAction,jdbcType=INTEGER}, #{resultMsg,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SettleCheckAccountRecord">
    insert into settle_check_account_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="deliveryNo != null">
        delivery_no,
      </if>
      <if test="deliveryId != null">
        delivery_id,
      </if>
      <if test="deliveryYear != null">
        delivery_year,
      </if>
      <if test="deliveryKey != null">
        delivery_key,
      </if>
      <if test="resultStatus != null">
        result_status,
      </if>
      <if test="checkAction != null">
        check_action,
      </if>
      <if test="resultMsg != null">
        result_msg,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="deliveryNo != null">
        #{deliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryId != null">
        #{deliveryId,jdbcType=VARCHAR},
      </if>
      <if test="deliveryYear != null">
        #{deliveryYear,jdbcType=VARCHAR},
      </if>
      <if test="deliveryKey != null">
        #{deliveryKey,jdbcType=VARCHAR},
      </if>
      <if test="resultStatus != null">
        #{resultStatus,jdbcType=VARCHAR},
      </if>
      <if test="checkAction != null">
        #{checkAction,jdbcType=INTEGER},
      </if>
      <if test="resultMsg != null">
        #{resultMsg,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SettleCheckAccountRecordExample" resultType="java.lang.Long">
    select count(*) from settle_check_account_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update settle_check_account_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.deliveryNo != null">
        delivery_no = #{record.deliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryId != null">
        delivery_id = #{record.deliveryId,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryYear != null">
        delivery_year = #{record.deliveryYear,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryKey != null">
        delivery_key = #{record.deliveryKey,jdbcType=VARCHAR},
      </if>
      <if test="record.resultStatus != null">
        result_status = #{record.resultStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.checkAction != null">
        check_action = #{record.checkAction,jdbcType=INTEGER},
      </if>
      <if test="record.resultMsg != null">
        result_msg = #{record.resultMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update settle_check_account_record
    set id = #{record.id,jdbcType=INTEGER},
      delivery_no = #{record.deliveryNo,jdbcType=VARCHAR},
      delivery_id = #{record.deliveryId,jdbcType=VARCHAR},
      delivery_year = #{record.deliveryYear,jdbcType=VARCHAR},
      delivery_key = #{record.deliveryKey,jdbcType=VARCHAR},
      result_status = #{record.resultStatus,jdbcType=VARCHAR},
      check_action = #{record.checkAction,jdbcType=INTEGER},
      result_msg = #{record.resultMsg,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SettleCheckAccountRecord">
    update settle_check_account_record
    <set>
      <if test="deliveryNo != null">
        delivery_no = #{deliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryId != null">
        delivery_id = #{deliveryId,jdbcType=VARCHAR},
      </if>
      <if test="deliveryYear != null">
        delivery_year = #{deliveryYear,jdbcType=VARCHAR},
      </if>
      <if test="deliveryKey != null">
        delivery_key = #{deliveryKey,jdbcType=VARCHAR},
      </if>
      <if test="resultStatus != null">
        result_status = #{resultStatus,jdbcType=VARCHAR},
      </if>
      <if test="checkAction != null">
        check_action = #{checkAction,jdbcType=INTEGER},
      </if>
      <if test="resultMsg != null">
        result_msg = #{resultMsg,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SettleCheckAccountRecord">
    update settle_check_account_record
    set delivery_no = #{deliveryNo,jdbcType=VARCHAR},
      delivery_id = #{deliveryId,jdbcType=VARCHAR},
      delivery_year = #{deliveryYear,jdbcType=VARCHAR},
      delivery_key = #{deliveryKey,jdbcType=VARCHAR},
      result_status = #{resultStatus,jdbcType=VARCHAR},
      check_action = #{checkAction,jdbcType=INTEGER},
      result_msg = #{resultMsg,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>