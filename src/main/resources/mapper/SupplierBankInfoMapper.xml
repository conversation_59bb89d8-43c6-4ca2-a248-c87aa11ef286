<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SupplierBankInfoMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SupplierBankInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="supplier_no" jdbcType="VARCHAR" property="supplierNo" />
    <result column="supplier_bankid" jdbcType="VARCHAR" property="supplierBankid" />
    <result column="supplier_bankname" jdbcType="VARCHAR" property="supplierBankname" />
    <result column="supplier_bankowner" jdbcType="VARCHAR" property="supplierBankowner" />
    <result column="supplier_cardid" jdbcType="VARCHAR" property="supplierCardid" />
    <result column="supplier_country" jdbcType="VARCHAR" property="supplierCountry" />
    <result column="bankind" jdbcType="VARCHAR" property="bankind" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, supplier_no, supplier_bankid, supplier_bankname, supplier_bankowner, supplier_cardid, 
    supplier_country, bankind, extend, `status`, version, created_by, updated_by, gmt_create, 
    gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SupplierBankInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from supplier_bank_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from supplier_bank_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from supplier_bank_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SupplierBankInfoExample">
    delete from supplier_bank_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SupplierBankInfo">
    insert into supplier_bank_info (id, supplier_no, supplier_bankid, 
      supplier_bankname, supplier_bankowner, supplier_cardid, 
      supplier_country, bankind, extend, 
      `status`, version, created_by, 
      updated_by, gmt_create, gmt_update
      )
    values (#{id,jdbcType=BIGINT}, #{supplierNo,jdbcType=VARCHAR}, #{supplierBankid,jdbcType=VARCHAR}, 
      #{supplierBankname,jdbcType=VARCHAR}, #{supplierBankowner,jdbcType=VARCHAR}, #{supplierCardid,jdbcType=VARCHAR}, 
      #{supplierCountry,jdbcType=VARCHAR}, #{bankind,jdbcType=VARCHAR}, #{extend,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, 
      #{updatedBy,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SupplierBankInfo">
    insert into supplier_bank_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="supplierNo != null">
        supplier_no,
      </if>
      <if test="supplierBankid != null">
        supplier_bankid,
      </if>
      <if test="supplierBankname != null">
        supplier_bankname,
      </if>
      <if test="supplierBankowner != null">
        supplier_bankowner,
      </if>
      <if test="supplierCardid != null">
        supplier_cardid,
      </if>
      <if test="supplierCountry != null">
        supplier_country,
      </if>
      <if test="bankind != null">
        bankind,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="supplierNo != null">
        #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierBankid != null">
        #{supplierBankid,jdbcType=VARCHAR},
      </if>
      <if test="supplierBankname != null">
        #{supplierBankname,jdbcType=VARCHAR},
      </if>
      <if test="supplierBankowner != null">
        #{supplierBankowner,jdbcType=VARCHAR},
      </if>
      <if test="supplierCardid != null">
        #{supplierCardid,jdbcType=VARCHAR},
      </if>
      <if test="supplierCountry != null">
        #{supplierCountry,jdbcType=VARCHAR},
      </if>
      <if test="bankind != null">
        #{bankind,jdbcType=VARCHAR},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SupplierBankInfoExample" resultType="java.lang.Long">
    select count(*) from supplier_bank_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update supplier_bank_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.supplierNo != null">
        supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierBankid != null">
        supplier_bankid = #{record.supplierBankid,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierBankname != null">
        supplier_bankname = #{record.supplierBankname,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierBankowner != null">
        supplier_bankowner = #{record.supplierBankowner,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierCardid != null">
        supplier_cardid = #{record.supplierCardid,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierCountry != null">
        supplier_country = #{record.supplierCountry,jdbcType=VARCHAR},
      </if>
      <if test="record.bankind != null">
        bankind = #{record.bankind,jdbcType=VARCHAR},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update supplier_bank_info
    set id = #{record.id,jdbcType=BIGINT},
      supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      supplier_bankid = #{record.supplierBankid,jdbcType=VARCHAR},
      supplier_bankname = #{record.supplierBankname,jdbcType=VARCHAR},
      supplier_bankowner = #{record.supplierBankowner,jdbcType=VARCHAR},
      supplier_cardid = #{record.supplierCardid,jdbcType=VARCHAR},
      supplier_country = #{record.supplierCountry,jdbcType=VARCHAR},
      bankind = #{record.bankind,jdbcType=VARCHAR},
      extend = #{record.extend,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=TINYINT},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SupplierBankInfo">
    update supplier_bank_info
    <set>
      <if test="supplierNo != null">
        supplier_no = #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierBankid != null">
        supplier_bankid = #{supplierBankid,jdbcType=VARCHAR},
      </if>
      <if test="supplierBankname != null">
        supplier_bankname = #{supplierBankname,jdbcType=VARCHAR},
      </if>
      <if test="supplierBankowner != null">
        supplier_bankowner = #{supplierBankowner,jdbcType=VARCHAR},
      </if>
      <if test="supplierCardid != null">
        supplier_cardid = #{supplierCardid,jdbcType=VARCHAR},
      </if>
      <if test="supplierCountry != null">
        supplier_country = #{supplierCountry,jdbcType=VARCHAR},
      </if>
      <if test="bankind != null">
        bankind = #{bankind,jdbcType=VARCHAR},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SupplierBankInfo">
    update supplier_bank_info
    set supplier_no = #{supplierNo,jdbcType=VARCHAR},
      supplier_bankid = #{supplierBankid,jdbcType=VARCHAR},
      supplier_bankname = #{supplierBankname,jdbcType=VARCHAR},
      supplier_bankowner = #{supplierBankowner,jdbcType=VARCHAR},
      supplier_cardid = #{supplierCardid,jdbcType=VARCHAR},
      supplier_country = #{supplierCountry,jdbcType=VARCHAR},
      bankind = #{bankind,jdbcType=VARCHAR},
      extend = #{extend,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>