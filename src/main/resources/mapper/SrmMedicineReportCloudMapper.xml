<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmMedicineReportCloudMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmMedicineReportCloud">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="supplier_id" jdbcType="VARCHAR" property="supplierId" />
    <result column="supplier_no" jdbcType="VARCHAR" property="supplierNo" />
    <result column="mongo_id" jdbcType="VARCHAR" property="mongoId" />
    <result column="drug_id" jdbcType="VARCHAR" property="drugId" />
    <result column="drug_name" jdbcType="VARCHAR" property="drugName" />
    <result column="drug_number" jdbcType="VARCHAR" property="drugNumber" />
    <result column="drug_batch_number" jdbcType="VARCHAR" property="drugBatchNumber" />
    <result column="drug_zhunzi" jdbcType="VARCHAR" property="drugZhunzi" />
    <result column="wldw_number" jdbcType="VARCHAR" property="wldwNumber" />
    <result column="wldw_name" jdbcType="VARCHAR" property="wldwName" />
    <result column="wldw_id" jdbcType="VARCHAR" property="wldwId" />
    <result column="wldw_standard_id" jdbcType="VARCHAR" property="wldwStandardId" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="file_pic_url" jdbcType="VARCHAR" property="filePicUrl" />
    <result column="files_count" jdbcType="INTEGER" property="filesCount" />
    <result column="supplier_send_no" jdbcType="VARCHAR" property="supplierSendNo" />
    <result column="flag" jdbcType="INTEGER" property="flag" />
    <result column="deal_status" jdbcType="TINYINT" property="dealStatus" />
    <result column="deal_reason" jdbcType="VARCHAR" property="dealReason" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, supplier_id, supplier_no, mongo_id, drug_id, drug_name, drug_number, drug_batch_number, 
    drug_zhunzi, wldw_number, wldw_name, wldw_id, wldw_standard_id, file_url, file_pic_url, 
    files_count, supplier_send_no, flag, deal_status, deal_reason, gmt_create, gmt_update, 
    `status`, extend, version
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmMedicineReportCloudExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_medicine_report_cloud
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_medicine_report_cloud
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from srm_medicine_report_cloud
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmMedicineReportCloudExample">
    delete from srm_medicine_report_cloud
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmMedicineReportCloud">
    insert into srm_medicine_report_cloud (id, supplier_id, supplier_no, 
      mongo_id, drug_id, drug_name, 
      drug_number, drug_batch_number, drug_zhunzi, 
      wldw_number, wldw_name, wldw_id, 
      wldw_standard_id, file_url, file_pic_url, 
      files_count, supplier_send_no, flag, 
      deal_status, deal_reason, gmt_create, 
      gmt_update, `status`, extend, 
      version)
    values (#{id,jdbcType=BIGINT}, #{supplierId,jdbcType=VARCHAR}, #{supplierNo,jdbcType=VARCHAR}, 
      #{mongoId,jdbcType=VARCHAR}, #{drugId,jdbcType=VARCHAR}, #{drugName,jdbcType=VARCHAR}, 
      #{drugNumber,jdbcType=VARCHAR}, #{drugBatchNumber,jdbcType=VARCHAR}, #{drugZhunzi,jdbcType=VARCHAR}, 
      #{wldwNumber,jdbcType=VARCHAR}, #{wldwName,jdbcType=VARCHAR}, #{wldwId,jdbcType=VARCHAR}, 
      #{wldwStandardId,jdbcType=VARCHAR}, #{fileUrl,jdbcType=VARCHAR}, #{filePicUrl,jdbcType=VARCHAR}, 
      #{filesCount,jdbcType=INTEGER}, #{supplierSendNo,jdbcType=VARCHAR}, #{flag,jdbcType=INTEGER}, 
      #{dealStatus,jdbcType=TINYINT}, #{dealReason,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmMedicineReportCloud">
    insert into srm_medicine_report_cloud
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="supplierNo != null">
        supplier_no,
      </if>
      <if test="mongoId != null">
        mongo_id,
      </if>
      <if test="drugId != null">
        drug_id,
      </if>
      <if test="drugName != null">
        drug_name,
      </if>
      <if test="drugNumber != null">
        drug_number,
      </if>
      <if test="drugBatchNumber != null">
        drug_batch_number,
      </if>
      <if test="drugZhunzi != null">
        drug_zhunzi,
      </if>
      <if test="wldwNumber != null">
        wldw_number,
      </if>
      <if test="wldwName != null">
        wldw_name,
      </if>
      <if test="wldwId != null">
        wldw_id,
      </if>
      <if test="wldwStandardId != null">
        wldw_standard_id,
      </if>
      <if test="fileUrl != null">
        file_url,
      </if>
      <if test="filePicUrl != null">
        file_pic_url,
      </if>
      <if test="filesCount != null">
        files_count,
      </if>
      <if test="supplierSendNo != null">
        supplier_send_no,
      </if>
      <if test="flag != null">
        flag,
      </if>
      <if test="dealStatus != null">
        deal_status,
      </if>
      <if test="dealReason != null">
        deal_reason,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="supplierId != null">
        #{supplierId,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null">
        #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="mongoId != null">
        #{mongoId,jdbcType=VARCHAR},
      </if>
      <if test="drugId != null">
        #{drugId,jdbcType=VARCHAR},
      </if>
      <if test="drugName != null">
        #{drugName,jdbcType=VARCHAR},
      </if>
      <if test="drugNumber != null">
        #{drugNumber,jdbcType=VARCHAR},
      </if>
      <if test="drugBatchNumber != null">
        #{drugBatchNumber,jdbcType=VARCHAR},
      </if>
      <if test="drugZhunzi != null">
        #{drugZhunzi,jdbcType=VARCHAR},
      </if>
      <if test="wldwNumber != null">
        #{wldwNumber,jdbcType=VARCHAR},
      </if>
      <if test="wldwName != null">
        #{wldwName,jdbcType=VARCHAR},
      </if>
      <if test="wldwId != null">
        #{wldwId,jdbcType=VARCHAR},
      </if>
      <if test="wldwStandardId != null">
        #{wldwStandardId,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="filePicUrl != null">
        #{filePicUrl,jdbcType=VARCHAR},
      </if>
      <if test="filesCount != null">
        #{filesCount,jdbcType=INTEGER},
      </if>
      <if test="supplierSendNo != null">
        #{supplierSendNo,jdbcType=VARCHAR},
      </if>
      <if test="flag != null">
        #{flag,jdbcType=INTEGER},
      </if>
      <if test="dealStatus != null">
        #{dealStatus,jdbcType=TINYINT},
      </if>
      <if test="dealReason != null">
        #{dealReason,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmMedicineReportCloudExample" resultType="java.lang.Long">
    select count(*) from srm_medicine_report_cloud
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_medicine_report_cloud
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.supplierId != null">
        supplier_id = #{record.supplierId,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierNo != null">
        supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="record.mongoId != null">
        mongo_id = #{record.mongoId,jdbcType=VARCHAR},
      </if>
      <if test="record.drugId != null">
        drug_id = #{record.drugId,jdbcType=VARCHAR},
      </if>
      <if test="record.drugName != null">
        drug_name = #{record.drugName,jdbcType=VARCHAR},
      </if>
      <if test="record.drugNumber != null">
        drug_number = #{record.drugNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.drugBatchNumber != null">
        drug_batch_number = #{record.drugBatchNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.drugZhunzi != null">
        drug_zhunzi = #{record.drugZhunzi,jdbcType=VARCHAR},
      </if>
      <if test="record.wldwNumber != null">
        wldw_number = #{record.wldwNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.wldwName != null">
        wldw_name = #{record.wldwName,jdbcType=VARCHAR},
      </if>
      <if test="record.wldwId != null">
        wldw_id = #{record.wldwId,jdbcType=VARCHAR},
      </if>
      <if test="record.wldwStandardId != null">
        wldw_standard_id = #{record.wldwStandardId,jdbcType=VARCHAR},
      </if>
      <if test="record.fileUrl != null">
        file_url = #{record.fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.filePicUrl != null">
        file_pic_url = #{record.filePicUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.filesCount != null">
        files_count = #{record.filesCount,jdbcType=INTEGER},
      </if>
      <if test="record.supplierSendNo != null">
        supplier_send_no = #{record.supplierSendNo,jdbcType=VARCHAR},
      </if>
      <if test="record.flag != null">
        flag = #{record.flag,jdbcType=INTEGER},
      </if>
      <if test="record.dealStatus != null">
        deal_status = #{record.dealStatus,jdbcType=TINYINT},
      </if>
      <if test="record.dealReason != null">
        deal_reason = #{record.dealReason,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_medicine_report_cloud
    set id = #{record.id,jdbcType=BIGINT},
      supplier_id = #{record.supplierId,jdbcType=VARCHAR},
      supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      mongo_id = #{record.mongoId,jdbcType=VARCHAR},
      drug_id = #{record.drugId,jdbcType=VARCHAR},
      drug_name = #{record.drugName,jdbcType=VARCHAR},
      drug_number = #{record.drugNumber,jdbcType=VARCHAR},
      drug_batch_number = #{record.drugBatchNumber,jdbcType=VARCHAR},
      drug_zhunzi = #{record.drugZhunzi,jdbcType=VARCHAR},
      wldw_number = #{record.wldwNumber,jdbcType=VARCHAR},
      wldw_name = #{record.wldwName,jdbcType=VARCHAR},
      wldw_id = #{record.wldwId,jdbcType=VARCHAR},
      wldw_standard_id = #{record.wldwStandardId,jdbcType=VARCHAR},
      file_url = #{record.fileUrl,jdbcType=VARCHAR},
      file_pic_url = #{record.filePicUrl,jdbcType=VARCHAR},
      files_count = #{record.filesCount,jdbcType=INTEGER},
      supplier_send_no = #{record.supplierSendNo,jdbcType=VARCHAR},
      flag = #{record.flag,jdbcType=INTEGER},
      deal_status = #{record.dealStatus,jdbcType=TINYINT},
      deal_reason = #{record.dealReason,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      `status` = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmMedicineReportCloud">
    update srm_medicine_report_cloud
    <set>
      <if test="supplierId != null">
        supplier_id = #{supplierId,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null">
        supplier_no = #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="mongoId != null">
        mongo_id = #{mongoId,jdbcType=VARCHAR},
      </if>
      <if test="drugId != null">
        drug_id = #{drugId,jdbcType=VARCHAR},
      </if>
      <if test="drugName != null">
        drug_name = #{drugName,jdbcType=VARCHAR},
      </if>
      <if test="drugNumber != null">
        drug_number = #{drugNumber,jdbcType=VARCHAR},
      </if>
      <if test="drugBatchNumber != null">
        drug_batch_number = #{drugBatchNumber,jdbcType=VARCHAR},
      </if>
      <if test="drugZhunzi != null">
        drug_zhunzi = #{drugZhunzi,jdbcType=VARCHAR},
      </if>
      <if test="wldwNumber != null">
        wldw_number = #{wldwNumber,jdbcType=VARCHAR},
      </if>
      <if test="wldwName != null">
        wldw_name = #{wldwName,jdbcType=VARCHAR},
      </if>
      <if test="wldwId != null">
        wldw_id = #{wldwId,jdbcType=VARCHAR},
      </if>
      <if test="wldwStandardId != null">
        wldw_standard_id = #{wldwStandardId,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="filePicUrl != null">
        file_pic_url = #{filePicUrl,jdbcType=VARCHAR},
      </if>
      <if test="filesCount != null">
        files_count = #{filesCount,jdbcType=INTEGER},
      </if>
      <if test="supplierSendNo != null">
        supplier_send_no = #{supplierSendNo,jdbcType=VARCHAR},
      </if>
      <if test="flag != null">
        flag = #{flag,jdbcType=INTEGER},
      </if>
      <if test="dealStatus != null">
        deal_status = #{dealStatus,jdbcType=TINYINT},
      </if>
      <if test="dealReason != null">
        deal_reason = #{dealReason,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmMedicineReportCloud">
    update srm_medicine_report_cloud
    set supplier_id = #{supplierId,jdbcType=VARCHAR},
      supplier_no = #{supplierNo,jdbcType=VARCHAR},
      mongo_id = #{mongoId,jdbcType=VARCHAR},
      drug_id = #{drugId,jdbcType=VARCHAR},
      drug_name = #{drugName,jdbcType=VARCHAR},
      drug_number = #{drugNumber,jdbcType=VARCHAR},
      drug_batch_number = #{drugBatchNumber,jdbcType=VARCHAR},
      drug_zhunzi = #{drugZhunzi,jdbcType=VARCHAR},
      wldw_number = #{wldwNumber,jdbcType=VARCHAR},
      wldw_name = #{wldwName,jdbcType=VARCHAR},
      wldw_id = #{wldwId,jdbcType=VARCHAR},
      wldw_standard_id = #{wldwStandardId,jdbcType=VARCHAR},
      file_url = #{fileUrl,jdbcType=VARCHAR},
      file_pic_url = #{filePicUrl,jdbcType=VARCHAR},
      files_count = #{filesCount,jdbcType=INTEGER},
      supplier_send_no = #{supplierSendNo,jdbcType=VARCHAR},
      flag = #{flag,jdbcType=INTEGER},
      deal_status = #{dealStatus,jdbcType=TINYINT},
      deal_reason = #{dealReason,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>