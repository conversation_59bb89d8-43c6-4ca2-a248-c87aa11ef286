<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.UnifiedPurchaseOrderMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.UnifiedPurchaseOrder">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="purchase_order_no" jdbcType="VARCHAR" property="purchaseOrderNo" />
    <result column="purchase_application_no" jdbcType="VARCHAR" property="purchaseApplicationNo" />
    <result column="sap_no" jdbcType="VARCHAR" property="sapNo" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="express_model" jdbcType="INTEGER" property="expressModel" />
    <result column="unified_company_no" jdbcType="VARCHAR" property="unifiedCompanyNo" />
    <result column="unified_company_name" jdbcType="VARCHAR" property="unifiedCompanyName" />
    <result column="receipt_repertory_no" jdbcType="VARCHAR" property="receiptRepertoryNo" />
    <result column="receipt_repertory_name" jdbcType="VARCHAR" property="receiptRepertoryName" />
    <result column="receipt_repertory_address" jdbcType="VARCHAR" property="receiptRepertoryAddress" />
    <result column="supplier_repertory_no" jdbcType="VARCHAR" property="supplierRepertoryNo" />
    <result column="supplier_repertory_name" jdbcType="VARCHAR" property="supplierRepertoryName" />
    <result column="supplier_no" jdbcType="VARCHAR" property="supplierNo" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="want_receipt_date" jdbcType="TIMESTAMP" property="wantReceiptDate" />
    <result column="purchase_channel" jdbcType="TINYINT" property="purchaseChannel" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="bill_type" jdbcType="VARCHAR" property="billType" />
    <result column="dn_no" jdbcType="VARCHAR" property="dnNo" />
    <result column="deal_date" jdbcType="TIMESTAMP" property="dealDate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, purchase_order_no, purchase_application_no, sap_no, org_id, org_name, business_id, 
    business_name, gmt_create, create_name, created_by, status, gmt_update, operator, 
    update_by, express_model, unified_company_no, unified_company_name, receipt_repertory_no, 
    receipt_repertory_name, receipt_repertory_address, supplier_repertory_no, supplier_repertory_name, 
    supplier_no, supplier_name, want_receipt_date, purchase_channel, extend, version, 
    bill_type, dn_no, deal_date
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.UnifiedPurchaseOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from unified_purchase_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from unified_purchase_order
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from unified_purchase_order
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.UnifiedPurchaseOrderExample">
    delete from unified_purchase_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.UnifiedPurchaseOrder">
    insert into unified_purchase_order (id, purchase_order_no, purchase_application_no, 
      sap_no, org_id, org_name, 
      business_id, business_name, gmt_create, 
      create_name, created_by, status, 
      gmt_update, operator, update_by, 
      express_model, unified_company_no, unified_company_name, 
      receipt_repertory_no, receipt_repertory_name, 
      receipt_repertory_address, supplier_repertory_no, 
      supplier_repertory_name, supplier_no, supplier_name, 
      want_receipt_date, purchase_channel, extend, 
      version, bill_type, dn_no, 
      deal_date)
    values (#{id,jdbcType=INTEGER}, #{purchaseOrderNo,jdbcType=VARCHAR}, #{purchaseApplicationNo,jdbcType=VARCHAR}, 
      #{sapNo,jdbcType=VARCHAR}, #{orgId,jdbcType=BIGINT}, #{orgName,jdbcType=VARCHAR}, 
      #{businessId,jdbcType=BIGINT}, #{businessName,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{createName,jdbcType=VARCHAR}, #{createdBy,jdbcType=BIGINT}, #{status,jdbcType=TINYINT}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{operator,jdbcType=VARCHAR}, #{updateBy,jdbcType=BIGINT}, 
      #{expressModel,jdbcType=INTEGER}, #{unifiedCompanyNo,jdbcType=VARCHAR}, #{unifiedCompanyName,jdbcType=VARCHAR}, 
      #{receiptRepertoryNo,jdbcType=VARCHAR}, #{receiptRepertoryName,jdbcType=VARCHAR}, 
      #{receiptRepertoryAddress,jdbcType=VARCHAR}, #{supplierRepertoryNo,jdbcType=VARCHAR}, 
      #{supplierRepertoryName,jdbcType=VARCHAR}, #{supplierNo,jdbcType=VARCHAR}, #{supplierName,jdbcType=VARCHAR}, 
      #{wantReceiptDate,jdbcType=TIMESTAMP}, #{purchaseChannel,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{billType,jdbcType=VARCHAR}, #{dnNo,jdbcType=VARCHAR}, 
      #{dealDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.UnifiedPurchaseOrder">
    insert into unified_purchase_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="purchaseOrderNo != null">
        purchase_order_no,
      </if>
      <if test="purchaseApplicationNo != null">
        purchase_application_no,
      </if>
      <if test="sapNo != null">
        sap_no,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="businessName != null">
        business_name,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="expressModel != null">
        express_model,
      </if>
      <if test="unifiedCompanyNo != null">
        unified_company_no,
      </if>
      <if test="unifiedCompanyName != null">
        unified_company_name,
      </if>
      <if test="receiptRepertoryNo != null">
        receipt_repertory_no,
      </if>
      <if test="receiptRepertoryName != null">
        receipt_repertory_name,
      </if>
      <if test="receiptRepertoryAddress != null">
        receipt_repertory_address,
      </if>
      <if test="supplierRepertoryNo != null">
        supplier_repertory_no,
      </if>
      <if test="supplierRepertoryName != null">
        supplier_repertory_name,
      </if>
      <if test="supplierNo != null">
        supplier_no,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="wantReceiptDate != null">
        want_receipt_date,
      </if>
      <if test="purchaseChannel != null">
        purchase_channel,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="billType != null">
        bill_type,
      </if>
      <if test="dnNo != null">
        dn_no,
      </if>
      <if test="dealDate != null">
        deal_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="purchaseOrderNo != null">
        #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseApplicationNo != null">
        #{purchaseApplicationNo,jdbcType=VARCHAR},
      </if>
      <if test="sapNo != null">
        #{sapNo,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessName != null">
        #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="expressModel != null">
        #{expressModel,jdbcType=INTEGER},
      </if>
      <if test="unifiedCompanyNo != null">
        #{unifiedCompanyNo,jdbcType=VARCHAR},
      </if>
      <if test="unifiedCompanyName != null">
        #{unifiedCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="receiptRepertoryNo != null">
        #{receiptRepertoryNo,jdbcType=VARCHAR},
      </if>
      <if test="receiptRepertoryName != null">
        #{receiptRepertoryName,jdbcType=VARCHAR},
      </if>
      <if test="receiptRepertoryAddress != null">
        #{receiptRepertoryAddress,jdbcType=VARCHAR},
      </if>
      <if test="supplierRepertoryNo != null">
        #{supplierRepertoryNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierRepertoryName != null">
        #{supplierRepertoryName,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null">
        #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="wantReceiptDate != null">
        #{wantReceiptDate,jdbcType=TIMESTAMP},
      </if>
      <if test="purchaseChannel != null">
        #{purchaseChannel,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="billType != null">
        #{billType,jdbcType=VARCHAR},
      </if>
      <if test="dnNo != null">
        #{dnNo,jdbcType=VARCHAR},
      </if>
      <if test="dealDate != null">
        #{dealDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.UnifiedPurchaseOrderExample" resultType="java.lang.Long">
    select count(*) from unified_purchase_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update unified_purchase_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.purchaseOrderNo != null">
        purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseApplicationNo != null">
        purchase_application_no = #{record.purchaseApplicationNo,jdbcType=VARCHAR},
      </if>
      <if test="record.sapNo != null">
        sap_no = #{record.sapNo,jdbcType=VARCHAR},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.businessName != null">
        business_name = #{record.businessName,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createName != null">
        create_name = #{record.createName,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operator != null">
        operator = #{record.operator,jdbcType=VARCHAR},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=BIGINT},
      </if>
      <if test="record.expressModel != null">
        express_model = #{record.expressModel,jdbcType=INTEGER},
      </if>
      <if test="record.unifiedCompanyNo != null">
        unified_company_no = #{record.unifiedCompanyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.unifiedCompanyName != null">
        unified_company_name = #{record.unifiedCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="record.receiptRepertoryNo != null">
        receipt_repertory_no = #{record.receiptRepertoryNo,jdbcType=VARCHAR},
      </if>
      <if test="record.receiptRepertoryName != null">
        receipt_repertory_name = #{record.receiptRepertoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.receiptRepertoryAddress != null">
        receipt_repertory_address = #{record.receiptRepertoryAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierRepertoryNo != null">
        supplier_repertory_no = #{record.supplierRepertoryNo,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierRepertoryName != null">
        supplier_repertory_name = #{record.supplierRepertoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierNo != null">
        supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierName != null">
        supplier_name = #{record.supplierName,jdbcType=VARCHAR},
      </if>
      <if test="record.wantReceiptDate != null">
        want_receipt_date = #{record.wantReceiptDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.purchaseChannel != null">
        purchase_channel = #{record.purchaseChannel,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.billType != null">
        bill_type = #{record.billType,jdbcType=VARCHAR},
      </if>
      <if test="record.dnNo != null">
        dn_no = #{record.dnNo,jdbcType=VARCHAR},
      </if>
      <if test="record.dealDate != null">
        deal_date = #{record.dealDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update unified_purchase_order
    set id = #{record.id,jdbcType=INTEGER},
      purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      purchase_application_no = #{record.purchaseApplicationNo,jdbcType=VARCHAR},
      sap_no = #{record.sapNo,jdbcType=VARCHAR},
      org_id = #{record.orgId,jdbcType=BIGINT},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      business_id = #{record.businessId,jdbcType=BIGINT},
      business_name = #{record.businessName,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      create_name = #{record.createName,jdbcType=VARCHAR},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      status = #{record.status,jdbcType=TINYINT},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      operator = #{record.operator,jdbcType=VARCHAR},
      update_by = #{record.updateBy,jdbcType=BIGINT},
      express_model = #{record.expressModel,jdbcType=INTEGER},
      unified_company_no = #{record.unifiedCompanyNo,jdbcType=VARCHAR},
      unified_company_name = #{record.unifiedCompanyName,jdbcType=VARCHAR},
      receipt_repertory_no = #{record.receiptRepertoryNo,jdbcType=VARCHAR},
      receipt_repertory_name = #{record.receiptRepertoryName,jdbcType=VARCHAR},
      receipt_repertory_address = #{record.receiptRepertoryAddress,jdbcType=VARCHAR},
      supplier_repertory_no = #{record.supplierRepertoryNo,jdbcType=VARCHAR},
      supplier_repertory_name = #{record.supplierRepertoryName,jdbcType=VARCHAR},
      supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      supplier_name = #{record.supplierName,jdbcType=VARCHAR},
      want_receipt_date = #{record.wantReceiptDate,jdbcType=TIMESTAMP},
      purchase_channel = #{record.purchaseChannel,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      bill_type = #{record.billType,jdbcType=VARCHAR},
      dn_no = #{record.dnNo,jdbcType=VARCHAR},
      deal_date = #{record.dealDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.UnifiedPurchaseOrder">
    update unified_purchase_order
    <set>
      <if test="purchaseOrderNo != null">
        purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseApplicationNo != null">
        purchase_application_no = #{purchaseApplicationNo,jdbcType=VARCHAR},
      </if>
      <if test="sapNo != null">
        sap_no = #{sapNo,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessName != null">
        business_name = #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="expressModel != null">
        express_model = #{expressModel,jdbcType=INTEGER},
      </if>
      <if test="unifiedCompanyNo != null">
        unified_company_no = #{unifiedCompanyNo,jdbcType=VARCHAR},
      </if>
      <if test="unifiedCompanyName != null">
        unified_company_name = #{unifiedCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="receiptRepertoryNo != null">
        receipt_repertory_no = #{receiptRepertoryNo,jdbcType=VARCHAR},
      </if>
      <if test="receiptRepertoryName != null">
        receipt_repertory_name = #{receiptRepertoryName,jdbcType=VARCHAR},
      </if>
      <if test="receiptRepertoryAddress != null">
        receipt_repertory_address = #{receiptRepertoryAddress,jdbcType=VARCHAR},
      </if>
      <if test="supplierRepertoryNo != null">
        supplier_repertory_no = #{supplierRepertoryNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierRepertoryName != null">
        supplier_repertory_name = #{supplierRepertoryName,jdbcType=VARCHAR},
      </if>
      <if test="supplierNo != null">
        supplier_no = #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="wantReceiptDate != null">
        want_receipt_date = #{wantReceiptDate,jdbcType=TIMESTAMP},
      </if>
      <if test="purchaseChannel != null">
        purchase_channel = #{purchaseChannel,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="billType != null">
        bill_type = #{billType,jdbcType=VARCHAR},
      </if>
      <if test="dnNo != null">
        dn_no = #{dnNo,jdbcType=VARCHAR},
      </if>
      <if test="dealDate != null">
        deal_date = #{dealDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.UnifiedPurchaseOrder">
    update unified_purchase_order
    set purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      purchase_application_no = #{purchaseApplicationNo,jdbcType=VARCHAR},
      sap_no = #{sapNo,jdbcType=VARCHAR},
      org_id = #{orgId,jdbcType=BIGINT},
      org_name = #{orgName,jdbcType=VARCHAR},
      business_id = #{businessId,jdbcType=BIGINT},
      business_name = #{businessName,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      create_name = #{createName,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=BIGINT},
      status = #{status,jdbcType=TINYINT},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      operator = #{operator,jdbcType=VARCHAR},
      update_by = #{updateBy,jdbcType=BIGINT},
      express_model = #{expressModel,jdbcType=INTEGER},
      unified_company_no = #{unifiedCompanyNo,jdbcType=VARCHAR},
      unified_company_name = #{unifiedCompanyName,jdbcType=VARCHAR},
      receipt_repertory_no = #{receiptRepertoryNo,jdbcType=VARCHAR},
      receipt_repertory_name = #{receiptRepertoryName,jdbcType=VARCHAR},
      receipt_repertory_address = #{receiptRepertoryAddress,jdbcType=VARCHAR},
      supplier_repertory_no = #{supplierRepertoryNo,jdbcType=VARCHAR},
      supplier_repertory_name = #{supplierRepertoryName,jdbcType=VARCHAR},
      supplier_no = #{supplierNo,jdbcType=VARCHAR},
      supplier_name = #{supplierName,jdbcType=VARCHAR},
      want_receipt_date = #{wantReceiptDate,jdbcType=TIMESTAMP},
      purchase_channel = #{purchaseChannel,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      bill_type = #{billType,jdbcType=VARCHAR},
      dn_no = #{dnNo,jdbcType=VARCHAR},
      deal_date = #{dealDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>