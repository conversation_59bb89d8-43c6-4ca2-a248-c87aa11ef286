<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SupplierGoodsMatchCodeMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SupplierGoodsMatchCode">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="out_goods_unit" jdbcType="VARCHAR" property="outGoodsUnit" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="goods_variety_id" jdbcType="BIGINT" property="goodsVarietyId" />
    <result column="supplier_manufacturer_code" jdbcType="VARCHAR" property="supplierManufacturerCode" />
    <result column="supplier_manufacturer_name" jdbcType="VARCHAR" property="supplierManufacturerName" />
    <result column="out_supplier_code" jdbcType="VARCHAR" property="outSupplierCode" />
    <result column="out_supplier_name" jdbcType="VARCHAR" property="outSupplierName" />
    <result column="out_goods_code" jdbcType="VARCHAR" property="outGoodsCode" />
    <result column="mdm_package_num" jdbcType="INTEGER" property="mdmPackageNum" />
    <result column="supplier_package_num" jdbcType="INTEGER" property="supplierPackageNum" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, out_goods_unit, goods_code, goods_variety_id, supplier_manufacturer_code, supplier_manufacturer_name,
    out_supplier_code, out_supplier_name, out_goods_code, mdm_package_num, supplier_package_num,
    status, gmt_create, gmt_update, extend, version, created_by, created_name, updated_by,
    updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SupplierGoodsMatchCodeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from supplier_goods_match_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>

    <select id="selectGoodsNoByExample" parameterType="com.cowell.purchase.entity.SupplierGoodsMatchCodeExample" resultType="java.lang.String">
        select
        goods_code
        from supplier_goods_match_code
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
        group by goods_code
    </select>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from supplier_goods_match_code
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from supplier_goods_match_code
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SupplierGoodsMatchCodeExample">
    delete from supplier_goods_match_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SupplierGoodsMatchCode">
    insert into supplier_goods_match_code (id, out_goods_unit, goods_code,
      goods_variety_id, supplier_manufacturer_code,
      supplier_manufacturer_name, out_supplier_code,
      out_supplier_name, out_goods_code, mdm_package_num,
      supplier_package_num, status, gmt_create,
      gmt_update, extend, version,
      created_by, created_name, updated_by,
      updated_name)
    values (#{id,jdbcType=INTEGER}, #{outGoodsUnit,jdbcType=VARCHAR}, #{goodsCode,jdbcType=VARCHAR},
      #{goodsVarietyId,jdbcType=BIGINT}, #{supplierManufacturerCode,jdbcType=VARCHAR},
      #{supplierManufacturerName,jdbcType=VARCHAR}, #{outSupplierCode,jdbcType=VARCHAR},
      #{outSupplierName,jdbcType=VARCHAR}, #{outGoodsCode,jdbcType=VARCHAR}, #{mdmPackageNum,jdbcType=INTEGER},
      #{supplierPackageNum,jdbcType=INTEGER}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER},
      #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT},
      #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SupplierGoodsMatchCode">
    insert into supplier_goods_match_code
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="outGoodsUnit != null">
        out_goods_unit,
      </if>
      <if test="goodsCode != null">
        goods_code,
      </if>
      <if test="goodsVarietyId != null">
        goods_variety_id,
      </if>
      <if test="supplierManufacturerCode != null">
        supplier_manufacturer_code,
      </if>
      <if test="supplierManufacturerName != null">
        supplier_manufacturer_name,
      </if>
      <if test="outSupplierCode != null">
        out_supplier_code,
      </if>
      <if test="outSupplierName != null">
        out_supplier_name,
      </if>
      <if test="outGoodsCode != null">
        out_goods_code,
      </if>
      <if test="mdmPackageNum != null">
        mdm_package_num,
      </if>
      <if test="supplierPackageNum != null">
        supplier_package_num,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="outGoodsUnit != null">
        #{outGoodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsVarietyId != null">
        #{goodsVarietyId,jdbcType=BIGINT},
      </if>
      <if test="supplierManufacturerCode != null">
        #{supplierManufacturerCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierManufacturerName != null">
        #{supplierManufacturerName,jdbcType=VARCHAR},
      </if>
      <if test="outSupplierCode != null">
        #{outSupplierCode,jdbcType=VARCHAR},
      </if>
      <if test="outSupplierName != null">
        #{outSupplierName,jdbcType=VARCHAR},
      </if>
      <if test="outGoodsCode != null">
        #{outGoodsCode,jdbcType=VARCHAR},
      </if>
      <if test="mdmPackageNum != null">
        #{mdmPackageNum,jdbcType=INTEGER},
      </if>
      <if test="supplierPackageNum != null">
        #{supplierPackageNum,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SupplierGoodsMatchCodeExample" resultType="java.lang.Long">
    select count(*) from supplier_goods_match_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update supplier_goods_match_code
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.outGoodsUnit != null">
        out_goods_unit = #{record.outGoodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCode != null">
        goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsVarietyId != null">
        goods_variety_id = #{record.goodsVarietyId,jdbcType=BIGINT},
      </if>
      <if test="record.supplierManufacturerCode != null">
        supplier_manufacturer_code = #{record.supplierManufacturerCode,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierManufacturerName != null">
        supplier_manufacturer_name = #{record.supplierManufacturerName,jdbcType=VARCHAR},
      </if>
      <if test="record.outSupplierCode != null">
        out_supplier_code = #{record.outSupplierCode,jdbcType=VARCHAR},
      </if>
      <if test="record.outSupplierName != null">
        out_supplier_name = #{record.outSupplierName,jdbcType=VARCHAR},
      </if>
      <if test="record.outGoodsCode != null">
        out_goods_code = #{record.outGoodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.mdmPackageNum != null">
        mdm_package_num = #{record.mdmPackageNum,jdbcType=INTEGER},
      </if>
      <if test="record.supplierPackageNum != null">
        supplier_package_num = #{record.supplierPackageNum,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update supplier_goods_match_code
    set id = #{record.id,jdbcType=INTEGER},
      out_goods_unit = #{record.outGoodsUnit,jdbcType=VARCHAR},
      goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      goods_variety_id = #{record.goodsVarietyId,jdbcType=BIGINT},
      supplier_manufacturer_code = #{record.supplierManufacturerCode,jdbcType=VARCHAR},
      supplier_manufacturer_name = #{record.supplierManufacturerName,jdbcType=VARCHAR},
      out_supplier_code = #{record.outSupplierCode,jdbcType=VARCHAR},
      out_supplier_name = #{record.outSupplierName,jdbcType=VARCHAR},
      out_goods_code = #{record.outGoodsCode,jdbcType=VARCHAR},
      mdm_package_num = #{record.mdmPackageNum,jdbcType=INTEGER},
      supplier_package_num = #{record.supplierPackageNum,jdbcType=INTEGER},
      status = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SupplierGoodsMatchCode">
    update supplier_goods_match_code
    <set>
      <if test="outGoodsUnit != null">
        out_goods_unit = #{outGoodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        goods_code = #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsVarietyId != null">
        goods_variety_id = #{goodsVarietyId,jdbcType=BIGINT},
      </if>
      <if test="supplierManufacturerCode != null">
        supplier_manufacturer_code = #{supplierManufacturerCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierManufacturerName != null">
        supplier_manufacturer_name = #{supplierManufacturerName,jdbcType=VARCHAR},
      </if>
      <if test="outSupplierCode != null">
        out_supplier_code = #{outSupplierCode,jdbcType=VARCHAR},
      </if>
      <if test="outSupplierName != null">
        out_supplier_name = #{outSupplierName,jdbcType=VARCHAR},
      </if>
      <if test="outGoodsCode != null">
        out_goods_code = #{outGoodsCode,jdbcType=VARCHAR},
      </if>
      <if test="mdmPackageNum != null">
        mdm_package_num = #{mdmPackageNum,jdbcType=INTEGER},
      </if>
      <if test="supplierPackageNum != null">
        supplier_package_num = #{supplierPackageNum,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SupplierGoodsMatchCode">
    update supplier_goods_match_code
    set out_goods_unit = #{outGoodsUnit,jdbcType=VARCHAR},
      goods_code = #{goodsCode,jdbcType=VARCHAR},
      goods_variety_id = #{goodsVarietyId,jdbcType=BIGINT},
      supplier_manufacturer_code = #{supplierManufacturerCode,jdbcType=VARCHAR},
      supplier_manufacturer_name = #{supplierManufacturerName,jdbcType=VARCHAR},
      out_supplier_code = #{outSupplierCode,jdbcType=VARCHAR},
      out_supplier_name = #{outSupplierName,jdbcType=VARCHAR},
      out_goods_code = #{outGoodsCode,jdbcType=VARCHAR},
      mdm_package_num = #{mdmPackageNum,jdbcType=INTEGER},
      supplier_package_num = #{supplierPackageNum,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>
