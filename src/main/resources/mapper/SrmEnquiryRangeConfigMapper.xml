<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmEnquiryRangeConfigMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmEnquiryRangeConfig">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="increase_warn_level" jdbcType="INTEGER" property="increaseWarnLevel" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, org_id, org_name, increase_warn_level, gmt_create
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmEnquiryRangeConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_enquiry_range_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_enquiry_range_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from srm_enquiry_range_config
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmEnquiryRangeConfigExample">
    delete from srm_enquiry_range_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmEnquiryRangeConfig">
    insert into srm_enquiry_range_config (id, org_id, org_name, 
      increase_warn_level, gmt_create)
    values (#{id,jdbcType=INTEGER}, #{orgId,jdbcType=BIGINT}, #{orgName,jdbcType=VARCHAR}, 
      #{increaseWarnLevel,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmEnquiryRangeConfig">
    insert into srm_enquiry_range_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="increaseWarnLevel != null">
        increase_warn_level,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="increaseWarnLevel != null">
        #{increaseWarnLevel,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmEnquiryRangeConfigExample" resultType="java.lang.Long">
    select count(*) from srm_enquiry_range_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_enquiry_range_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.increaseWarnLevel != null">
        increase_warn_level = #{record.increaseWarnLevel,jdbcType=INTEGER},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_enquiry_range_config
    set id = #{record.id,jdbcType=INTEGER},
      org_id = #{record.orgId,jdbcType=BIGINT},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      increase_warn_level = #{record.increaseWarnLevel,jdbcType=INTEGER},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmEnquiryRangeConfig">
    update srm_enquiry_range_config
    <set>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="increaseWarnLevel != null">
        increase_warn_level = #{increaseWarnLevel,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmEnquiryRangeConfig">
    update srm_enquiry_range_config
    set org_id = #{orgId,jdbcType=BIGINT},
      org_name = #{orgName,jdbcType=VARCHAR},
      increase_warn_level = #{increaseWarnLevel,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>