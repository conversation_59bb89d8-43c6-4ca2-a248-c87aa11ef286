<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.PortalDraftPurchaseOrderDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.PortalDraftPurchaseOrderDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="draft_no" jdbcType="VARCHAR" property="draftNo" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="warehouse_addr" jdbcType="VARCHAR" property="warehouseAddr" />
    <result column="quantity" jdbcType="DECIMAL" property="quantity" />
    <result column="have_tax_price" jdbcType="DECIMAL" property="haveTaxPrice" />
    <result column="tax_code" jdbcType="VARCHAR" property="taxCode" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="habitat" jdbcType="VARCHAR" property="habitat" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, draft_no, goods_code, warehouse_addr, quantity, have_tax_price, tax_code, batch_no, 
    manufacturer, habitat, gmt_create, gmt_update, `status`, extend, version, created_by, 
    created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.PortalDraftPurchaseOrderDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from portal_draft_purchase_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from portal_draft_purchase_order_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from portal_draft_purchase_order_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.PortalDraftPurchaseOrderDetailExample">
    delete from portal_draft_purchase_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.PortalDraftPurchaseOrderDetail">
    insert into portal_draft_purchase_order_detail (id, draft_no, goods_code, 
      warehouse_addr, quantity, have_tax_price, 
      tax_code, batch_no, manufacturer, 
      habitat, gmt_create, gmt_update, 
      `status`, extend, version, 
      created_by, created_name, updated_by, 
      updated_name)
    values (#{id,jdbcType=BIGINT}, #{draftNo,jdbcType=VARCHAR}, #{goodsCode,jdbcType=VARCHAR}, 
      #{warehouseAddr,jdbcType=VARCHAR}, #{quantity,jdbcType=DECIMAL}, #{haveTaxPrice,jdbcType=DECIMAL}, 
      #{taxCode,jdbcType=VARCHAR}, #{batchNo,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, 
      #{habitat,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, 
      #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.PortalDraftPurchaseOrderDetail">
    insert into portal_draft_purchase_order_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="draftNo != null">
        draft_no,
      </if>
      <if test="goodsCode != null">
        goods_code,
      </if>
      <if test="warehouseAddr != null">
        warehouse_addr,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="haveTaxPrice != null">
        have_tax_price,
      </if>
      <if test="taxCode != null">
        tax_code,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="habitat != null">
        habitat,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="draftNo != null">
        #{draftNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseAddr != null">
        #{warehouseAddr,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=DECIMAL},
      </if>
      <if test="haveTaxPrice != null">
        #{haveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="taxCode != null">
        #{taxCode,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="habitat != null">
        #{habitat,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.PortalDraftPurchaseOrderDetailExample" resultType="java.lang.Long">
    select count(*) from portal_draft_purchase_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update portal_draft_purchase_order_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.draftNo != null">
        draft_no = #{record.draftNo,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCode != null">
        goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseAddr != null">
        warehouse_addr = #{record.warehouseAddr,jdbcType=VARCHAR},
      </if>
      <if test="record.quantity != null">
        quantity = #{record.quantity,jdbcType=DECIMAL},
      </if>
      <if test="record.haveTaxPrice != null">
        have_tax_price = #{record.haveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.taxCode != null">
        tax_code = #{record.taxCode,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.habitat != null">
        habitat = #{record.habitat,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update portal_draft_purchase_order_detail
    set id = #{record.id,jdbcType=BIGINT},
      draft_no = #{record.draftNo,jdbcType=VARCHAR},
      goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      warehouse_addr = #{record.warehouseAddr,jdbcType=VARCHAR},
      quantity = #{record.quantity,jdbcType=DECIMAL},
      have_tax_price = #{record.haveTaxPrice,jdbcType=DECIMAL},
      tax_code = #{record.taxCode,jdbcType=VARCHAR},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      habitat = #{record.habitat,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      `status` = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.PortalDraftPurchaseOrderDetail">
    update portal_draft_purchase_order_detail
    <set>
      <if test="draftNo != null">
        draft_no = #{draftNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        goods_code = #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseAddr != null">
        warehouse_addr = #{warehouseAddr,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=DECIMAL},
      </if>
      <if test="haveTaxPrice != null">
        have_tax_price = #{haveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="taxCode != null">
        tax_code = #{taxCode,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="habitat != null">
        habitat = #{habitat,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.PortalDraftPurchaseOrderDetail">
    update portal_draft_purchase_order_detail
    set draft_no = #{draftNo,jdbcType=VARCHAR},
      goods_code = #{goodsCode,jdbcType=VARCHAR},
      warehouse_addr = #{warehouseAddr,jdbcType=VARCHAR},
      quantity = #{quantity,jdbcType=DECIMAL},
      have_tax_price = #{haveTaxPrice,jdbcType=DECIMAL},
      tax_code = #{taxCode,jdbcType=VARCHAR},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      habitat = #{habitat,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>