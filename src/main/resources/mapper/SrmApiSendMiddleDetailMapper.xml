<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmApiSendMiddleDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmApiSendMiddleDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="supplier_key" jdbcType="VARCHAR" property="supplierKey" />
    <result column="supplier_out_order_no" jdbcType="VARCHAR" property="supplierOutOrderNo" />
    <result column="supplier_out_order_id" jdbcType="VARCHAR" property="supplierOutOrderId" />
    <result column="purchase_plan_no" jdbcType="VARCHAR" property="purchasePlanNo" />
    <result column="purchase_plan_id" jdbcType="VARCHAR" property="purchasePlanId" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="supplier_goods_code" jdbcType="VARCHAR" property="supplierGoodsCode" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="have_tax_price" jdbcType="DECIMAL" property="haveTaxPrice" />
    <result column="have_tax_amount" jdbcType="DECIMAL" property="haveTaxAmount" />
    <result column="tax_rate" jdbcType="DECIMAL" property="taxRate" />
    <result column="order_quantity" jdbcType="DECIMAL" property="orderQuantity" />
    <result column="this_send_quantity" jdbcType="DECIMAL" property="thisSendQuantity" />
    <result column="cost_price" jdbcType="DECIMAL" property="costPrice" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="validity_date" jdbcType="TIMESTAMP" property="validityDate" />
    <result column="produce_date" jdbcType="TIMESTAMP" property="produceDate" />
    <result column="habitat" jdbcType="VARCHAR" property="habitat" />
    <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="supervision_code" jdbcType="VARCHAR" property="supervisionCode" />
    <result column="specs_ratio" jdbcType="VARCHAR" property="specsRatio" />
    <result column="comments" jdbcType="VARCHAR" property="comments" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="supplier_purchase_count" jdbcType="DECIMAL" property="supplierPurchaseCount" />
    <result column="supplier_have_tax_price" jdbcType="DECIMAL" property="supplierHaveTaxPrice" />
    <result column="line_id" jdbcType="INTEGER" property="lineId" />
    <result column="deal_status" jdbcType="TINYINT" property="dealStatus" />
    <result column="deal_reason" jdbcType="VARCHAR" property="dealReason" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, supplier_key, supplier_out_order_no, supplier_out_order_id, purchase_plan_no, 
    purchase_plan_id, goods_code, bar_code, supplier_goods_code, goods_common_name, description, 
    specifications, dosage_form, manufacturer, have_tax_price, have_tax_amount, tax_rate, 
    order_quantity, this_send_quantity, cost_price, batch_no, validity_date, produce_date, 
    habitat, approval_number, unit, supervision_code, specs_ratio, comments, gmt_create, 
    gmt_update, supplier_purchase_count, supplier_have_tax_price, line_id, deal_status, 
    deal_reason
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmApiSendMiddleDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_api_send_middle_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_api_send_middle_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from srm_api_send_middle_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmApiSendMiddleDetailExample">
    delete from srm_api_send_middle_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmApiSendMiddleDetail">
    insert into srm_api_send_middle_detail (id, supplier_key, supplier_out_order_no, 
      supplier_out_order_id, purchase_plan_no, purchase_plan_id, 
      goods_code, bar_code, supplier_goods_code, 
      goods_common_name, description, specifications, 
      dosage_form, manufacturer, have_tax_price, 
      have_tax_amount, tax_rate, order_quantity, 
      this_send_quantity, cost_price, batch_no, 
      validity_date, produce_date, habitat, 
      approval_number, unit, supervision_code, 
      specs_ratio, comments, gmt_create, 
      gmt_update, supplier_purchase_count, supplier_have_tax_price, 
      line_id, deal_status, deal_reason
      )
    values (#{id,jdbcType=BIGINT}, #{supplierKey,jdbcType=VARCHAR}, #{supplierOutOrderNo,jdbcType=VARCHAR}, 
      #{supplierOutOrderId,jdbcType=VARCHAR}, #{purchasePlanNo,jdbcType=VARCHAR}, #{purchasePlanId,jdbcType=VARCHAR}, 
      #{goodsCode,jdbcType=VARCHAR}, #{barCode,jdbcType=VARCHAR}, #{supplierGoodsCode,jdbcType=VARCHAR}, 
      #{goodsCommonName,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{specifications,jdbcType=VARCHAR}, 
      #{dosageForm,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, #{haveTaxPrice,jdbcType=DECIMAL}, 
      #{haveTaxAmount,jdbcType=DECIMAL}, #{taxRate,jdbcType=DECIMAL}, #{orderQuantity,jdbcType=DECIMAL}, 
      #{thisSendQuantity,jdbcType=DECIMAL}, #{costPrice,jdbcType=DECIMAL}, #{batchNo,jdbcType=VARCHAR}, 
      #{validityDate,jdbcType=TIMESTAMP}, #{produceDate,jdbcType=TIMESTAMP}, #{habitat,jdbcType=VARCHAR}, 
      #{approvalNumber,jdbcType=VARCHAR}, #{unit,jdbcType=VARCHAR}, #{supervisionCode,jdbcType=VARCHAR}, 
      #{specsRatio,jdbcType=VARCHAR}, #{comments,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{supplierPurchaseCount,jdbcType=DECIMAL}, #{supplierHaveTaxPrice,jdbcType=DECIMAL}, 
      #{lineId,jdbcType=INTEGER}, #{dealStatus,jdbcType=TINYINT}, #{dealReason,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmApiSendMiddleDetail">
    insert into srm_api_send_middle_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="supplierKey != null">
        supplier_key,
      </if>
      <if test="supplierOutOrderNo != null">
        supplier_out_order_no,
      </if>
      <if test="supplierOutOrderId != null">
        supplier_out_order_id,
      </if>
      <if test="purchasePlanNo != null">
        purchase_plan_no,
      </if>
      <if test="purchasePlanId != null">
        purchase_plan_id,
      </if>
      <if test="goodsCode != null">
        goods_code,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="supplierGoodsCode != null">
        supplier_goods_code,
      </if>
      <if test="goodsCommonName != null">
        goods_common_name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="dosageForm != null">
        dosage_form,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="haveTaxPrice != null">
        have_tax_price,
      </if>
      <if test="haveTaxAmount != null">
        have_tax_amount,
      </if>
      <if test="taxRate != null">
        tax_rate,
      </if>
      <if test="orderQuantity != null">
        order_quantity,
      </if>
      <if test="thisSendQuantity != null">
        this_send_quantity,
      </if>
      <if test="costPrice != null">
        cost_price,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="validityDate != null">
        validity_date,
      </if>
      <if test="produceDate != null">
        produce_date,
      </if>
      <if test="habitat != null">
        habitat,
      </if>
      <if test="approvalNumber != null">
        approval_number,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="supervisionCode != null">
        supervision_code,
      </if>
      <if test="specsRatio != null">
        specs_ratio,
      </if>
      <if test="comments != null">
        comments,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="supplierPurchaseCount != null">
        supplier_purchase_count,
      </if>
      <if test="supplierHaveTaxPrice != null">
        supplier_have_tax_price,
      </if>
      <if test="lineId != null">
        line_id,
      </if>
      <if test="dealStatus != null">
        deal_status,
      </if>
      <if test="dealReason != null">
        deal_reason,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="supplierKey != null">
        #{supplierKey,jdbcType=VARCHAR},
      </if>
      <if test="supplierOutOrderNo != null">
        #{supplierOutOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierOutOrderId != null">
        #{supplierOutOrderId,jdbcType=VARCHAR},
      </if>
      <if test="purchasePlanNo != null">
        #{purchasePlanNo,jdbcType=VARCHAR},
      </if>
      <if test="purchasePlanId != null">
        #{purchasePlanId,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierGoodsCode != null">
        #{supplierGoodsCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="haveTaxPrice != null">
        #{haveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="haveTaxAmount != null">
        #{haveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="orderQuantity != null">
        #{orderQuantity,jdbcType=DECIMAL},
      </if>
      <if test="thisSendQuantity != null">
        #{thisSendQuantity,jdbcType=DECIMAL},
      </if>
      <if test="costPrice != null">
        #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        #{validityDate,jdbcType=TIMESTAMP},
      </if>
      <if test="produceDate != null">
        #{produceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="habitat != null">
        #{habitat,jdbcType=VARCHAR},
      </if>
      <if test="approvalNumber != null">
        #{approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="supervisionCode != null">
        #{supervisionCode,jdbcType=VARCHAR},
      </if>
      <if test="specsRatio != null">
        #{specsRatio,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="supplierPurchaseCount != null">
        #{supplierPurchaseCount,jdbcType=DECIMAL},
      </if>
      <if test="supplierHaveTaxPrice != null">
        #{supplierHaveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="lineId != null">
        #{lineId,jdbcType=INTEGER},
      </if>
      <if test="dealStatus != null">
        #{dealStatus,jdbcType=TINYINT},
      </if>
      <if test="dealReason != null">
        #{dealReason,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmApiSendMiddleDetailExample" resultType="java.lang.Long">
    select count(*) from srm_api_send_middle_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_api_send_middle_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.supplierKey != null">
        supplier_key = #{record.supplierKey,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierOutOrderNo != null">
        supplier_out_order_no = #{record.supplierOutOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierOutOrderId != null">
        supplier_out_order_id = #{record.supplierOutOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.purchasePlanNo != null">
        purchase_plan_no = #{record.purchasePlanNo,jdbcType=VARCHAR},
      </if>
      <if test="record.purchasePlanId != null">
        purchase_plan_id = #{record.purchasePlanId,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCode != null">
        goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierGoodsCode != null">
        supplier_goods_code = #{record.supplierGoodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCommonName != null">
        goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.dosageForm != null">
        dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.haveTaxPrice != null">
        have_tax_price = #{record.haveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.haveTaxAmount != null">
        have_tax_amount = #{record.haveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.taxRate != null">
        tax_rate = #{record.taxRate,jdbcType=DECIMAL},
      </if>
      <if test="record.orderQuantity != null">
        order_quantity = #{record.orderQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.thisSendQuantity != null">
        this_send_quantity = #{record.thisSendQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.costPrice != null">
        cost_price = #{record.costPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.validityDate != null">
        validity_date = #{record.validityDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.produceDate != null">
        produce_date = #{record.produceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.habitat != null">
        habitat = #{record.habitat,jdbcType=VARCHAR},
      </if>
      <if test="record.approvalNumber != null">
        approval_number = #{record.approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.supervisionCode != null">
        supervision_code = #{record.supervisionCode,jdbcType=VARCHAR},
      </if>
      <if test="record.specsRatio != null">
        specs_ratio = #{record.specsRatio,jdbcType=VARCHAR},
      </if>
      <if test="record.comments != null">
        comments = #{record.comments,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.supplierPurchaseCount != null">
        supplier_purchase_count = #{record.supplierPurchaseCount,jdbcType=DECIMAL},
      </if>
      <if test="record.supplierHaveTaxPrice != null">
        supplier_have_tax_price = #{record.supplierHaveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.lineId != null">
        line_id = #{record.lineId,jdbcType=INTEGER},
      </if>
      <if test="record.dealStatus != null">
        deal_status = #{record.dealStatus,jdbcType=TINYINT},
      </if>
      <if test="record.dealReason != null">
        deal_reason = #{record.dealReason,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_api_send_middle_detail
    set id = #{record.id,jdbcType=BIGINT},
      supplier_key = #{record.supplierKey,jdbcType=VARCHAR},
      supplier_out_order_no = #{record.supplierOutOrderNo,jdbcType=VARCHAR},
      supplier_out_order_id = #{record.supplierOutOrderId,jdbcType=VARCHAR},
      purchase_plan_no = #{record.purchasePlanNo,jdbcType=VARCHAR},
      purchase_plan_id = #{record.purchasePlanId,jdbcType=VARCHAR},
      goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      supplier_goods_code = #{record.supplierGoodsCode,jdbcType=VARCHAR},
      goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      specifications = #{record.specifications,jdbcType=VARCHAR},
      dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      have_tax_price = #{record.haveTaxPrice,jdbcType=DECIMAL},
      have_tax_amount = #{record.haveTaxAmount,jdbcType=DECIMAL},
      tax_rate = #{record.taxRate,jdbcType=DECIMAL},
      order_quantity = #{record.orderQuantity,jdbcType=DECIMAL},
      this_send_quantity = #{record.thisSendQuantity,jdbcType=DECIMAL},
      cost_price = #{record.costPrice,jdbcType=DECIMAL},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      validity_date = #{record.validityDate,jdbcType=TIMESTAMP},
      produce_date = #{record.produceDate,jdbcType=TIMESTAMP},
      habitat = #{record.habitat,jdbcType=VARCHAR},
      approval_number = #{record.approvalNumber,jdbcType=VARCHAR},
      unit = #{record.unit,jdbcType=VARCHAR},
      supervision_code = #{record.supervisionCode,jdbcType=VARCHAR},
      specs_ratio = #{record.specsRatio,jdbcType=VARCHAR},
      comments = #{record.comments,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      supplier_purchase_count = #{record.supplierPurchaseCount,jdbcType=DECIMAL},
      supplier_have_tax_price = #{record.supplierHaveTaxPrice,jdbcType=DECIMAL},
      line_id = #{record.lineId,jdbcType=INTEGER},
      deal_status = #{record.dealStatus,jdbcType=TINYINT},
      deal_reason = #{record.dealReason,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmApiSendMiddleDetail">
    update srm_api_send_middle_detail
    <set>
      <if test="supplierKey != null">
        supplier_key = #{supplierKey,jdbcType=VARCHAR},
      </if>
      <if test="supplierOutOrderNo != null">
        supplier_out_order_no = #{supplierOutOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierOutOrderId != null">
        supplier_out_order_id = #{supplierOutOrderId,jdbcType=VARCHAR},
      </if>
      <if test="purchasePlanNo != null">
        purchase_plan_no = #{purchasePlanNo,jdbcType=VARCHAR},
      </if>
      <if test="purchasePlanId != null">
        purchase_plan_id = #{purchasePlanId,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        goods_code = #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierGoodsCode != null">
        supplier_goods_code = #{supplierGoodsCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        dosage_form = #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="haveTaxPrice != null">
        have_tax_price = #{haveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="haveTaxAmount != null">
        have_tax_amount = #{haveTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxRate != null">
        tax_rate = #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="orderQuantity != null">
        order_quantity = #{orderQuantity,jdbcType=DECIMAL},
      </if>
      <if test="thisSendQuantity != null">
        this_send_quantity = #{thisSendQuantity,jdbcType=DECIMAL},
      </if>
      <if test="costPrice != null">
        cost_price = #{costPrice,jdbcType=DECIMAL},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        validity_date = #{validityDate,jdbcType=TIMESTAMP},
      </if>
      <if test="produceDate != null">
        produce_date = #{produceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="habitat != null">
        habitat = #{habitat,jdbcType=VARCHAR},
      </if>
      <if test="approvalNumber != null">
        approval_number = #{approvalNumber,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="supervisionCode != null">
        supervision_code = #{supervisionCode,jdbcType=VARCHAR},
      </if>
      <if test="specsRatio != null">
        specs_ratio = #{specsRatio,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        comments = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="supplierPurchaseCount != null">
        supplier_purchase_count = #{supplierPurchaseCount,jdbcType=DECIMAL},
      </if>
      <if test="supplierHaveTaxPrice != null">
        supplier_have_tax_price = #{supplierHaveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="lineId != null">
        line_id = #{lineId,jdbcType=INTEGER},
      </if>
      <if test="dealStatus != null">
        deal_status = #{dealStatus,jdbcType=TINYINT},
      </if>
      <if test="dealReason != null">
        deal_reason = #{dealReason,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmApiSendMiddleDetail">
    update srm_api_send_middle_detail
    set supplier_key = #{supplierKey,jdbcType=VARCHAR},
      supplier_out_order_no = #{supplierOutOrderNo,jdbcType=VARCHAR},
      supplier_out_order_id = #{supplierOutOrderId,jdbcType=VARCHAR},
      purchase_plan_no = #{purchasePlanNo,jdbcType=VARCHAR},
      purchase_plan_id = #{purchasePlanId,jdbcType=VARCHAR},
      goods_code = #{goodsCode,jdbcType=VARCHAR},
      bar_code = #{barCode,jdbcType=VARCHAR},
      supplier_goods_code = #{supplierGoodsCode,jdbcType=VARCHAR},
      goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      specifications = #{specifications,jdbcType=VARCHAR},
      dosage_form = #{dosageForm,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      have_tax_price = #{haveTaxPrice,jdbcType=DECIMAL},
      have_tax_amount = #{haveTaxAmount,jdbcType=DECIMAL},
      tax_rate = #{taxRate,jdbcType=DECIMAL},
      order_quantity = #{orderQuantity,jdbcType=DECIMAL},
      this_send_quantity = #{thisSendQuantity,jdbcType=DECIMAL},
      cost_price = #{costPrice,jdbcType=DECIMAL},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      validity_date = #{validityDate,jdbcType=TIMESTAMP},
      produce_date = #{produceDate,jdbcType=TIMESTAMP},
      habitat = #{habitat,jdbcType=VARCHAR},
      approval_number = #{approvalNumber,jdbcType=VARCHAR},
      unit = #{unit,jdbcType=VARCHAR},
      supervision_code = #{supervisionCode,jdbcType=VARCHAR},
      specs_ratio = #{specsRatio,jdbcType=VARCHAR},
      comments = #{comments,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      supplier_purchase_count = #{supplierPurchaseCount,jdbcType=DECIMAL},
      supplier_have_tax_price = #{supplierHaveTaxPrice,jdbcType=DECIMAL},
      line_id = #{lineId,jdbcType=INTEGER},
      deal_status = #{dealStatus,jdbcType=TINYINT},
      deal_reason = #{dealReason,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>