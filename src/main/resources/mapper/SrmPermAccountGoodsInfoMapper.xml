<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmPermAccountGoodsInfoMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmPermAccountGoodsInfo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_no" jdbcType="VARCHAR" property="accountNo" />
    <result column="account_class" jdbcType="INTEGER" property="accountClass" />
    <result column="account_data_type" jdbcType="INTEGER" property="accountDataType" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="replace_goods_no" jdbcType="VARCHAR" property="replaceGoodsNo" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_no, account_class, account_data_type, goods_no, replace_goods_no, bar_code, 
    goods_common_name, goods_name, description, specifications, dosage_form, manufacturer, 
    gmt_create, gmt_update, `status`, extend, version
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmPermAccountGoodsInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_perm_account_goods_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_perm_account_goods_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from srm_perm_account_goods_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmPermAccountGoodsInfoExample">
    delete from srm_perm_account_goods_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmPermAccountGoodsInfo">
    insert into srm_perm_account_goods_info (id, account_no, account_class, 
      account_data_type, goods_no, replace_goods_no, 
      bar_code, goods_common_name, goods_name, 
      description, specifications, dosage_form, 
      manufacturer, gmt_create, gmt_update, 
      `status`, extend, version
      )
    values (#{id,jdbcType=INTEGER}, #{accountNo,jdbcType=VARCHAR}, #{accountClass,jdbcType=INTEGER}, 
      #{accountDataType,jdbcType=INTEGER}, #{goodsNo,jdbcType=VARCHAR}, #{replaceGoodsNo,jdbcType=VARCHAR}, 
      #{barCode,jdbcType=VARCHAR}, #{goodsCommonName,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{specifications,jdbcType=VARCHAR}, #{dosageForm,jdbcType=VARCHAR}, 
      #{manufacturer,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmPermAccountGoodsInfo">
    insert into srm_perm_account_goods_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="accountNo != null">
        account_no,
      </if>
      <if test="accountClass != null">
        account_class,
      </if>
      <if test="accountDataType != null">
        account_data_type,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="replaceGoodsNo != null">
        replace_goods_no,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="goodsCommonName != null">
        goods_common_name,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="dosageForm != null">
        dosage_form,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="accountNo != null">
        #{accountNo,jdbcType=VARCHAR},
      </if>
      <if test="accountClass != null">
        #{accountClass,jdbcType=INTEGER},
      </if>
      <if test="accountDataType != null">
        #{accountDataType,jdbcType=INTEGER},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="replaceGoodsNo != null">
        #{replaceGoodsNo,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmPermAccountGoodsInfoExample" resultType="java.lang.Long">
    select count(*) from srm_perm_account_goods_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_perm_account_goods_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountNo != null">
        account_no = #{record.accountNo,jdbcType=VARCHAR},
      </if>
      <if test="record.accountClass != null">
        account_class = #{record.accountClass,jdbcType=INTEGER},
      </if>
      <if test="record.accountDataType != null">
        account_data_type = #{record.accountDataType,jdbcType=INTEGER},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.replaceGoodsNo != null">
        replace_goods_no = #{record.replaceGoodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCommonName != null">
        goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.dosageForm != null">
        dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_perm_account_goods_info
    set id = #{record.id,jdbcType=INTEGER},
      account_no = #{record.accountNo,jdbcType=VARCHAR},
      account_class = #{record.accountClass,jdbcType=INTEGER},
      account_data_type = #{record.accountDataType,jdbcType=INTEGER},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      replace_goods_no = #{record.replaceGoodsNo,jdbcType=VARCHAR},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      specifications = #{record.specifications,jdbcType=VARCHAR},
      dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      `status` = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmPermAccountGoodsInfo">
    update srm_perm_account_goods_info
    <set>
      <if test="accountNo != null">
        account_no = #{accountNo,jdbcType=VARCHAR},
      </if>
      <if test="accountClass != null">
        account_class = #{accountClass,jdbcType=INTEGER},
      </if>
      <if test="accountDataType != null">
        account_data_type = #{accountDataType,jdbcType=INTEGER},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="replaceGoodsNo != null">
        replace_goods_no = #{replaceGoodsNo,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        dosage_form = #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmPermAccountGoodsInfo">
    update srm_perm_account_goods_info
    set account_no = #{accountNo,jdbcType=VARCHAR},
      account_class = #{accountClass,jdbcType=INTEGER},
      account_data_type = #{accountDataType,jdbcType=INTEGER},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      replace_goods_no = #{replaceGoodsNo,jdbcType=VARCHAR},
      bar_code = #{barCode,jdbcType=VARCHAR},
      goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      specifications = #{specifications,jdbcType=VARCHAR},
      dosage_form = #{dosageForm,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>