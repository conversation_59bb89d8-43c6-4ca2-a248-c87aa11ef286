<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SupplierCompanyRelationMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SupplierCompanyRelation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="supplier_no" jdbcType="VARCHAR" property="supplierNo" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="recaccount" jdbcType="VARCHAR" property="recaccount" />
    <result column="old_bp_no" jdbcType="VARCHAR" property="oldBpNo" />
    <result column="old_bp_name" jdbcType="VARCHAR" property="oldBpName" />
    <result column="abcind" jdbcType="VARCHAR" property="abcind" />
    <result column="comfreez" jdbcType="VARCHAR" property="comfreez" />
    <result column="deleteind" jdbcType="VARCHAR" property="deleteind" />
    <result column="supplier_lead_time" jdbcType="DECIMAL" property="supplierLeadTime" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="bpaycondition" jdbcType="VARCHAR" property="bpaycondition" />
    <result column="spaycondition" jdbcType="VARCHAR" property="spaycondition" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, supplier_no, company_code, recaccount, old_bp_no, old_bp_name, abcind, comfreez, 
    deleteind, supplier_lead_time, version, created_by, gmt_create, updated_by, gmt_update, 
    extend, `status`, bpaycondition, spaycondition
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SupplierCompanyRelationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from supplier_company_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from supplier_company_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from supplier_company_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SupplierCompanyRelationExample">
    delete from supplier_company_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SupplierCompanyRelation">
    insert into supplier_company_relation (id, supplier_no, company_code, 
      recaccount, old_bp_no, old_bp_name, 
      abcind, comfreez, deleteind, 
      supplier_lead_time, version, created_by, 
      gmt_create, updated_by, gmt_update, 
      extend, `status`, bpaycondition, 
      spaycondition)
    values (#{id,jdbcType=BIGINT}, #{supplierNo,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR}, 
      #{recaccount,jdbcType=VARCHAR}, #{oldBpNo,jdbcType=VARCHAR}, #{oldBpName,jdbcType=VARCHAR}, 
      #{abcind,jdbcType=VARCHAR}, #{comfreez,jdbcType=VARCHAR}, #{deleteind,jdbcType=VARCHAR}, 
      #{supplierLeadTime,jdbcType=DECIMAL}, #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{gmtUpdate,jdbcType=TIMESTAMP}, 
      #{extend,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{bpaycondition,jdbcType=VARCHAR}, 
      #{spaycondition,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SupplierCompanyRelation">
    insert into supplier_company_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="supplierNo != null">
        supplier_no,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="recaccount != null">
        recaccount,
      </if>
      <if test="oldBpNo != null">
        old_bp_no,
      </if>
      <if test="oldBpName != null">
        old_bp_name,
      </if>
      <if test="abcind != null">
        abcind,
      </if>
      <if test="comfreez != null">
        comfreez,
      </if>
      <if test="deleteind != null">
        deleteind,
      </if>
      <if test="supplierLeadTime != null">
        supplier_lead_time,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="bpaycondition != null">
        bpaycondition,
      </if>
      <if test="spaycondition != null">
        spaycondition,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="supplierNo != null">
        #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="recaccount != null">
        #{recaccount,jdbcType=VARCHAR},
      </if>
      <if test="oldBpNo != null">
        #{oldBpNo,jdbcType=VARCHAR},
      </if>
      <if test="oldBpName != null">
        #{oldBpName,jdbcType=VARCHAR},
      </if>
      <if test="abcind != null">
        #{abcind,jdbcType=VARCHAR},
      </if>
      <if test="comfreez != null">
        #{comfreez,jdbcType=VARCHAR},
      </if>
      <if test="deleteind != null">
        #{deleteind,jdbcType=VARCHAR},
      </if>
      <if test="supplierLeadTime != null">
        #{supplierLeadTime,jdbcType=DECIMAL},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="bpaycondition != null">
        #{bpaycondition,jdbcType=VARCHAR},
      </if>
      <if test="spaycondition != null">
        #{spaycondition,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SupplierCompanyRelationExample" resultType="java.lang.Long">
    select count(*) from supplier_company_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update supplier_company_relation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.supplierNo != null">
        supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.recaccount != null">
        recaccount = #{record.recaccount,jdbcType=VARCHAR},
      </if>
      <if test="record.oldBpNo != null">
        old_bp_no = #{record.oldBpNo,jdbcType=VARCHAR},
      </if>
      <if test="record.oldBpName != null">
        old_bp_name = #{record.oldBpName,jdbcType=VARCHAR},
      </if>
      <if test="record.abcind != null">
        abcind = #{record.abcind,jdbcType=VARCHAR},
      </if>
      <if test="record.comfreez != null">
        comfreez = #{record.comfreez,jdbcType=VARCHAR},
      </if>
      <if test="record.deleteind != null">
        deleteind = #{record.deleteind,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierLeadTime != null">
        supplier_lead_time = #{record.supplierLeadTime,jdbcType=DECIMAL},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.bpaycondition != null">
        bpaycondition = #{record.bpaycondition,jdbcType=VARCHAR},
      </if>
      <if test="record.spaycondition != null">
        spaycondition = #{record.spaycondition,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update supplier_company_relation
    set id = #{record.id,jdbcType=BIGINT},
      supplier_no = #{record.supplierNo,jdbcType=VARCHAR},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      recaccount = #{record.recaccount,jdbcType=VARCHAR},
      old_bp_no = #{record.oldBpNo,jdbcType=VARCHAR},
      old_bp_name = #{record.oldBpName,jdbcType=VARCHAR},
      abcind = #{record.abcind,jdbcType=VARCHAR},
      comfreez = #{record.comfreez,jdbcType=VARCHAR},
      deleteind = #{record.deleteind,jdbcType=VARCHAR},
      supplier_lead_time = #{record.supplierLeadTime,jdbcType=DECIMAL},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=TINYINT},
      bpaycondition = #{record.bpaycondition,jdbcType=VARCHAR},
      spaycondition = #{record.spaycondition,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SupplierCompanyRelation">
    update supplier_company_relation
    <set>
      <if test="supplierNo != null">
        supplier_no = #{supplierNo,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="recaccount != null">
        recaccount = #{recaccount,jdbcType=VARCHAR},
      </if>
      <if test="oldBpNo != null">
        old_bp_no = #{oldBpNo,jdbcType=VARCHAR},
      </if>
      <if test="oldBpName != null">
        old_bp_name = #{oldBpName,jdbcType=VARCHAR},
      </if>
      <if test="abcind != null">
        abcind = #{abcind,jdbcType=VARCHAR},
      </if>
      <if test="comfreez != null">
        comfreez = #{comfreez,jdbcType=VARCHAR},
      </if>
      <if test="deleteind != null">
        deleteind = #{deleteind,jdbcType=VARCHAR},
      </if>
      <if test="supplierLeadTime != null">
        supplier_lead_time = #{supplierLeadTime,jdbcType=DECIMAL},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="bpaycondition != null">
        bpaycondition = #{bpaycondition,jdbcType=VARCHAR},
      </if>
      <if test="spaycondition != null">
        spaycondition = #{spaycondition,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SupplierCompanyRelation">
    update supplier_company_relation
    set supplier_no = #{supplierNo,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      recaccount = #{recaccount,jdbcType=VARCHAR},
      old_bp_no = #{oldBpNo,jdbcType=VARCHAR},
      old_bp_name = #{oldBpName,jdbcType=VARCHAR},
      abcind = #{abcind,jdbcType=VARCHAR},
      comfreez = #{comfreez,jdbcType=VARCHAR},
      deleteind = #{deleteind,jdbcType=VARCHAR},
      supplier_lead_time = #{supplierLeadTime,jdbcType=DECIMAL},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      bpaycondition = #{bpaycondition,jdbcType=VARCHAR},
      spaycondition = #{spaycondition,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>