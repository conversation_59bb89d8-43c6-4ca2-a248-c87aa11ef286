<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmBillOrderDetailsMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmBillOrderDetails">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="bill_order_no" jdbcType="VARCHAR" property="billOrderNo" />
    <result column="purchase_order_no" jdbcType="VARCHAR" property="purchaseOrderNo" />
    <result column="purchase_order_id" jdbcType="VARCHAR" property="purchaseOrderId" />
    <result column="purchase_delivery_id" jdbcType="INTEGER" property="purchaseDeliveryId" />
    <result column="delivery_no" jdbcType="VARCHAR" property="deliveryNo" />
    <result column="delivery_id" jdbcType="VARCHAR" property="deliveryId" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="goods_common_name" jdbcType="VARCHAR" property="goodsCommonName" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="dosage_form" jdbcType="VARCHAR" property="dosageForm" />
    <result column="habitat" jdbcType="VARCHAR" property="habitat" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="bill_count" jdbcType="INTEGER" property="billCount" />
    <result column="all_amount" jdbcType="DECIMAL" property="allAmount" />
    <result column="paid_amount" jdbcType="DECIMAL" property="paidAmount" />
    <result column="input_storage_count" jdbcType="INTEGER" property="inputStorageCount" />
    <result column="sales_count" jdbcType="INTEGER" property="salesCount" />
    <result column="may_bill_count" jdbcType="INTEGER" property="mayBillCount" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="purchase_count" jdbcType="INTEGER" property="purchaseCount" />
    <result column="no_tax_amount" jdbcType="DECIMAL" property="noTaxAmount" />
    <result column="no_tax_purchase_amount" jdbcType="DECIMAL" property="noTaxPurchaseAmount" />
    <result column="move_type" jdbcType="INTEGER" property="moveType" />
    <result column="move_type_name" jdbcType="VARCHAR" property="moveTypeName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, bill_order_no, purchase_order_no, purchase_order_id, purchase_delivery_id, delivery_no, 
    delivery_id, goods_code, bar_code, goods_common_name, goods_name, specifications, 
    dosage_form, habitat, manufacturer, bill_count, all_amount, paid_amount, input_storage_count, 
    sales_count, may_bill_count, gmt_create, purchase_count, no_tax_amount, no_tax_purchase_amount, 
    move_type, move_type_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmBillOrderDetailsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_bill_order_details
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_bill_order_details
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from srm_bill_order_details
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmBillOrderDetailsExample">
    delete from srm_bill_order_details
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmBillOrderDetails">
    insert into srm_bill_order_details (id, bill_order_no, purchase_order_no, 
      purchase_order_id, purchase_delivery_id, delivery_no, 
      delivery_id, goods_code, bar_code, 
      goods_common_name, goods_name, specifications, 
      dosage_form, habitat, manufacturer, 
      bill_count, all_amount, paid_amount, 
      input_storage_count, sales_count, may_bill_count, 
      gmt_create, purchase_count, no_tax_amount, 
      no_tax_purchase_amount, move_type, move_type_name
      )
    values (#{id,jdbcType=INTEGER}, #{billOrderNo,jdbcType=VARCHAR}, #{purchaseOrderNo,jdbcType=VARCHAR}, 
      #{purchaseOrderId,jdbcType=VARCHAR}, #{purchaseDeliveryId,jdbcType=INTEGER}, #{deliveryNo,jdbcType=VARCHAR}, 
      #{deliveryId,jdbcType=VARCHAR}, #{goodsCode,jdbcType=VARCHAR}, #{barCode,jdbcType=VARCHAR}, 
      #{goodsCommonName,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, #{specifications,jdbcType=VARCHAR}, 
      #{dosageForm,jdbcType=VARCHAR}, #{habitat,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR}, 
      #{billCount,jdbcType=INTEGER}, #{allAmount,jdbcType=DECIMAL}, #{paidAmount,jdbcType=DECIMAL}, 
      #{inputStorageCount,jdbcType=INTEGER}, #{salesCount,jdbcType=INTEGER}, #{mayBillCount,jdbcType=INTEGER}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{purchaseCount,jdbcType=INTEGER}, #{noTaxAmount,jdbcType=DECIMAL}, 
      #{noTaxPurchaseAmount,jdbcType=DECIMAL}, #{moveType,jdbcType=INTEGER}, #{moveTypeName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmBillOrderDetails">
    insert into srm_bill_order_details
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="billOrderNo != null">
        bill_order_no,
      </if>
      <if test="purchaseOrderNo != null">
        purchase_order_no,
      </if>
      <if test="purchaseOrderId != null">
        purchase_order_id,
      </if>
      <if test="purchaseDeliveryId != null">
        purchase_delivery_id,
      </if>
      <if test="deliveryNo != null">
        delivery_no,
      </if>
      <if test="deliveryId != null">
        delivery_id,
      </if>
      <if test="goodsCode != null">
        goods_code,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="goodsCommonName != null">
        goods_common_name,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="dosageForm != null">
        dosage_form,
      </if>
      <if test="habitat != null">
        habitat,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="billCount != null">
        bill_count,
      </if>
      <if test="allAmount != null">
        all_amount,
      </if>
      <if test="paidAmount != null">
        paid_amount,
      </if>
      <if test="inputStorageCount != null">
        input_storage_count,
      </if>
      <if test="salesCount != null">
        sales_count,
      </if>
      <if test="mayBillCount != null">
        may_bill_count,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="purchaseCount != null">
        purchase_count,
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount,
      </if>
      <if test="noTaxPurchaseAmount != null">
        no_tax_purchase_amount,
      </if>
      <if test="moveType != null">
        move_type,
      </if>
      <if test="moveTypeName != null">
        move_type_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="billOrderNo != null">
        #{billOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderNo != null">
        #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderId != null">
        #{purchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="purchaseDeliveryId != null">
        #{purchaseDeliveryId,jdbcType=INTEGER},
      </if>
      <if test="deliveryNo != null">
        #{deliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryId != null">
        #{deliveryId,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="habitat != null">
        #{habitat,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="billCount != null">
        #{billCount,jdbcType=INTEGER},
      </if>
      <if test="allAmount != null">
        #{allAmount,jdbcType=DECIMAL},
      </if>
      <if test="paidAmount != null">
        #{paidAmount,jdbcType=DECIMAL},
      </if>
      <if test="inputStorageCount != null">
        #{inputStorageCount,jdbcType=INTEGER},
      </if>
      <if test="salesCount != null">
        #{salesCount,jdbcType=INTEGER},
      </if>
      <if test="mayBillCount != null">
        #{mayBillCount,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="purchaseCount != null">
        #{purchaseCount,jdbcType=INTEGER},
      </if>
      <if test="noTaxAmount != null">
        #{noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="noTaxPurchaseAmount != null">
        #{noTaxPurchaseAmount,jdbcType=DECIMAL},
      </if>
      <if test="moveType != null">
        #{moveType,jdbcType=INTEGER},
      </if>
      <if test="moveTypeName != null">
        #{moveTypeName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmBillOrderDetailsExample" resultType="java.lang.Long">
    select count(*) from srm_bill_order_details
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_bill_order_details
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.billOrderNo != null">
        bill_order_no = #{record.billOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrderNo != null">
        purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrderId != null">
        purchase_order_id = #{record.purchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseDeliveryId != null">
        purchase_delivery_id = #{record.purchaseDeliveryId,jdbcType=INTEGER},
      </if>
      <if test="record.deliveryNo != null">
        delivery_no = #{record.deliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="record.deliveryId != null">
        delivery_id = #{record.deliveryId,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCode != null">
        goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCommonName != null">
        goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.dosageForm != null">
        dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="record.habitat != null">
        habitat = #{record.habitat,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.billCount != null">
        bill_count = #{record.billCount,jdbcType=INTEGER},
      </if>
      <if test="record.allAmount != null">
        all_amount = #{record.allAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.paidAmount != null">
        paid_amount = #{record.paidAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.inputStorageCount != null">
        input_storage_count = #{record.inputStorageCount,jdbcType=INTEGER},
      </if>
      <if test="record.salesCount != null">
        sales_count = #{record.salesCount,jdbcType=INTEGER},
      </if>
      <if test="record.mayBillCount != null">
        may_bill_count = #{record.mayBillCount,jdbcType=INTEGER},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.purchaseCount != null">
        purchase_count = #{record.purchaseCount,jdbcType=INTEGER},
      </if>
      <if test="record.noTaxAmount != null">
        no_tax_amount = #{record.noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.noTaxPurchaseAmount != null">
        no_tax_purchase_amount = #{record.noTaxPurchaseAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.moveType != null">
        move_type = #{record.moveType,jdbcType=INTEGER},
      </if>
      <if test="record.moveTypeName != null">
        move_type_name = #{record.moveTypeName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_bill_order_details
    set id = #{record.id,jdbcType=INTEGER},
      bill_order_no = #{record.billOrderNo,jdbcType=VARCHAR},
      purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      purchase_order_id = #{record.purchaseOrderId,jdbcType=VARCHAR},
      purchase_delivery_id = #{record.purchaseDeliveryId,jdbcType=INTEGER},
      delivery_no = #{record.deliveryNo,jdbcType=VARCHAR},
      delivery_id = #{record.deliveryId,jdbcType=VARCHAR},
      goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      goods_common_name = #{record.goodsCommonName,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      specifications = #{record.specifications,jdbcType=VARCHAR},
      dosage_form = #{record.dosageForm,jdbcType=VARCHAR},
      habitat = #{record.habitat,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      bill_count = #{record.billCount,jdbcType=INTEGER},
      all_amount = #{record.allAmount,jdbcType=DECIMAL},
      paid_amount = #{record.paidAmount,jdbcType=DECIMAL},
      input_storage_count = #{record.inputStorageCount,jdbcType=INTEGER},
      sales_count = #{record.salesCount,jdbcType=INTEGER},
      may_bill_count = #{record.mayBillCount,jdbcType=INTEGER},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      purchase_count = #{record.purchaseCount,jdbcType=INTEGER},
      no_tax_amount = #{record.noTaxAmount,jdbcType=DECIMAL},
      no_tax_purchase_amount = #{record.noTaxPurchaseAmount,jdbcType=DECIMAL},
      move_type = #{record.moveType,jdbcType=INTEGER},
      move_type_name = #{record.moveTypeName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmBillOrderDetails">
    update srm_bill_order_details
    <set>
      <if test="billOrderNo != null">
        bill_order_no = #{billOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderNo != null">
        purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderId != null">
        purchase_order_id = #{purchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="purchaseDeliveryId != null">
        purchase_delivery_id = #{purchaseDeliveryId,jdbcType=INTEGER},
      </if>
      <if test="deliveryNo != null">
        delivery_no = #{deliveryNo,jdbcType=VARCHAR},
      </if>
      <if test="deliveryId != null">
        delivery_id = #{deliveryId,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        goods_code = #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsCommonName != null">
        goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="dosageForm != null">
        dosage_form = #{dosageForm,jdbcType=VARCHAR},
      </if>
      <if test="habitat != null">
        habitat = #{habitat,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="billCount != null">
        bill_count = #{billCount,jdbcType=INTEGER},
      </if>
      <if test="allAmount != null">
        all_amount = #{allAmount,jdbcType=DECIMAL},
      </if>
      <if test="paidAmount != null">
        paid_amount = #{paidAmount,jdbcType=DECIMAL},
      </if>
      <if test="inputStorageCount != null">
        input_storage_count = #{inputStorageCount,jdbcType=INTEGER},
      </if>
      <if test="salesCount != null">
        sales_count = #{salesCount,jdbcType=INTEGER},
      </if>
      <if test="mayBillCount != null">
        may_bill_count = #{mayBillCount,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="purchaseCount != null">
        purchase_count = #{purchaseCount,jdbcType=INTEGER},
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount = #{noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="noTaxPurchaseAmount != null">
        no_tax_purchase_amount = #{noTaxPurchaseAmount,jdbcType=DECIMAL},
      </if>
      <if test="moveType != null">
        move_type = #{moveType,jdbcType=INTEGER},
      </if>
      <if test="moveTypeName != null">
        move_type_name = #{moveTypeName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmBillOrderDetails">
    update srm_bill_order_details
    set bill_order_no = #{billOrderNo,jdbcType=VARCHAR},
      purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      purchase_order_id = #{purchaseOrderId,jdbcType=VARCHAR},
      purchase_delivery_id = #{purchaseDeliveryId,jdbcType=INTEGER},
      delivery_no = #{deliveryNo,jdbcType=VARCHAR},
      delivery_id = #{deliveryId,jdbcType=VARCHAR},
      goods_code = #{goodsCode,jdbcType=VARCHAR},
      bar_code = #{barCode,jdbcType=VARCHAR},
      goods_common_name = #{goodsCommonName,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      specifications = #{specifications,jdbcType=VARCHAR},
      dosage_form = #{dosageForm,jdbcType=VARCHAR},
      habitat = #{habitat,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      bill_count = #{billCount,jdbcType=INTEGER},
      all_amount = #{allAmount,jdbcType=DECIMAL},
      paid_amount = #{paidAmount,jdbcType=DECIMAL},
      input_storage_count = #{inputStorageCount,jdbcType=INTEGER},
      sales_count = #{salesCount,jdbcType=INTEGER},
      may_bill_count = #{mayBillCount,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      purchase_count = #{purchaseCount,jdbcType=INTEGER},
      no_tax_amount = #{noTaxAmount,jdbcType=DECIMAL},
      no_tax_purchase_amount = #{noTaxPurchaseAmount,jdbcType=DECIMAL},
      move_type = #{moveType,jdbcType=INTEGER},
      move_type_name = #{moveTypeName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>