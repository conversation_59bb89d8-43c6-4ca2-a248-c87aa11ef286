<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SettleBillOrderDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SettleBillOrderDetail">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="bill_order_no" jdbcType="VARCHAR" property="billOrderNo" />
    <result column="service_name" jdbcType="VARCHAR" property="serviceName" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="bill_count" jdbcType="DECIMAL" property="billCount" />
    <result column="bill_unit" jdbcType="VARCHAR" property="billUnit" />
    <result column="no_tax_price" jdbcType="DECIMAL" property="noTaxPrice" />
    <result column="no_tax_amount" jdbcType="DECIMAL" property="noTaxAmount" />
    <result column="tax_amount" jdbcType="DECIMAL" property="taxAmount" />
    <result column="tax_rate" jdbcType="DECIMAL" property="taxRate" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, bill_order_no, service_name, specifications, bill_count, bill_unit, no_tax_price, 
    no_tax_amount, tax_amount, tax_rate, gmt_create, created_by, create_name, gmt_update, 
    operator, update_by
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SettleBillOrderDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from settle_bill_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from settle_bill_order_detail
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from settle_bill_order_detail
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SettleBillOrderDetailExample">
    delete from settle_bill_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SettleBillOrderDetail">
    insert into settle_bill_order_detail (id, bill_order_no, service_name, 
      specifications, bill_count, bill_unit, 
      no_tax_price, no_tax_amount, tax_amount, 
      tax_rate, gmt_create, created_by, 
      create_name, gmt_update, operator, 
      update_by)
    values (#{id,jdbcType=INTEGER}, #{billOrderNo,jdbcType=VARCHAR}, #{serviceName,jdbcType=VARCHAR}, 
      #{specifications,jdbcType=VARCHAR}, #{billCount,jdbcType=DECIMAL}, #{billUnit,jdbcType=VARCHAR}, 
      #{noTaxPrice,jdbcType=DECIMAL}, #{noTaxAmount,jdbcType=DECIMAL}, #{taxAmount,jdbcType=DECIMAL}, 
      #{taxRate,jdbcType=DECIMAL}, #{gmtCreate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=BIGINT}, 
      #{createName,jdbcType=VARCHAR}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{operator,jdbcType=VARCHAR}, 
      #{updateBy,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SettleBillOrderDetail">
    insert into settle_bill_order_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="billOrderNo != null">
        bill_order_no,
      </if>
      <if test="serviceName != null">
        service_name,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="billCount != null">
        bill_count,
      </if>
      <if test="billUnit != null">
        bill_unit,
      </if>
      <if test="noTaxPrice != null">
        no_tax_price,
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount,
      </if>
      <if test="taxAmount != null">
        tax_amount,
      </if>
      <if test="taxRate != null">
        tax_rate,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="billOrderNo != null">
        #{billOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="serviceName != null">
        #{serviceName,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="billCount != null">
        #{billCount,jdbcType=DECIMAL},
      </if>
      <if test="billUnit != null">
        #{billUnit,jdbcType=VARCHAR},
      </if>
      <if test="noTaxPrice != null">
        #{noTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="noTaxAmount != null">
        #{noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SettleBillOrderDetailExample" resultType="java.lang.Long">
    select count(*) from settle_bill_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update settle_bill_order_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.billOrderNo != null">
        bill_order_no = #{record.billOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.serviceName != null">
        service_name = #{record.serviceName,jdbcType=VARCHAR},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.billCount != null">
        bill_count = #{record.billCount,jdbcType=DECIMAL},
      </if>
      <if test="record.billUnit != null">
        bill_unit = #{record.billUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.noTaxPrice != null">
        no_tax_price = #{record.noTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.noTaxAmount != null">
        no_tax_amount = #{record.noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.taxAmount != null">
        tax_amount = #{record.taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.taxRate != null">
        tax_rate = #{record.taxRate,jdbcType=DECIMAL},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createName != null">
        create_name = #{record.createName,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operator != null">
        operator = #{record.operator,jdbcType=VARCHAR},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update settle_bill_order_detail
    set id = #{record.id,jdbcType=INTEGER},
      bill_order_no = #{record.billOrderNo,jdbcType=VARCHAR},
      service_name = #{record.serviceName,jdbcType=VARCHAR},
      specifications = #{record.specifications,jdbcType=VARCHAR},
      bill_count = #{record.billCount,jdbcType=DECIMAL},
      bill_unit = #{record.billUnit,jdbcType=VARCHAR},
      no_tax_price = #{record.noTaxPrice,jdbcType=DECIMAL},
      no_tax_amount = #{record.noTaxAmount,jdbcType=DECIMAL},
      tax_amount = #{record.taxAmount,jdbcType=DECIMAL},
      tax_rate = #{record.taxRate,jdbcType=DECIMAL},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      create_name = #{record.createName,jdbcType=VARCHAR},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      operator = #{record.operator,jdbcType=VARCHAR},
      update_by = #{record.updateBy,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SettleBillOrderDetail">
    update settle_bill_order_detail
    <set>
      <if test="billOrderNo != null">
        bill_order_no = #{billOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="serviceName != null">
        service_name = #{serviceName,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="billCount != null">
        bill_count = #{billCount,jdbcType=DECIMAL},
      </if>
      <if test="billUnit != null">
        bill_unit = #{billUnit,jdbcType=VARCHAR},
      </if>
      <if test="noTaxPrice != null">
        no_tax_price = #{noTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount = #{noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxAmount != null">
        tax_amount = #{taxAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxRate != null">
        tax_rate = #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SettleBillOrderDetail">
    update settle_bill_order_detail
    set bill_order_no = #{billOrderNo,jdbcType=VARCHAR},
      service_name = #{serviceName,jdbcType=VARCHAR},
      specifications = #{specifications,jdbcType=VARCHAR},
      bill_count = #{billCount,jdbcType=DECIMAL},
      bill_unit = #{billUnit,jdbcType=VARCHAR},
      no_tax_price = #{noTaxPrice,jdbcType=DECIMAL},
      no_tax_amount = #{noTaxAmount,jdbcType=DECIMAL},
      tax_amount = #{taxAmount,jdbcType=DECIMAL},
      tax_rate = #{taxRate,jdbcType=DECIMAL},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=BIGINT},
      create_name = #{createName,jdbcType=VARCHAR},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      operator = #{operator,jdbcType=VARCHAR},
      update_by = #{updateBy,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>