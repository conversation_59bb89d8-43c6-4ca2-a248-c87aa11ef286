<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmRuleOperateLogMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmRuleOperateLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="rule_no" jdbcType="VARCHAR" property="ruleNo" />
    <result column="operate_info" jdbcType="VARCHAR" property="operateInfo" />
    <result column="operate_no" jdbcType="VARCHAR" property="operateNo" />
    <result column="operate_name" jdbcType="VARCHAR" property="operateName" />
    <result column="operate_type" jdbcType="VARCHAR" property="operateType" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, rule_no, operate_info, operate_no, operate_name, operate_type, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmRuleOperateLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_rule_operate_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_rule_operate_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from srm_rule_operate_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmRuleOperateLogExample">
    delete from srm_rule_operate_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmRuleOperateLog">
    insert into srm_rule_operate_log (id, rule_no, operate_info, 
      operate_no, operate_name, operate_type, 
      gmt_create, gmt_update)
    values (#{id,jdbcType=BIGINT}, #{ruleNo,jdbcType=VARCHAR}, #{operateInfo,jdbcType=VARCHAR}, 
      #{operateNo,jdbcType=VARCHAR}, #{operateName,jdbcType=VARCHAR}, #{operateType,jdbcType=VARCHAR}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmRuleOperateLog">
    insert into srm_rule_operate_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="ruleNo != null">
        rule_no,
      </if>
      <if test="operateInfo != null">
        operate_info,
      </if>
      <if test="operateNo != null">
        operate_no,
      </if>
      <if test="operateName != null">
        operate_name,
      </if>
      <if test="operateType != null">
        operate_type,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="ruleNo != null">
        #{ruleNo,jdbcType=VARCHAR},
      </if>
      <if test="operateInfo != null">
        #{operateInfo,jdbcType=VARCHAR},
      </if>
      <if test="operateNo != null">
        #{operateNo,jdbcType=VARCHAR},
      </if>
      <if test="operateName != null">
        #{operateName,jdbcType=VARCHAR},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmRuleOperateLogExample" resultType="java.lang.Long">
    select count(*) from srm_rule_operate_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_rule_operate_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.ruleNo != null">
        rule_no = #{record.ruleNo,jdbcType=VARCHAR},
      </if>
      <if test="record.operateInfo != null">
        operate_info = #{record.operateInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.operateNo != null">
        operate_no = #{record.operateNo,jdbcType=VARCHAR},
      </if>
      <if test="record.operateName != null">
        operate_name = #{record.operateName,jdbcType=VARCHAR},
      </if>
      <if test="record.operateType != null">
        operate_type = #{record.operateType,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_rule_operate_log
    set id = #{record.id,jdbcType=BIGINT},
      rule_no = #{record.ruleNo,jdbcType=VARCHAR},
      operate_info = #{record.operateInfo,jdbcType=VARCHAR},
      operate_no = #{record.operateNo,jdbcType=VARCHAR},
      operate_name = #{record.operateName,jdbcType=VARCHAR},
      operate_type = #{record.operateType,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmRuleOperateLog">
    update srm_rule_operate_log
    <set>
      <if test="ruleNo != null">
        rule_no = #{ruleNo,jdbcType=VARCHAR},
      </if>
      <if test="operateInfo != null">
        operate_info = #{operateInfo,jdbcType=VARCHAR},
      </if>
      <if test="operateNo != null">
        operate_no = #{operateNo,jdbcType=VARCHAR},
      </if>
      <if test="operateName != null">
        operate_name = #{operateName,jdbcType=VARCHAR},
      </if>
      <if test="operateType != null">
        operate_type = #{operateType,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmRuleOperateLog">
    update srm_rule_operate_log
    set rule_no = #{ruleNo,jdbcType=VARCHAR},
      operate_info = #{operateInfo,jdbcType=VARCHAR},
      operate_no = #{operateNo,jdbcType=VARCHAR},
      operate_name = #{operateName,jdbcType=VARCHAR},
      operate_type = #{operateType,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>