<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.PortalShortageRegistrationMapper">
    <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.PortalShortageRegistration">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="business_id" jdbcType="BIGINT" property="businessId" />
        <result column="business_code" jdbcType="VARCHAR" property="businessCode" />
        <result column="business_name" jdbcType="VARCHAR" property="businessName" />
        <result column="store_id" jdbcType="BIGINT" property="storeId" />
        <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
        <result column="store_name" jdbcType="VARCHAR" property="storeName" />
        <result column="emp_code" jdbcType="VARCHAR" property="empCode" />
        <result column="user_id" jdbcType="BIGINT" property="userId" />
        <result column="user_name" jdbcType="VARCHAR" property="userName" />
        <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
        <result column="goods_num" jdbcType="INTEGER" property="goodsNum" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="extend" jdbcType="VARCHAR" property="extend" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="created_by" jdbcType="BIGINT" property="createdBy" />
        <result column="created_name" jdbcType="VARCHAR" property="createdName" />
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
        <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
        <result column="from_source" jdbcType="VARCHAR" property="fromSource" />
        <result column="biz_status" jdbcType="INTEGER" property="bizStatus" />
        <result column="dc_store_id" jdbcType="BIGINT" property="dcStoreId" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id, business_id, business_code, business_name, store_id, store_code, store_name,
    emp_code, user_id, user_name, goods_no, goods_name, goods_num, gmt_create, gmt_update,
    status, extend, version, created_by, created_name, updated_by, updated_name, from_source,dc_store_id,biz_status
    </sql>
    <select id="selectByExample" parameterType="com.cowell.purchase.entity.PortalShortageRegistrationExample" resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        'true' as QUERYID,
        <include refid="Base_Column_List" />
        from portal_shortage_registration
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from portal_shortage_registration
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from portal_shortage_registration
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.PortalShortageRegistrationExample">
        delete from portal_shortage_registration
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
    </delete>
    <insert id="insert" parameterType="com.cowell.purchase.entity.PortalShortageRegistration" useGeneratedKeys="true" keyProperty="id">
        insert into portal_shortage_registration (business_id, business_code, business_name,
        store_id, store_code, store_name,
        emp_code, user_id, user_name,
        goods_no, goods_name, goods_num,
        gmt_create, gmt_update, status,
        extend, version, created_by,
        created_name, updated_by, updated_name,
        from_source,biz_status,dc_store_id)
        values (#{businessId,jdbcType=BIGINT}, #{businessCode,jdbcType=VARCHAR}, #{businessName,jdbcType=VARCHAR},
        #{storeId,jdbcType=BIGINT}, #{storeCode,jdbcType=VARCHAR}, #{storeName,jdbcType=VARCHAR},
        #{empCode,jdbcType=VARCHAR}, #{userId,jdbcType=BIGINT}, #{userName,jdbcType=VARCHAR},
        #{goodsNo,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, #{goodsNum,jdbcType=INTEGER},
        #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT},
        #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT},
        #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR},
        #{fromSource,jdbcType=VARCHAR},#{bizStatus,jdbcType=INTEGER},#{dcStoreId,jdbcType=BIGINT})
    </insert>
    <insert id="insertSelective" parameterType="com.cowell.purchase.entity.PortalShortageRegistration" useGeneratedKeys="true" keyProperty="id">
        insert into portal_shortage_registration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="businessId != null">
                business_id,
            </if>
            <if test="businessCode != null">
                business_code,
            </if>
            <if test="businessName != null">
                business_name,
            </if>
            <if test="storeId != null">
                store_id,
            </if>
            <if test="storeCode != null">
                store_code,
            </if>
            <if test="storeName != null">
                store_name,
            </if>
            <if test="empCode != null">
                emp_code,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="userName != null">
                user_name,
            </if>
            <if test="goodsNo != null">
                goods_no,
            </if>
            <if test="goodsName != null">
                goods_name,
            </if>
            <if test="goodsNum != null">
                goods_num,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtUpdate != null">
                gmt_update,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="extend != null">
                extend,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdName != null">
                created_name,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="updatedName != null">
                updated_name,
            </if>
            <if test="fromSource != null">
                from_source,
            </if>
            <if test="bizStatus != null">
                biz_status,
            </if>
            <if test="dcStoreId != null">
                dc_store_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="businessId != null">
                #{businessId,jdbcType=BIGINT},
            </if>
            <if test="businessCode != null">
                #{businessCode,jdbcType=VARCHAR},
            </if>
            <if test="businessName != null">
                #{businessName,jdbcType=VARCHAR},
            </if>
            <if test="storeId != null">
                #{storeId,jdbcType=BIGINT},
            </if>
            <if test="storeCode != null">
                #{storeCode,jdbcType=VARCHAR},
            </if>
            <if test="storeName != null">
                #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="empCode != null">
                #{empCode,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="goodsNo != null">
                #{goodsNo,jdbcType=VARCHAR},
            </if>
            <if test="goodsName != null">
                #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="goodsNum != null">
                #{goodsNum,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtUpdate != null">
                #{gmtUpdate,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="extend != null">
                #{extend,jdbcType=VARCHAR},
            </if>
            <if test="version != null">
                #{version,jdbcType=INTEGER},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=BIGINT},
            </if>
            <if test="createdName != null">
                #{createdName,jdbcType=VARCHAR},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=BIGINT},
            </if>
            <if test="updatedName != null">
                #{updatedName,jdbcType=VARCHAR},
            </if>
            <if test="fromSource != null">
                #{fromSource,jdbcType=VARCHAR},
            </if>
            <if test="bizStatus != null">
                #{bizStatus,jdbcType=INTEGER},
            </if>
            <if test="dcStoreId != null">
                #{dcStoreId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.cowell.purchase.entity.PortalShortageRegistrationExample" resultType="java.lang.Long">
        select count(*) from portal_shortage_registration
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
    </select>
    <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.PortalShortageRegistration">
        update portal_shortage_registration
        <set>
            <if test="businessId != null">
                business_id = #{businessId,jdbcType=BIGINT},
            </if>
            <if test="businessCode != null">
                business_code = #{businessCode,jdbcType=VARCHAR},
            </if>
            <if test="businessName != null">
                business_name = #{businessName,jdbcType=VARCHAR},
            </if>
            <if test="storeId != null">
                store_id = #{storeId,jdbcType=BIGINT},
            </if>
            <if test="storeCode != null">
                store_code = #{storeCode,jdbcType=VARCHAR},
            </if>
            <if test="storeName != null">
                store_name = #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="empCode != null">
                emp_code = #{empCode,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="userName != null">
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="goodsNo != null">
                goods_no = #{goodsNo,jdbcType=VARCHAR},
            </if>
            <if test="goodsName != null">
                goods_name = #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="goodsNum != null">
                goods_num = #{goodsNum,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtUpdate != null">
                gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="extend != null">
                extend = #{extend,jdbcType=VARCHAR},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=INTEGER},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=BIGINT},
            </if>
            <if test="createdName != null">
                created_name = #{createdName,jdbcType=VARCHAR},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy,jdbcType=BIGINT},
            </if>
            <if test="updatedName != null">
                updated_name = #{updatedName,jdbcType=VARCHAR},
            </if>
            <if test="fromSource != null">
                from_source = #{fromSource,jdbcType=VARCHAR},
            </if>
            <if test="bizStatus != null">
                biz_status = #{bizStatus,jdbcType=INTEGER},
            </if>
            <if test="dcStoreId != null">
                dc_store_id =#{dcStoreId,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.PortalShortageRegistration">
        update portal_shortage_registration
        set business_id = #{businessId,jdbcType=BIGINT},
            business_code = #{businessCode,jdbcType=VARCHAR},
            business_name = #{businessName,jdbcType=VARCHAR},
            store_id = #{storeId,jdbcType=BIGINT},
            store_code = #{storeCode,jdbcType=VARCHAR},
            store_name = #{storeName,jdbcType=VARCHAR},
            emp_code = #{empCode,jdbcType=VARCHAR},
            user_id = #{userId,jdbcType=BIGINT},
            user_name = #{userName,jdbcType=VARCHAR},
            goods_no = #{goodsNo,jdbcType=VARCHAR},
            goods_name = #{goodsName,jdbcType=VARCHAR},
            goods_num = #{goodsNum,jdbcType=INTEGER},
            gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
            status = #{status,jdbcType=TINYINT},
            extend = #{extend,jdbcType=VARCHAR},
            version = #{version,jdbcType=INTEGER},
            created_by = #{createdBy,jdbcType=BIGINT},
            created_name = #{createdName,jdbcType=VARCHAR},
            updated_by = #{updatedBy,jdbcType=BIGINT},
            updated_name = #{updatedName,jdbcType=VARCHAR},
            from_source = #{fromSource,jdbcType=VARCHAR},
            biz_status = #{bizStatus,jdbcType=INTEGER},
            dc_store_id = #{dcStoreId,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>


    <update id="updateNotice" >
        update portal_shortage_registration set biz_status = 1  where id = #{id} and biz_status = 0
    </update>

    <select id="findWaitNoticeByDcStoreIdAndGoodsNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from portal_shortage_registration
        where  biz_status = 0
          and gmt_create >=(NOW() - INTERVAL 7 DAY)
          and dc_store_id = #{dcStoreId}
          and goods_no = #{goodsNo}
    </select>


    <select id="findStatisticsOrderList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from portal_shortage_registration
        WHERE
        gmt_create &gt;= #{startTime}
        AND gmt_create &lt;= #{endTime}
    </select>

</mapper>
