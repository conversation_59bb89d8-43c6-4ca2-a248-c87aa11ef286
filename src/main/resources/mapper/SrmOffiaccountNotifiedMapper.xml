<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmOffiaccountNotifiedMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmOffiaccountNotified">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="data_no" jdbcType="VARCHAR" property="dataNo" />
    <result column="offiaccount_openid" jdbcType="VARCHAR" property="offiaccountOpenid" />
    <result column="account_no" jdbcType="VARCHAR" property="accountNo" />
    <result column="notify_type" jdbcType="TINYINT" property="notifyType" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, data_no, offiaccount_openid, account_no, notify_type, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmOffiaccountNotifiedExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_offiaccount_notified
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from srm_offiaccount_notified
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from srm_offiaccount_notified
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmOffiaccountNotifiedExample">
    delete from srm_offiaccount_notified
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmOffiaccountNotified">
    insert into srm_offiaccount_notified (id, data_no, offiaccount_openid,
      account_no, notify_type, gmt_create,
      gmt_update)
    values (#{id,jdbcType=BIGINT}, #{dataNo,jdbcType=VARCHAR}, #{offiaccountOpenid,jdbcType=VARCHAR},
      #{accountNo,jdbcType=VARCHAR}, #{notifyType,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmOffiaccountNotified">
    insert into srm_offiaccount_notified
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="dataNo != null">
        data_no,
      </if>
      <if test="offiaccountOpenid != null">
        offiaccount_openid,
      </if>
      <if test="accountNo != null">
        account_no,
      </if>
      <if test="notifyType != null">
        notify_type,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="dataNo != null">
        #{dataNo,jdbcType=VARCHAR},
      </if>
      <if test="offiaccountOpenid != null">
        #{offiaccountOpenid,jdbcType=VARCHAR},
      </if>
      <if test="accountNo != null">
        #{accountNo,jdbcType=VARCHAR},
      </if>
      <if test="notifyType != null">
        #{notifyType,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmOffiaccountNotifiedExample" resultType="java.lang.Long">
    select count(*) from srm_offiaccount_notified
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_offiaccount_notified
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.dataNo != null">
        data_no = #{record.dataNo,jdbcType=VARCHAR},
      </if>
      <if test="record.offiaccountOpenid != null">
        offiaccount_openid = #{record.offiaccountOpenid,jdbcType=VARCHAR},
      </if>
      <if test="record.accountNo != null">
        account_no = #{record.accountNo,jdbcType=VARCHAR},
      </if>
      <if test="record.notifyType != null">
        notify_type = #{record.notifyType,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_offiaccount_notified
    set id = #{record.id,jdbcType=BIGINT},
      data_no = #{record.dataNo,jdbcType=VARCHAR},
      offiaccount_openid = #{record.offiaccountOpenid,jdbcType=VARCHAR},
      account_no = #{record.accountNo,jdbcType=VARCHAR},
      notify_type = #{record.notifyType,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmOffiaccountNotified">
    update srm_offiaccount_notified
    <set>
      <if test="dataNo != null">
        data_no = #{dataNo,jdbcType=VARCHAR},
      </if>
      <if test="offiaccountOpenid != null">
        offiaccount_openid = #{offiaccountOpenid,jdbcType=VARCHAR},
      </if>
      <if test="accountNo != null">
        account_no = #{accountNo,jdbcType=VARCHAR},
      </if>
      <if test="notifyType != null">
        notify_type = #{notifyType,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmOffiaccountNotified">
    update srm_offiaccount_notified
    set data_no = #{dataNo,jdbcType=VARCHAR},
      offiaccount_openid = #{offiaccountOpenid,jdbcType=VARCHAR},
      account_no = #{accountNo,jdbcType=VARCHAR},
      notify_type = #{notifyType,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
