<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmDataDirectConfigMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmDataDirectConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="query_type" jdbcType="VARCHAR" property="queryType" />
    <result column="account_code" jdbcType="VARCHAR" property="accountCode" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="account_no" jdbcType="VARCHAR" property="accountNo" />
    <result column="data_type" jdbcType="INTEGER" property="dataType" />
    <result column="data_level" jdbcType="INTEGER" property="dataLevel" />
    <result column="trans_type" jdbcType="INTEGER" property="transType" />
    <result column="trans_url" jdbcType="VARCHAR" property="transUrl" />
    <result column="trans_config" jdbcType="VARCHAR" property="transConfig" />
    <result column="report_config" jdbcType="VARCHAR" property="reportConfig" />
    <result column="data_structure_config" jdbcType="VARCHAR" property="dataStructureConfig" />
    <result column="data_structure_json_mapping" jdbcType="VARCHAR" property="dataStructureJsonMapping" />
    <result column="extend_config" jdbcType="VARCHAR" property="extendConfig" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, app_id, query_type, account_code, account_name, account_no, data_type, data_level, 
    trans_type, trans_url, trans_config, report_config, data_structure_config, data_structure_json_mapping, 
    extend_config, gmt_create, gmt_update, `status`, extend, version, created_by, created_name, 
    updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmDataDirectConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_data_direct_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_data_direct_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from srm_data_direct_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmDataDirectConfigExample">
    delete from srm_data_direct_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmDataDirectConfig">
    insert into srm_data_direct_config (id, app_id, query_type, 
      account_code, account_name, account_no, 
      data_type, data_level, trans_type, 
      trans_url, trans_config, report_config, 
      data_structure_config, data_structure_json_mapping, 
      extend_config, gmt_create, gmt_update, 
      `status`, extend, version, 
      created_by, created_name, updated_by, 
      updated_name)
    values (#{id,jdbcType=BIGINT}, #{appId,jdbcType=VARCHAR}, #{queryType,jdbcType=VARCHAR}, 
      #{accountCode,jdbcType=VARCHAR}, #{accountName,jdbcType=VARCHAR}, #{accountNo,jdbcType=VARCHAR}, 
      #{dataType,jdbcType=INTEGER}, #{dataLevel,jdbcType=INTEGER}, #{transType,jdbcType=INTEGER}, 
      #{transUrl,jdbcType=VARCHAR}, #{transConfig,jdbcType=VARCHAR}, #{reportConfig,jdbcType=VARCHAR}, 
      #{dataStructureConfig,jdbcType=VARCHAR}, #{dataStructureJsonMapping,jdbcType=VARCHAR}, 
      #{extendConfig,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, 
      #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmDataDirectConfig">
    insert into srm_data_direct_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="queryType != null">
        query_type,
      </if>
      <if test="accountCode != null">
        account_code,
      </if>
      <if test="accountName != null">
        account_name,
      </if>
      <if test="accountNo != null">
        account_no,
      </if>
      <if test="dataType != null">
        data_type,
      </if>
      <if test="dataLevel != null">
        data_level,
      </if>
      <if test="transType != null">
        trans_type,
      </if>
      <if test="transUrl != null">
        trans_url,
      </if>
      <if test="transConfig != null">
        trans_config,
      </if>
      <if test="reportConfig != null">
        report_config,
      </if>
      <if test="dataStructureConfig != null">
        data_structure_config,
      </if>
      <if test="dataStructureJsonMapping != null">
        data_structure_json_mapping,
      </if>
      <if test="extendConfig != null">
        extend_config,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="queryType != null">
        #{queryType,jdbcType=VARCHAR},
      </if>
      <if test="accountCode != null">
        #{accountCode,jdbcType=VARCHAR},
      </if>
      <if test="accountName != null">
        #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="accountNo != null">
        #{accountNo,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null">
        #{dataType,jdbcType=INTEGER},
      </if>
      <if test="dataLevel != null">
        #{dataLevel,jdbcType=INTEGER},
      </if>
      <if test="transType != null">
        #{transType,jdbcType=INTEGER},
      </if>
      <if test="transUrl != null">
        #{transUrl,jdbcType=VARCHAR},
      </if>
      <if test="transConfig != null">
        #{transConfig,jdbcType=VARCHAR},
      </if>
      <if test="reportConfig != null">
        #{reportConfig,jdbcType=VARCHAR},
      </if>
      <if test="dataStructureConfig != null">
        #{dataStructureConfig,jdbcType=VARCHAR},
      </if>
      <if test="dataStructureJsonMapping != null">
        #{dataStructureJsonMapping,jdbcType=VARCHAR},
      </if>
      <if test="extendConfig != null">
        #{extendConfig,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmDataDirectConfigExample" resultType="java.lang.Long">
    select count(*) from srm_data_direct_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_data_direct_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.appId != null">
        app_id = #{record.appId,jdbcType=VARCHAR},
      </if>
      <if test="record.queryType != null">
        query_type = #{record.queryType,jdbcType=VARCHAR},
      </if>
      <if test="record.accountCode != null">
        account_code = #{record.accountCode,jdbcType=VARCHAR},
      </if>
      <if test="record.accountName != null">
        account_name = #{record.accountName,jdbcType=VARCHAR},
      </if>
      <if test="record.accountNo != null">
        account_no = #{record.accountNo,jdbcType=VARCHAR},
      </if>
      <if test="record.dataType != null">
        data_type = #{record.dataType,jdbcType=INTEGER},
      </if>
      <if test="record.dataLevel != null">
        data_level = #{record.dataLevel,jdbcType=INTEGER},
      </if>
      <if test="record.transType != null">
        trans_type = #{record.transType,jdbcType=INTEGER},
      </if>
      <if test="record.transUrl != null">
        trans_url = #{record.transUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.transConfig != null">
        trans_config = #{record.transConfig,jdbcType=VARCHAR},
      </if>
      <if test="record.reportConfig != null">
        report_config = #{record.reportConfig,jdbcType=VARCHAR},
      </if>
      <if test="record.dataStructureConfig != null">
        data_structure_config = #{record.dataStructureConfig,jdbcType=VARCHAR},
      </if>
      <if test="record.dataStructureJsonMapping != null">
        data_structure_json_mapping = #{record.dataStructureJsonMapping,jdbcType=VARCHAR},
      </if>
      <if test="record.extendConfig != null">
        extend_config = #{record.extendConfig,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_data_direct_config
    set id = #{record.id,jdbcType=BIGINT},
      app_id = #{record.appId,jdbcType=VARCHAR},
      query_type = #{record.queryType,jdbcType=VARCHAR},
      account_code = #{record.accountCode,jdbcType=VARCHAR},
      account_name = #{record.accountName,jdbcType=VARCHAR},
      account_no = #{record.accountNo,jdbcType=VARCHAR},
      data_type = #{record.dataType,jdbcType=INTEGER},
      data_level = #{record.dataLevel,jdbcType=INTEGER},
      trans_type = #{record.transType,jdbcType=INTEGER},
      trans_url = #{record.transUrl,jdbcType=VARCHAR},
      trans_config = #{record.transConfig,jdbcType=VARCHAR},
      report_config = #{record.reportConfig,jdbcType=VARCHAR},
      data_structure_config = #{record.dataStructureConfig,jdbcType=VARCHAR},
      data_structure_json_mapping = #{record.dataStructureJsonMapping,jdbcType=VARCHAR},
      extend_config = #{record.extendConfig,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      `status` = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmDataDirectConfig">
    update srm_data_direct_config
    <set>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="queryType != null">
        query_type = #{queryType,jdbcType=VARCHAR},
      </if>
      <if test="accountCode != null">
        account_code = #{accountCode,jdbcType=VARCHAR},
      </if>
      <if test="accountName != null">
        account_name = #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="accountNo != null">
        account_no = #{accountNo,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null">
        data_type = #{dataType,jdbcType=INTEGER},
      </if>
      <if test="dataLevel != null">
        data_level = #{dataLevel,jdbcType=INTEGER},
      </if>
      <if test="transType != null">
        trans_type = #{transType,jdbcType=INTEGER},
      </if>
      <if test="transUrl != null">
        trans_url = #{transUrl,jdbcType=VARCHAR},
      </if>
      <if test="transConfig != null">
        trans_config = #{transConfig,jdbcType=VARCHAR},
      </if>
      <if test="reportConfig != null">
        report_config = #{reportConfig,jdbcType=VARCHAR},
      </if>
      <if test="dataStructureConfig != null">
        data_structure_config = #{dataStructureConfig,jdbcType=VARCHAR},
      </if>
      <if test="dataStructureJsonMapping != null">
        data_structure_json_mapping = #{dataStructureJsonMapping,jdbcType=VARCHAR},
      </if>
      <if test="extendConfig != null">
        extend_config = #{extendConfig,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmDataDirectConfig">
    update srm_data_direct_config
    set app_id = #{appId,jdbcType=VARCHAR},
      query_type = #{queryType,jdbcType=VARCHAR},
      account_code = #{accountCode,jdbcType=VARCHAR},
      account_name = #{accountName,jdbcType=VARCHAR},
      account_no = #{accountNo,jdbcType=VARCHAR},
      data_type = #{dataType,jdbcType=INTEGER},
      data_level = #{dataLevel,jdbcType=INTEGER},
      trans_type = #{transType,jdbcType=INTEGER},
      trans_url = #{transUrl,jdbcType=VARCHAR},
      trans_config = #{transConfig,jdbcType=VARCHAR},
      report_config = #{reportConfig,jdbcType=VARCHAR},
      data_structure_config = #{dataStructureConfig,jdbcType=VARCHAR},
      data_structure_json_mapping = #{dataStructureJsonMapping,jdbcType=VARCHAR},
      extend_config = #{extendConfig,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>