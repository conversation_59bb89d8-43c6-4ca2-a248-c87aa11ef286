<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmTicketRebateDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmTicketRebateDetail">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="rebate_no" jdbcType="VARCHAR" property="rebateNo" />
    <result column="rebate_id" jdbcType="VARCHAR" property="rebateId" />
    <result column="rebate_key" jdbcType="VARCHAR" property="rebateKey" />
    <result column="tax_code" jdbcType="VARCHAR" property="taxCode" />
    <result column="tax_rate" jdbcType="VARCHAR" property="taxRate" />
    <result column="have_amount" jdbcType="DECIMAL" property="haveAmount" />
    <result column="unpaid_have_amount" jdbcType="DECIMAL" property="unpaidHaveAmount" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, rebate_no, rebate_id, rebate_key, tax_code, tax_rate, have_amount, unpaid_have_amount, 
    gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmTicketRebateDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_ticket_rebate_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_ticket_rebate_detail
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from srm_ticket_rebate_detail
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmTicketRebateDetailExample">
    delete from srm_ticket_rebate_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmTicketRebateDetail">
    insert into srm_ticket_rebate_detail (id, rebate_no, rebate_id, 
      rebate_key, tax_code, tax_rate, 
      have_amount, unpaid_have_amount, gmt_create, 
      gmt_update)
    values (#{id,jdbcType=INTEGER}, #{rebateNo,jdbcType=VARCHAR}, #{rebateId,jdbcType=VARCHAR}, 
      #{rebateKey,jdbcType=VARCHAR}, #{taxCode,jdbcType=VARCHAR}, #{taxRate,jdbcType=VARCHAR}, 
      #{haveAmount,jdbcType=DECIMAL}, #{unpaidHaveAmount,jdbcType=DECIMAL}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmTicketRebateDetail">
    insert into srm_ticket_rebate_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="rebateNo != null">
        rebate_no,
      </if>
      <if test="rebateId != null">
        rebate_id,
      </if>
      <if test="rebateKey != null">
        rebate_key,
      </if>
      <if test="taxCode != null">
        tax_code,
      </if>
      <if test="taxRate != null">
        tax_rate,
      </if>
      <if test="haveAmount != null">
        have_amount,
      </if>
      <if test="unpaidHaveAmount != null">
        unpaid_have_amount,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="rebateNo != null">
        #{rebateNo,jdbcType=VARCHAR},
      </if>
      <if test="rebateId != null">
        #{rebateId,jdbcType=VARCHAR},
      </if>
      <if test="rebateKey != null">
        #{rebateKey,jdbcType=VARCHAR},
      </if>
      <if test="taxCode != null">
        #{taxCode,jdbcType=VARCHAR},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=VARCHAR},
      </if>
      <if test="haveAmount != null">
        #{haveAmount,jdbcType=DECIMAL},
      </if>
      <if test="unpaidHaveAmount != null">
        #{unpaidHaveAmount,jdbcType=DECIMAL},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmTicketRebateDetailExample" resultType="java.lang.Long">
    select count(*) from srm_ticket_rebate_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_ticket_rebate_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.rebateNo != null">
        rebate_no = #{record.rebateNo,jdbcType=VARCHAR},
      </if>
      <if test="record.rebateId != null">
        rebate_id = #{record.rebateId,jdbcType=VARCHAR},
      </if>
      <if test="record.rebateKey != null">
        rebate_key = #{record.rebateKey,jdbcType=VARCHAR},
      </if>
      <if test="record.taxCode != null">
        tax_code = #{record.taxCode,jdbcType=VARCHAR},
      </if>
      <if test="record.taxRate != null">
        tax_rate = #{record.taxRate,jdbcType=VARCHAR},
      </if>
      <if test="record.haveAmount != null">
        have_amount = #{record.haveAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.unpaidHaveAmount != null">
        unpaid_have_amount = #{record.unpaidHaveAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_ticket_rebate_detail
    set id = #{record.id,jdbcType=INTEGER},
      rebate_no = #{record.rebateNo,jdbcType=VARCHAR},
      rebate_id = #{record.rebateId,jdbcType=VARCHAR},
      rebate_key = #{record.rebateKey,jdbcType=VARCHAR},
      tax_code = #{record.taxCode,jdbcType=VARCHAR},
      tax_rate = #{record.taxRate,jdbcType=VARCHAR},
      have_amount = #{record.haveAmount,jdbcType=DECIMAL},
      unpaid_have_amount = #{record.unpaidHaveAmount,jdbcType=DECIMAL},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmTicketRebateDetail">
    update srm_ticket_rebate_detail
    <set>
      <if test="rebateNo != null">
        rebate_no = #{rebateNo,jdbcType=VARCHAR},
      </if>
      <if test="rebateId != null">
        rebate_id = #{rebateId,jdbcType=VARCHAR},
      </if>
      <if test="rebateKey != null">
        rebate_key = #{rebateKey,jdbcType=VARCHAR},
      </if>
      <if test="taxCode != null">
        tax_code = #{taxCode,jdbcType=VARCHAR},
      </if>
      <if test="taxRate != null">
        tax_rate = #{taxRate,jdbcType=VARCHAR},
      </if>
      <if test="haveAmount != null">
        have_amount = #{haveAmount,jdbcType=DECIMAL},
      </if>
      <if test="unpaidHaveAmount != null">
        unpaid_have_amount = #{unpaidHaveAmount,jdbcType=DECIMAL},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmTicketRebateDetail">
    update srm_ticket_rebate_detail
    set rebate_no = #{rebateNo,jdbcType=VARCHAR},
      rebate_id = #{rebateId,jdbcType=VARCHAR},
      rebate_key = #{rebateKey,jdbcType=VARCHAR},
      tax_code = #{taxCode,jdbcType=VARCHAR},
      tax_rate = #{taxRate,jdbcType=VARCHAR},
      have_amount = #{haveAmount,jdbcType=DECIMAL},
      unpaid_have_amount = #{unpaidHaveAmount,jdbcType=DECIMAL},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>