<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SettleInvoiceTaskDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SettleInvoiceTaskDetail">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="task_no" jdbcType="VARCHAR" property="taskNo" />
    <result column="invoice_code" jdbcType="VARCHAR" property="invoiceCode" />
    <result column="invoice_no" jdbcType="VARCHAR" property="invoiceNo" />
    <result column="invoice_date" jdbcType="DATE" property="invoiceDate" />
    <result column="no_tax_amount" jdbcType="DECIMAL" property="noTaxAmount" />
    <result column="total_amount_tax" jdbcType="DECIMAL" property="totalAmountTax" />
    <result column="check_code" jdbcType="VARCHAR" property="checkCode" />
    <result column="task_status" jdbcType="TINYINT" property="taskStatus" />
    <result column="sale_list_able" jdbcType="TINYINT" property="saleListAble" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_no, invoice_code, invoice_no, invoice_date, no_tax_amount, total_amount_tax, 
    check_code, task_status, sale_list_able, `status`, gmt_create, gmt_update, extend, 
    version, created_by, created_name, updated_by, updated_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SettleInvoiceTaskDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from settle_invoice_task_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from settle_invoice_task_detail
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from settle_invoice_task_detail
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SettleInvoiceTaskDetailExample">
    delete from settle_invoice_task_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SettleInvoiceTaskDetail">
    insert into settle_invoice_task_detail (id, task_no, invoice_code, 
      invoice_no, invoice_date, no_tax_amount, 
      total_amount_tax, check_code, task_status, 
      sale_list_able, `status`, gmt_create, 
      gmt_update, extend, version, 
      created_by, created_name, updated_by, 
      updated_name)
    values (#{id,jdbcType=INTEGER}, #{taskNo,jdbcType=VARCHAR}, #{invoiceCode,jdbcType=VARCHAR}, 
      #{invoiceNo,jdbcType=VARCHAR}, #{invoiceDate,jdbcType=DATE}, #{noTaxAmount,jdbcType=DECIMAL}, 
      #{totalAmountTax,jdbcType=DECIMAL}, #{checkCode,jdbcType=VARCHAR}, #{taskStatus,jdbcType=TINYINT}, 
      #{saleListAble,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, 
      #{updatedName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SettleInvoiceTaskDetail">
    insert into settle_invoice_task_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="taskNo != null">
        task_no,
      </if>
      <if test="invoiceCode != null">
        invoice_code,
      </if>
      <if test="invoiceNo != null">
        invoice_no,
      </if>
      <if test="invoiceDate != null">
        invoice_date,
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount,
      </if>
      <if test="totalAmountTax != null">
        total_amount_tax,
      </if>
      <if test="checkCode != null">
        check_code,
      </if>
      <if test="taskStatus != null">
        task_status,
      </if>
      <if test="saleListAble != null">
        sale_list_able,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="taskNo != null">
        #{taskNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceCode != null">
        #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNo != null">
        #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceDate != null">
        #{invoiceDate,jdbcType=DATE},
      </if>
      <if test="noTaxAmount != null">
        #{noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalAmountTax != null">
        #{totalAmountTax,jdbcType=DECIMAL},
      </if>
      <if test="checkCode != null">
        #{checkCode,jdbcType=VARCHAR},
      </if>
      <if test="taskStatus != null">
        #{taskStatus,jdbcType=TINYINT},
      </if>
      <if test="saleListAble != null">
        #{saleListAble,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SettleInvoiceTaskDetailExample" resultType="java.lang.Long">
    select count(*) from settle_invoice_task_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update settle_invoice_task_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.taskNo != null">
        task_no = #{record.taskNo,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceCode != null">
        invoice_code = #{record.invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceNo != null">
        invoice_no = #{record.invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="record.invoiceDate != null">
        invoice_date = #{record.invoiceDate,jdbcType=DATE},
      </if>
      <if test="record.noTaxAmount != null">
        no_tax_amount = #{record.noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.totalAmountTax != null">
        total_amount_tax = #{record.totalAmountTax,jdbcType=DECIMAL},
      </if>
      <if test="record.checkCode != null">
        check_code = #{record.checkCode,jdbcType=VARCHAR},
      </if>
      <if test="record.taskStatus != null">
        task_status = #{record.taskStatus,jdbcType=TINYINT},
      </if>
      <if test="record.saleListAble != null">
        sale_list_able = #{record.saleListAble,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update settle_invoice_task_detail
    set id = #{record.id,jdbcType=INTEGER},
      task_no = #{record.taskNo,jdbcType=VARCHAR},
      invoice_code = #{record.invoiceCode,jdbcType=VARCHAR},
      invoice_no = #{record.invoiceNo,jdbcType=VARCHAR},
      invoice_date = #{record.invoiceDate,jdbcType=DATE},
      no_tax_amount = #{record.noTaxAmount,jdbcType=DECIMAL},
      total_amount_tax = #{record.totalAmountTax,jdbcType=DECIMAL},
      check_code = #{record.checkCode,jdbcType=VARCHAR},
      task_status = #{record.taskStatus,jdbcType=TINYINT},
      sale_list_able = #{record.saleListAble,jdbcType=TINYINT},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SettleInvoiceTaskDetail">
    update settle_invoice_task_detail
    <set>
      <if test="taskNo != null">
        task_no = #{taskNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceCode != null">
        invoice_code = #{invoiceCode,jdbcType=VARCHAR},
      </if>
      <if test="invoiceNo != null">
        invoice_no = #{invoiceNo,jdbcType=VARCHAR},
      </if>
      <if test="invoiceDate != null">
        invoice_date = #{invoiceDate,jdbcType=DATE},
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount = #{noTaxAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalAmountTax != null">
        total_amount_tax = #{totalAmountTax,jdbcType=DECIMAL},
      </if>
      <if test="checkCode != null">
        check_code = #{checkCode,jdbcType=VARCHAR},
      </if>
      <if test="taskStatus != null">
        task_status = #{taskStatus,jdbcType=TINYINT},
      </if>
      <if test="saleListAble != null">
        sale_list_able = #{saleListAble,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SettleInvoiceTaskDetail">
    update settle_invoice_task_detail
    set task_no = #{taskNo,jdbcType=VARCHAR},
      invoice_code = #{invoiceCode,jdbcType=VARCHAR},
      invoice_no = #{invoiceNo,jdbcType=VARCHAR},
      invoice_date = #{invoiceDate,jdbcType=DATE},
      no_tax_amount = #{noTaxAmount,jdbcType=DECIMAL},
      total_amount_tax = #{totalAmountTax,jdbcType=DECIMAL},
      check_code = #{checkCode,jdbcType=VARCHAR},
      task_status = #{taskStatus,jdbcType=TINYINT},
      sale_list_able = #{saleListAble,jdbcType=TINYINT},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>