<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapper.SrmInnerPurchaseOrderSendDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entity.SrmInnerPurchaseOrderSendDetail">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="send_id" jdbcType="INTEGER" property="sendId" />
    <result column="send_no" jdbcType="VARCHAR" property="sendNo" />
    <result column="send_status" jdbcType="INTEGER" property="sendStatus" />
    <result column="purchase_order_no" jdbcType="VARCHAR" property="purchaseOrderNo" />
    <result column="purchase_order_id" jdbcType="VARCHAR" property="purchaseOrderId" />
    <result column="order_details_key" jdbcType="VARCHAR" property="orderDetailsKey" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="validity_date" jdbcType="TIMESTAMP" property="validityDate" />
    <result column="produce_date" jdbcType="TIMESTAMP" property="produceDate" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="no_tax_price" jdbcType="DECIMAL" property="noTaxPrice" />
    <result column="have_tax_price" jdbcType="DECIMAL" property="haveTaxPrice" />
    <result column="order_quantity" jdbcType="DECIMAL" property="orderQuantity" />
    <result column="can_send_quantity" jdbcType="DECIMAL" property="canSendQuantity" />
    <result column="this_send_quantity" jdbcType="DECIMAL" property="thisSendQuantity" />
    <result column="delivery_count" jdbcType="DECIMAL" property="deliveryCount" />
    <result column="refuse_quantity" jdbcType="DECIMAL" property="refuseQuantity" />
    <result column="refuse_reason" jdbcType="VARCHAR" property="refuseReason" />
    <result column="price_unit" jdbcType="VARCHAR" property="priceUnit" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_name" jdbcType="VARCHAR" property="createdName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_name" jdbcType="VARCHAR" property="updatedName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, send_id, send_no, send_status, purchase_order_no, purchase_order_id, order_details_key, 
    goods_code, batch_no, validity_date, produce_date, warehouse_code, warehouse_name, 
    no_tax_price, have_tax_price, order_quantity, can_send_quantity, this_send_quantity, 
    delivery_count, refuse_quantity, refuse_reason, price_unit, `status`, extend, version, 
    created_by, created_name, updated_by, updated_name, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrderSendDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_inner_purchase_order_send_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_inner_purchase_order_send_detail
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from srm_inner_purchase_order_send_detail
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrderSendDetailExample">
    delete from srm_inner_purchase_order_send_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrderSendDetail">
    insert into srm_inner_purchase_order_send_detail (id, send_id, send_no, 
      send_status, purchase_order_no, purchase_order_id, 
      order_details_key, goods_code, batch_no, 
      validity_date, produce_date, warehouse_code, 
      warehouse_name, no_tax_price, have_tax_price, 
      order_quantity, can_send_quantity, this_send_quantity, 
      delivery_count, refuse_quantity, refuse_reason, 
      price_unit, `status`, extend, 
      version, created_by, created_name, 
      updated_by, updated_name, gmt_create, 
      gmt_update)
    values (#{id,jdbcType=INTEGER}, #{sendId,jdbcType=INTEGER}, #{sendNo,jdbcType=VARCHAR}, 
      #{sendStatus,jdbcType=INTEGER}, #{purchaseOrderNo,jdbcType=VARCHAR}, #{purchaseOrderId,jdbcType=VARCHAR}, 
      #{orderDetailsKey,jdbcType=VARCHAR}, #{goodsCode,jdbcType=VARCHAR}, #{batchNo,jdbcType=VARCHAR}, 
      #{validityDate,jdbcType=TIMESTAMP}, #{produceDate,jdbcType=TIMESTAMP}, #{warehouseCode,jdbcType=VARCHAR}, 
      #{warehouseName,jdbcType=VARCHAR}, #{noTaxPrice,jdbcType=DECIMAL}, #{haveTaxPrice,jdbcType=DECIMAL}, 
      #{orderQuantity,jdbcType=DECIMAL}, #{canSendQuantity,jdbcType=DECIMAL}, #{thisSendQuantity,jdbcType=DECIMAL}, 
      #{deliveryCount,jdbcType=DECIMAL}, #{refuseQuantity,jdbcType=DECIMAL}, #{refuseReason,jdbcType=VARCHAR}, 
      #{priceUnit,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, 
      #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, #{createdName,jdbcType=VARCHAR}, 
      #{updatedBy,jdbcType=BIGINT}, #{updatedName,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrderSendDetail">
    insert into srm_inner_purchase_order_send_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sendId != null">
        send_id,
      </if>
      <if test="sendNo != null">
        send_no,
      </if>
      <if test="sendStatus != null">
        send_status,
      </if>
      <if test="purchaseOrderNo != null">
        purchase_order_no,
      </if>
      <if test="purchaseOrderId != null">
        purchase_order_id,
      </if>
      <if test="orderDetailsKey != null">
        order_details_key,
      </if>
      <if test="goodsCode != null">
        goods_code,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="validityDate != null">
        validity_date,
      </if>
      <if test="produceDate != null">
        produce_date,
      </if>
      <if test="warehouseCode != null">
        warehouse_code,
      </if>
      <if test="warehouseName != null">
        warehouse_name,
      </if>
      <if test="noTaxPrice != null">
        no_tax_price,
      </if>
      <if test="haveTaxPrice != null">
        have_tax_price,
      </if>
      <if test="orderQuantity != null">
        order_quantity,
      </if>
      <if test="canSendQuantity != null">
        can_send_quantity,
      </if>
      <if test="thisSendQuantity != null">
        this_send_quantity,
      </if>
      <if test="deliveryCount != null">
        delivery_count,
      </if>
      <if test="refuseQuantity != null">
        refuse_quantity,
      </if>
      <if test="refuseReason != null">
        refuse_reason,
      </if>
      <if test="priceUnit != null">
        price_unit,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdName != null">
        created_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedName != null">
        updated_name,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="sendId != null">
        #{sendId,jdbcType=INTEGER},
      </if>
      <if test="sendNo != null">
        #{sendNo,jdbcType=VARCHAR},
      </if>
      <if test="sendStatus != null">
        #{sendStatus,jdbcType=INTEGER},
      </if>
      <if test="purchaseOrderNo != null">
        #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderId != null">
        #{purchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderDetailsKey != null">
        #{orderDetailsKey,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        #{validityDate,jdbcType=TIMESTAMP},
      </if>
      <if test="produceDate != null">
        #{produceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="warehouseCode != null">
        #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="noTaxPrice != null">
        #{noTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="haveTaxPrice != null">
        #{haveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="orderQuantity != null">
        #{orderQuantity,jdbcType=DECIMAL},
      </if>
      <if test="canSendQuantity != null">
        #{canSendQuantity,jdbcType=DECIMAL},
      </if>
      <if test="thisSendQuantity != null">
        #{thisSendQuantity,jdbcType=DECIMAL},
      </if>
      <if test="deliveryCount != null">
        #{deliveryCount,jdbcType=DECIMAL},
      </if>
      <if test="refuseQuantity != null">
        #{refuseQuantity,jdbcType=DECIMAL},
      </if>
      <if test="refuseReason != null">
        #{refuseReason,jdbcType=VARCHAR},
      </if>
      <if test="priceUnit != null">
        #{priceUnit,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrderSendDetailExample" resultType="java.lang.Long">
    select count(*) from srm_inner_purchase_order_send_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_inner_purchase_order_send_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.sendId != null">
        send_id = #{record.sendId,jdbcType=INTEGER},
      </if>
      <if test="record.sendNo != null">
        send_no = #{record.sendNo,jdbcType=VARCHAR},
      </if>
      <if test="record.sendStatus != null">
        send_status = #{record.sendStatus,jdbcType=INTEGER},
      </if>
      <if test="record.purchaseOrderNo != null">
        purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.purchaseOrderId != null">
        purchase_order_id = #{record.purchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orderDetailsKey != null">
        order_details_key = #{record.orderDetailsKey,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCode != null">
        goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.validityDate != null">
        validity_date = #{record.validityDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.produceDate != null">
        produce_date = #{record.produceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.warehouseCode != null">
        warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseName != null">
        warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="record.noTaxPrice != null">
        no_tax_price = #{record.noTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.haveTaxPrice != null">
        have_tax_price = #{record.haveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.orderQuantity != null">
        order_quantity = #{record.orderQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.canSendQuantity != null">
        can_send_quantity = #{record.canSendQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.thisSendQuantity != null">
        this_send_quantity = #{record.thisSendQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.deliveryCount != null">
        delivery_count = #{record.deliveryCount,jdbcType=DECIMAL},
      </if>
      <if test="record.refuseQuantity != null">
        refuse_quantity = #{record.refuseQuantity,jdbcType=DECIMAL},
      </if>
      <if test="record.refuseReason != null">
        refuse_reason = #{record.refuseReason,jdbcType=VARCHAR},
      </if>
      <if test="record.priceUnit != null">
        price_unit = #{record.priceUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdName != null">
        created_name = #{record.createdName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedName != null">
        updated_name = #{record.updatedName,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_inner_purchase_order_send_detail
    set id = #{record.id,jdbcType=INTEGER},
      send_id = #{record.sendId,jdbcType=INTEGER},
      send_no = #{record.sendNo,jdbcType=VARCHAR},
      send_status = #{record.sendStatus,jdbcType=INTEGER},
      purchase_order_no = #{record.purchaseOrderNo,jdbcType=VARCHAR},
      purchase_order_id = #{record.purchaseOrderId,jdbcType=VARCHAR},
      order_details_key = #{record.orderDetailsKey,jdbcType=VARCHAR},
      goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      validity_date = #{record.validityDate,jdbcType=TIMESTAMP},
      produce_date = #{record.produceDate,jdbcType=TIMESTAMP},
      warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      no_tax_price = #{record.noTaxPrice,jdbcType=DECIMAL},
      have_tax_price = #{record.haveTaxPrice,jdbcType=DECIMAL},
      order_quantity = #{record.orderQuantity,jdbcType=DECIMAL},
      can_send_quantity = #{record.canSendQuantity,jdbcType=DECIMAL},
      this_send_quantity = #{record.thisSendQuantity,jdbcType=DECIMAL},
      delivery_count = #{record.deliveryCount,jdbcType=DECIMAL},
      refuse_quantity = #{record.refuseQuantity,jdbcType=DECIMAL},
      refuse_reason = #{record.refuseReason,jdbcType=VARCHAR},
      price_unit = #{record.priceUnit,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_name = #{record.createdName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_name = #{record.updatedName,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrderSendDetail">
    update srm_inner_purchase_order_send_detail
    <set>
      <if test="sendId != null">
        send_id = #{sendId,jdbcType=INTEGER},
      </if>
      <if test="sendNo != null">
        send_no = #{sendNo,jdbcType=VARCHAR},
      </if>
      <if test="sendStatus != null">
        send_status = #{sendStatus,jdbcType=INTEGER},
      </if>
      <if test="purchaseOrderNo != null">
        purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="purchaseOrderId != null">
        purchase_order_id = #{purchaseOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orderDetailsKey != null">
        order_details_key = #{orderDetailsKey,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        goods_code = #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        validity_date = #{validityDate,jdbcType=TIMESTAMP},
      </if>
      <if test="produceDate != null">
        produce_date = #{produceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="warehouseCode != null">
        warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="noTaxPrice != null">
        no_tax_price = #{noTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="haveTaxPrice != null">
        have_tax_price = #{haveTaxPrice,jdbcType=DECIMAL},
      </if>
      <if test="orderQuantity != null">
        order_quantity = #{orderQuantity,jdbcType=DECIMAL},
      </if>
      <if test="canSendQuantity != null">
        can_send_quantity = #{canSendQuantity,jdbcType=DECIMAL},
      </if>
      <if test="thisSendQuantity != null">
        this_send_quantity = #{thisSendQuantity,jdbcType=DECIMAL},
      </if>
      <if test="deliveryCount != null">
        delivery_count = #{deliveryCount,jdbcType=DECIMAL},
      </if>
      <if test="refuseQuantity != null">
        refuse_quantity = #{refuseQuantity,jdbcType=DECIMAL},
      </if>
      <if test="refuseReason != null">
        refuse_reason = #{refuseReason,jdbcType=VARCHAR},
      </if>
      <if test="priceUnit != null">
        price_unit = #{priceUnit,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdName != null">
        created_name = #{createdName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedName != null">
        updated_name = #{updatedName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entity.SrmInnerPurchaseOrderSendDetail">
    update srm_inner_purchase_order_send_detail
    set send_id = #{sendId,jdbcType=INTEGER},
      send_no = #{sendNo,jdbcType=VARCHAR},
      send_status = #{sendStatus,jdbcType=INTEGER},
      purchase_order_no = #{purchaseOrderNo,jdbcType=VARCHAR},
      purchase_order_id = #{purchaseOrderId,jdbcType=VARCHAR},
      order_details_key = #{orderDetailsKey,jdbcType=VARCHAR},
      goods_code = #{goodsCode,jdbcType=VARCHAR},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      validity_date = #{validityDate,jdbcType=TIMESTAMP},
      produce_date = #{produceDate,jdbcType=TIMESTAMP},
      warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      no_tax_price = #{noTaxPrice,jdbcType=DECIMAL},
      have_tax_price = #{haveTaxPrice,jdbcType=DECIMAL},
      order_quantity = #{orderQuantity,jdbcType=DECIMAL},
      can_send_quantity = #{canSendQuantity,jdbcType=DECIMAL},
      this_send_quantity = #{thisSendQuantity,jdbcType=DECIMAL},
      delivery_count = #{deliveryCount,jdbcType=DECIMAL},
      refuse_quantity = #{refuseQuantity,jdbcType=DECIMAL},
      refuse_reason = #{refuseReason,jdbcType=VARCHAR},
      price_unit = #{priceUnit,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_name = #{createdName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_name = #{updatedName,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>