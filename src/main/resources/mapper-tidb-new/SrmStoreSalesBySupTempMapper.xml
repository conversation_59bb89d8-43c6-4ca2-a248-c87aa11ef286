<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapperTidbNew.SrmStoreSalesBySupTempMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entityTidb.SrmStoreSalesBySupTemp">
    <id column="id" jdbcType="OTHER" property="id" />
    <result column="manufacturer_code" jdbcType="OTHER" property="manufacturerCode" />
    <result column="manufacturer" jdbcType="OTHER" property="manufacturer" />
    <result column="supplier_code" jdbcType="OTHER" property="supplierCode" />
    <result column="supplier_name" jdbcType="OTHER" property="supplierName" />
    <result column="store_code" jdbcType="OTHER" property="storeCode" />
    <result column="store_name" jdbcType="OTHER" property="storeName" />
    <result column="data_granularity" jdbcType="OTHER" property="dataGranularity" />
    <result column="company_code" jdbcType="OTHER" property="companyCode" />
    <result column="company_name" jdbcType="OTHER" property="companyName" />
    <result column="goods_code" jdbcType="OTHER" property="goodsCode" />
    <result column="goods_name" jdbcType="OTHER" property="goodsName" />
    <result column="specifications" jdbcType="OTHER" property="specifications" />
    <result column="bar_code" jdbcType="OTHER" property="barCode" />
    <result column="unit" jdbcType="OTHER" property="unit" />
    <result column="batch_number" jdbcType="OTHER" property="batchNumber" />
    <result column="validity_date" jdbcType="OTHER" property="validityDate" />
    <result column="produce_date" jdbcType="OTHER" property="produceDate" />
    <result column="warehouse_code" jdbcType="OTHER" property="warehouseCode" />
    <result column="warehouse_name" jdbcType="OTHER" property="warehouseName" />
    <result column="quatity" jdbcType="OTHER" property="quatity" />
    <result column="business_date" jdbcType="OTHER" property="businessDate" />
    <result column="gmt_create" jdbcType="OTHER" property="gmtCreate" />
    <result column="material_channel" jdbcType="OTHER" property="materialChannel" />
    <result column="retail_amount" jdbcType="OTHER" property="retailAmount" />
    <result column="cost_tax" jdbcType="OTHER" property="costTax" />
    <result column="cost_no_tax" jdbcType="OTHER" property="costNoTax" />
    <result column="apprdocno" jdbcType="OTHER" property="apprdocno" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, manufacturer_code, manufacturer, supplier_code, supplier_name, store_code, store_name, 
    data_granularity, company_code, company_name, goods_code, goods_name, specifications, 
    bar_code, unit, batch_number, validity_date, produce_date, warehouse_code, warehouse_name, 
    quatity, business_date, gmt_create, material_channel, retail_amount, cost_tax, cost_no_tax, 
    apprdocno
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entityTidb.SrmStoreSalesBySupTempExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_store_sales_by_sup_temp
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Object" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from srm_store_sales_by_sup_temp
    where id = #{id,jdbcType=OTHER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Object">
    delete from srm_store_sales_by_sup_temp
    where id = #{id,jdbcType=OTHER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entityTidb.SrmStoreSalesBySupTempExample">
    delete from srm_store_sales_by_sup_temp
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entityTidb.SrmStoreSalesBySupTemp">
    insert into srm_store_sales_by_sup_temp (id, manufacturer_code, manufacturer, 
      supplier_code, supplier_name, store_code, 
      store_name, data_granularity, company_code, 
      company_name, goods_code, goods_name, 
      specifications, bar_code, unit, 
      batch_number, validity_date, produce_date, 
      warehouse_code, warehouse_name, quatity, 
      business_date, gmt_create, material_channel, 
      retail_amount, cost_tax, cost_no_tax, 
      apprdocno)
    values (#{id,jdbcType=OTHER}, #{manufacturerCode,jdbcType=OTHER}, #{manufacturer,jdbcType=OTHER}, 
      #{supplierCode,jdbcType=OTHER}, #{supplierName,jdbcType=OTHER}, #{storeCode,jdbcType=OTHER}, 
      #{storeName,jdbcType=OTHER}, #{dataGranularity,jdbcType=OTHER}, #{companyCode,jdbcType=OTHER}, 
      #{companyName,jdbcType=OTHER}, #{goodsCode,jdbcType=OTHER}, #{goodsName,jdbcType=OTHER}, 
      #{specifications,jdbcType=OTHER}, #{barCode,jdbcType=OTHER}, #{unit,jdbcType=OTHER}, 
      #{batchNumber,jdbcType=OTHER}, #{validityDate,jdbcType=OTHER}, #{produceDate,jdbcType=OTHER}, 
      #{warehouseCode,jdbcType=OTHER}, #{warehouseName,jdbcType=OTHER}, #{quatity,jdbcType=OTHER}, 
      #{businessDate,jdbcType=OTHER}, #{gmtCreate,jdbcType=OTHER}, #{materialChannel,jdbcType=OTHER}, 
      #{retailAmount,jdbcType=OTHER}, #{costTax,jdbcType=OTHER}, #{costNoTax,jdbcType=OTHER}, 
      #{apprdocno,jdbcType=OTHER})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entityTidb.SrmStoreSalesBySupTemp">
    insert into srm_store_sales_by_sup_temp
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="manufacturerCode != null">
        manufacturer_code,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="supplierCode != null">
        supplier_code,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="dataGranularity != null">
        data_granularity,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="goodsCode != null">
        goods_code,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="batchNumber != null">
        batch_number,
      </if>
      <if test="validityDate != null">
        validity_date,
      </if>
      <if test="produceDate != null">
        produce_date,
      </if>
      <if test="warehouseCode != null">
        warehouse_code,
      </if>
      <if test="warehouseName != null">
        warehouse_name,
      </if>
      <if test="quatity != null">
        quatity,
      </if>
      <if test="businessDate != null">
        business_date,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="materialChannel != null">
        material_channel,
      </if>
      <if test="retailAmount != null">
        retail_amount,
      </if>
      <if test="costTax != null">
        cost_tax,
      </if>
      <if test="costNoTax != null">
        cost_no_tax,
      </if>
      <if test="apprdocno != null">
        apprdocno,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=OTHER},
      </if>
      <if test="manufacturerCode != null">
        #{manufacturerCode,jdbcType=OTHER},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=OTHER},
      </if>
      <if test="supplierCode != null">
        #{supplierCode,jdbcType=OTHER},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=OTHER},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=OTHER},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=OTHER},
      </if>
      <if test="dataGranularity != null">
        #{dataGranularity,jdbcType=OTHER},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=OTHER},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=OTHER},
      </if>
      <if test="goodsCode != null">
        #{goodsCode,jdbcType=OTHER},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=OTHER},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=OTHER},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=OTHER},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=OTHER},
      </if>
      <if test="batchNumber != null">
        #{batchNumber,jdbcType=OTHER},
      </if>
      <if test="validityDate != null">
        #{validityDate,jdbcType=OTHER},
      </if>
      <if test="produceDate != null">
        #{produceDate,jdbcType=OTHER},
      </if>
      <if test="warehouseCode != null">
        #{warehouseCode,jdbcType=OTHER},
      </if>
      <if test="warehouseName != null">
        #{warehouseName,jdbcType=OTHER},
      </if>
      <if test="quatity != null">
        #{quatity,jdbcType=OTHER},
      </if>
      <if test="businessDate != null">
        #{businessDate,jdbcType=OTHER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=OTHER},
      </if>
      <if test="materialChannel != null">
        #{materialChannel,jdbcType=OTHER},
      </if>
      <if test="retailAmount != null">
        #{retailAmount,jdbcType=OTHER},
      </if>
      <if test="costTax != null">
        #{costTax,jdbcType=OTHER},
      </if>
      <if test="costNoTax != null">
        #{costNoTax,jdbcType=OTHER},
      </if>
      <if test="apprdocno != null">
        #{apprdocno,jdbcType=OTHER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entityTidb.SrmStoreSalesBySupTempExample" resultType="java.lang.Long">
    select count(*) from srm_store_sales_by_sup_temp
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_store_sales_by_sup_temp
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=OTHER},
      </if>
      <if test="record.manufacturerCode != null">
        manufacturer_code = #{record.manufacturerCode,jdbcType=OTHER},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=OTHER},
      </if>
      <if test="record.supplierCode != null">
        supplier_code = #{record.supplierCode,jdbcType=OTHER},
      </if>
      <if test="record.supplierName != null">
        supplier_name = #{record.supplierName,jdbcType=OTHER},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=OTHER},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=OTHER},
      </if>
      <if test="record.dataGranularity != null">
        data_granularity = #{record.dataGranularity,jdbcType=OTHER},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=OTHER},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=OTHER},
      </if>
      <if test="record.goodsCode != null">
        goods_code = #{record.goodsCode,jdbcType=OTHER},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=OTHER},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=OTHER},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=OTHER},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=OTHER},
      </if>
      <if test="record.batchNumber != null">
        batch_number = #{record.batchNumber,jdbcType=OTHER},
      </if>
      <if test="record.validityDate != null">
        validity_date = #{record.validityDate,jdbcType=OTHER},
      </if>
      <if test="record.produceDate != null">
        produce_date = #{record.produceDate,jdbcType=OTHER},
      </if>
      <if test="record.warehouseCode != null">
        warehouse_code = #{record.warehouseCode,jdbcType=OTHER},
      </if>
      <if test="record.warehouseName != null">
        warehouse_name = #{record.warehouseName,jdbcType=OTHER},
      </if>
      <if test="record.quatity != null">
        quatity = #{record.quatity,jdbcType=OTHER},
      </if>
      <if test="record.businessDate != null">
        business_date = #{record.businessDate,jdbcType=OTHER},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=OTHER},
      </if>
      <if test="record.materialChannel != null">
        material_channel = #{record.materialChannel,jdbcType=OTHER},
      </if>
      <if test="record.retailAmount != null">
        retail_amount = #{record.retailAmount,jdbcType=OTHER},
      </if>
      <if test="record.costTax != null">
        cost_tax = #{record.costTax,jdbcType=OTHER},
      </if>
      <if test="record.costNoTax != null">
        cost_no_tax = #{record.costNoTax,jdbcType=OTHER},
      </if>
      <if test="record.apprdocno != null">
        apprdocno = #{record.apprdocno,jdbcType=OTHER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_store_sales_by_sup_temp
    set id = #{record.id,jdbcType=OTHER},
      manufacturer_code = #{record.manufacturerCode,jdbcType=OTHER},
      manufacturer = #{record.manufacturer,jdbcType=OTHER},
      supplier_code = #{record.supplierCode,jdbcType=OTHER},
      supplier_name = #{record.supplierName,jdbcType=OTHER},
      store_code = #{record.storeCode,jdbcType=OTHER},
      store_name = #{record.storeName,jdbcType=OTHER},
      data_granularity = #{record.dataGranularity,jdbcType=OTHER},
      company_code = #{record.companyCode,jdbcType=OTHER},
      company_name = #{record.companyName,jdbcType=OTHER},
      goods_code = #{record.goodsCode,jdbcType=OTHER},
      goods_name = #{record.goodsName,jdbcType=OTHER},
      specifications = #{record.specifications,jdbcType=OTHER},
      bar_code = #{record.barCode,jdbcType=OTHER},
      unit = #{record.unit,jdbcType=OTHER},
      batch_number = #{record.batchNumber,jdbcType=OTHER},
      validity_date = #{record.validityDate,jdbcType=OTHER},
      produce_date = #{record.produceDate,jdbcType=OTHER},
      warehouse_code = #{record.warehouseCode,jdbcType=OTHER},
      warehouse_name = #{record.warehouseName,jdbcType=OTHER},
      quatity = #{record.quatity,jdbcType=OTHER},
      business_date = #{record.businessDate,jdbcType=OTHER},
      gmt_create = #{record.gmtCreate,jdbcType=OTHER},
      material_channel = #{record.materialChannel,jdbcType=OTHER},
      retail_amount = #{record.retailAmount,jdbcType=OTHER},
      cost_tax = #{record.costTax,jdbcType=OTHER},
      cost_no_tax = #{record.costNoTax,jdbcType=OTHER},
      apprdocno = #{record.apprdocno,jdbcType=OTHER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entityTidb.SrmStoreSalesBySupTemp">
    update srm_store_sales_by_sup_temp
    <set>
      <if test="manufacturerCode != null">
        manufacturer_code = #{manufacturerCode,jdbcType=OTHER},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=OTHER},
      </if>
      <if test="supplierCode != null">
        supplier_code = #{supplierCode,jdbcType=OTHER},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=OTHER},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=OTHER},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=OTHER},
      </if>
      <if test="dataGranularity != null">
        data_granularity = #{dataGranularity,jdbcType=OTHER},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=OTHER},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=OTHER},
      </if>
      <if test="goodsCode != null">
        goods_code = #{goodsCode,jdbcType=OTHER},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=OTHER},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=OTHER},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=OTHER},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=OTHER},
      </if>
      <if test="batchNumber != null">
        batch_number = #{batchNumber,jdbcType=OTHER},
      </if>
      <if test="validityDate != null">
        validity_date = #{validityDate,jdbcType=OTHER},
      </if>
      <if test="produceDate != null">
        produce_date = #{produceDate,jdbcType=OTHER},
      </if>
      <if test="warehouseCode != null">
        warehouse_code = #{warehouseCode,jdbcType=OTHER},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=OTHER},
      </if>
      <if test="quatity != null">
        quatity = #{quatity,jdbcType=OTHER},
      </if>
      <if test="businessDate != null">
        business_date = #{businessDate,jdbcType=OTHER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=OTHER},
      </if>
      <if test="materialChannel != null">
        material_channel = #{materialChannel,jdbcType=OTHER},
      </if>
      <if test="retailAmount != null">
        retail_amount = #{retailAmount,jdbcType=OTHER},
      </if>
      <if test="costTax != null">
        cost_tax = #{costTax,jdbcType=OTHER},
      </if>
      <if test="costNoTax != null">
        cost_no_tax = #{costNoTax,jdbcType=OTHER},
      </if>
      <if test="apprdocno != null">
        apprdocno = #{apprdocno,jdbcType=OTHER},
      </if>
    </set>
    where id = #{id,jdbcType=OTHER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entityTidb.SrmStoreSalesBySupTemp">
    update srm_store_sales_by_sup_temp
    set manufacturer_code = #{manufacturerCode,jdbcType=OTHER},
      manufacturer = #{manufacturer,jdbcType=OTHER},
      supplier_code = #{supplierCode,jdbcType=OTHER},
      supplier_name = #{supplierName,jdbcType=OTHER},
      store_code = #{storeCode,jdbcType=OTHER},
      store_name = #{storeName,jdbcType=OTHER},
      data_granularity = #{dataGranularity,jdbcType=OTHER},
      company_code = #{companyCode,jdbcType=OTHER},
      company_name = #{companyName,jdbcType=OTHER},
      goods_code = #{goodsCode,jdbcType=OTHER},
      goods_name = #{goodsName,jdbcType=OTHER},
      specifications = #{specifications,jdbcType=OTHER},
      bar_code = #{barCode,jdbcType=OTHER},
      unit = #{unit,jdbcType=OTHER},
      batch_number = #{batchNumber,jdbcType=OTHER},
      validity_date = #{validityDate,jdbcType=OTHER},
      produce_date = #{produceDate,jdbcType=OTHER},
      warehouse_code = #{warehouseCode,jdbcType=OTHER},
      warehouse_name = #{warehouseName,jdbcType=OTHER},
      quatity = #{quatity,jdbcType=OTHER},
      business_date = #{businessDate,jdbcType=OTHER},
      gmt_create = #{gmtCreate,jdbcType=OTHER},
      material_channel = #{materialChannel,jdbcType=OTHER},
      retail_amount = #{retailAmount,jdbcType=OTHER},
      cost_tax = #{costTax,jdbcType=OTHER},
      cost_no_tax = #{costNoTax,jdbcType=OTHER},
      apprdocno = #{apprdocno,jdbcType=OTHER}
    where id = #{id,jdbcType=OTHER}
  </update>
</mapper>
