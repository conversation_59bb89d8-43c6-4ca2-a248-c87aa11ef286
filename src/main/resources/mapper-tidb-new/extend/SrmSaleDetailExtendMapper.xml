<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapperTidbNew.extend.SrmSaleDetailExtendMapper">
    <resultMap id="BaseResultMap" type="com.cowell.purchase.entityTidb.SrmSaleDetail">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="business_day_date" jdbcType="VARCHAR" property="businessDayDate" />
        <result column="tran_time" jdbcType="VARCHAR" property="tranTime" />
        <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
        <result column="company_name" jdbcType="VARCHAR" property="companyName" />
        <result column="retail_store_id" jdbcType="VARCHAR" property="retailStoreId" />
        <result column="retail_store_name" jdbcType="VARCHAR" property="retailStoreName" />
        <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
        <result column="order_no_line" jdbcType="VARCHAR" property="orderNoLine" />
        <result column="temid" jdbcType="VARCHAR" property="temid" />
        <result column="temid_name" jdbcType="VARCHAR" property="temidName" />
        <result column="temid_spec" jdbcType="VARCHAR" property="temidSpec" />
        <result column="batch_id" jdbcType="VARCHAR" property="batchId" />
        <result column="retail_quantity" jdbcType="DECIMAL" property="retailQuantity" />
        <result column="unit" jdbcType="VARCHAR" property="unit" />
        <result column="price_tax" jdbcType="DECIMAL" property="priceTax" />
        <result column="retail_amount" jdbcType="DECIMAL" property="retailAmount" />
        <result column="dmbtr" jdbcType="DECIMAL" property="dmbtr" />
        <result column="eff_date" jdbcType="VARCHAR" property="effDate" />
        <result column="member_id" jdbcType="BIGINT" property="memberId" />
        <result column="manu" jdbcType="VARCHAR" property="manu" />
        <result column="manu_code" jdbcType="VARCHAR" property="manuCode" />
        <result column="lifnr" jdbcType="VARCHAR" property="lifnr" />
        <result column="lifnr_name" jdbcType="VARCHAR" property="lifnrName" />
        <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
        <result column="batch_code" jdbcType="VARCHAR" property="batchCode" />
        <result column="app_num" jdbcType="VARCHAR" property="appNum" />
        <result column="product_date" jdbcType="VARCHAR" property="productDate" />
        <result column="material_channel" jdbcType="VARCHAR" property="materialChannel" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="rule_no" jdbcType="VARCHAR" property="ruleNo" />
        <result column="type" jdbcType="TINYINT" property="type" />
        <result column="province" jdbcType="VARCHAR" property="province" />
        <result column="city" jdbcType="VARCHAR" property="city" />
        <result column="member_name" jdbcType="VARCHAR" property="memberName" />
        <result column="member_phone" jdbcType="VARCHAR" property="memberPhone" />
        <result column="pay_type" jdbcType="VARCHAR" property="payType" />
        <result column="pay_name" jdbcType="VARCHAR" property="payName" />
        <result column="chnl" jdbcType="VARCHAR" property="chnl" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    </resultMap>

    <insert id="batchInsertSelective" parameterType="java.util.List">
        insert into srm_sale_detail ( business_day_date, tran_time,
        company_code, company_name, retail_store_id,
        retail_store_name, order_no, order_no_line,
        temid, temid_name, temid_spec,
        batch_id, retail_quantity, unit,
        price_tax, retail_amount, dmbtr,
        eff_date, member_id, manu,
        manu_code, lifnr, lifnr_name,
        bar_code, batch_code, app_num,
        product_date, material_channel, rule_no, `type`)
        values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.businessDayDate,jdbcType=VARCHAR}, #{item.tranTime,jdbcType=VARCHAR},
            #{item.companyCode,jdbcType=VARCHAR}, #{item.companyName,jdbcType=VARCHAR}, #{item.retailStoreId,jdbcType=VARCHAR},
            #{item.retailStoreName,jdbcType=VARCHAR}, #{item.orderNo,jdbcType=VARCHAR}, #{item.orderNoLine,jdbcType=VARCHAR},
            #{item.temid,jdbcType=VARCHAR}, #{item.temidName,jdbcType=VARCHAR}, #{item.temidSpec,jdbcType=VARCHAR},
            #{item.batchId,jdbcType=VARCHAR}, #{item.retailQuantity,jdbcType=DECIMAL}, #{item.unit,jdbcType=VARCHAR},
            #{item.priceTax,jdbcType=DECIMAL}, #{item.retailAmount,jdbcType=DECIMAL}, #{item.dmbtr,jdbcType=DECIMAL},
            #{item.effDate,jdbcType=VARCHAR}, #{item.memberId,jdbcType=BIGINT}, #{item.manu,jdbcType=VARCHAR},
            #{item.manuCode,jdbcType=VARCHAR}, #{item.lifnr,jdbcType=VARCHAR}, #{item.lifnrName,jdbcType=VARCHAR},
            #{item.barCode,jdbcType=VARCHAR}, #{item.batchCode,jdbcType=VARCHAR}, #{item.appNum,jdbcType=VARCHAR},
            #{item.productDate,jdbcType=VARCHAR}, #{item.materialChannel,jdbcType=VARCHAR}, #{item.ruleNo,jdbcType=VARCHAR}, #{item.type,jdbcType=TINYINT})
        </foreach>
    </insert>

    <update id="batchUpdateSelective" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update srm_sale_detail
            <set>
                <if test="item.ruleNo != null">
                    rule_no = #{item.ruleNo},
                </if>
                <if test="item.status != null">
                    status = #{item.status},
                </if>
            </set>
            where temid=#{item.temid}
            and retail_store_id=#{item.retailStoreId}
            and business_day_date between #{item.ruleStartDate} and #{item.ruleEndDate}
            and `type`=#{item.type}
        </foreach>
    </update>
</mapper>
