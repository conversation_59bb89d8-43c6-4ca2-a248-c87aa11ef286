<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapperTidbNew.extend.SrmSupplierInventoryYmExtendMapper">
    <delete id="deleteByCondition" parameterType="java.lang.Long">
        SET SESSION tidb_batch_delete=1;
        SET SESSION tidb_dml_batch_size=5000;
        delete from srm_supplier_inventory_ym WHERE gmt_create >= '2020-07-27 18:26:00' limit ${limit};
    </delete>

    <select id="selectCountByCondition" parameterType="java.lang.String" resultType="java.lang.Integer">
        <![CDATA[
            SELECT COUNT(*) FROM `srm_supplier_inventory_ym` WHERE gmt_create >= '2020-07-27 17:26:00' AND gmt_create <= '2020-07-27 18:00:00';
        ]]>
    </select>

    <select id="selectMaxIdsByCondition" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT MAX(id) id FROM `srm_supplier_inventory_ym` WHERE business_date in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        order by id desc
    </select>

    <delete id="deleteBatchByCondition" parameterType="com.cowell.purchase.service.dto.DeleteBatchDTO">
        <foreach collection="param.sizeList" item="item" index="index" separator=";">
            <![CDATA[
                SET SESSION tidb_batch_delete=1;
                SET SESSION tidb_dml_batch_size=5000;
                delete from srm_supplier_inventory_ym WHERE gmt_create >= '2020-07-27 17:26:00' AND gmt_create <= '2020-07-27 18:00:00' limit ${param.limit}
            ]]>
        </foreach>
    </delete>

    <delete id="deleteBatchByCondition2">
        SET SESSION tidb_batch_delete=1;
        SET SESSION tidb_dml_batch_size=5000;
        SET SESSION tidb_mem_quota_query = 4;
        SET SESSION tidb_disable_txn_auto_retry = off;
        SET SESSION tidb_retry_limit = 1000;
        delete from srm_supplier_inventory_ym WHERE id&gt;=#{minId} and id&lt;=#{maxId} and business_date = #{businessDate}
    </delete>


    <select id="getCountId" parameterType="java.util.List" resultType="com.cowell.purchase.service.dto.DataInvYmDto">
        SELECT MAX(id) maxId, MIN(id) minId FROM `srm_supplier_inventory_ym` WHERE business_date = #{businessDate}
    </select>

    <select id="selectCountByCondition2" resultType="java.lang.Integer">
        SELECT COUNT(id) FROM `srm_supplier_inventory_ym` WHERE business_date in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>
