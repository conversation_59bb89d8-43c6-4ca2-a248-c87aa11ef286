<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapperTidbNew.extend.SrmStoreSalesByManuDExtendMapper">
    <delete id="deleteBatchByCondition2">
        SET SESSION tidb_batch_delete=1;
        SET SESSION tidb_dml_batch_size=5000;
        SET SESSION tidb_mem_quota_query = 4;
        delete from srm_store_sales_by_manu_d WHERE id&gt;=#{minId} and id&lt;=#{maxId} and business_date in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>
</mapper>