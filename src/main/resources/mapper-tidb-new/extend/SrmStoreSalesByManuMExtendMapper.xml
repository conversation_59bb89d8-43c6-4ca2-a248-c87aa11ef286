<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapperTidbNew.extend.SrmStoreSalesByManuMExtendMapper">
    <delete id="deleteByCondition" parameterType="java.lang.String">
        SET SESSION tidb_batch_delete=1;
        SET SESSION tidb_dml_batch_size=5000;
        delete from srm_store_sales_by_manu_m WHERE business_date = #{businessDate,jdbcType=VARCHAR};
    </delete>

    <select id="selectIdsByCondition" parameterType="java.lang.String" resultType="java.lang.Long">
        SELECT id FROM `srm_store_sales_by_manu_m` WHERE business_date in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <delete id="deleteBatchByCondition" parameterType="java.util.List">
        SET SESSION tidb_batch_delete=1;
        SET SESSION tidb_dml_batch_size=5000;
        delete from srm_store_sales_by_manu_m WHERE id in
        <foreach collection="list" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteBatchByCondition2">
        SET SESSION tidb_batch_delete=1;
        SET SESSION tidb_dml_batch_size=5000;
        SET SESSION tidb_mem_quota_query = 4;
        delete from srm_store_sales_by_manu_m WHERE id&gt;=#{minId} and id&lt;=#{maxId} and business_date in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>

    <select id="getCountId" parameterType="java.util.List" resultType="com.cowell.purchase.service.dto.DataInvYmDto">
        SELECT MAX(id) maxId, MIN(id) minId FROM `srm_store_sales_by_manu_m` WHERE business_date in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectCountByCondition" resultType="java.lang.Integer">
        SELECT COUNT(id) FROM `srm_store_sales_by_manu_m` WHERE business_date in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>
