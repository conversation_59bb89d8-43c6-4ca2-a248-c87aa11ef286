<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapperTidbNew.extend.SrmDtpSalesExtendMapper">
    <delete id="deleteOrderData2" parameterType="java.lang.Integer">
        SET SESSION tidb_batch_delete=1;
        SET SESSION tidb_dml_batch_size=5000;
        delete from srm_dtp_sales WHERE id in
        <foreach collection="list" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
        limit 2;
    </delete>
</mapper>
