<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapperTidbNew.extend.TSrmBusinessDataFlowByManuExtendMapper">

    <resultMap id="BaseResultMap" type="com.cowell.purchase.entityTidb.SrmBusinessDataFlowByManu">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="manufacturer_code" jdbcType="VARCHAR" property="manufacturerCode" />
        <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
        <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode" />
        <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
        <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
        <result column="company_name" jdbcType="VARCHAR" property="companyName" />
        <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
        <result column="specifications" jdbcType="VARCHAR" property="specifications" />
        <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
        <result column="unit" jdbcType="VARCHAR" property="unit" />
        <result column="batch_number" jdbcType="VARCHAR" property="batchNumber" />
        <result column="validity_date" jdbcType="VARCHAR" property="validityDate" />
        <result column="produce_date" jdbcType="VARCHAR" property="produceDate" />
        <result column="quatity" jdbcType="VARCHAR" property="quatity" />
        <result column="tran_type" jdbcType="VARCHAR" property="tranType" />
        <result column="tran_type_name" jdbcType="VARCHAR" property="tranTypeName" />
        <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
        <result column="old_order_no" jdbcType="VARCHAR" property="oldOrderNo" />
        <result column="business_date" jdbcType="VARCHAR" property="businessDate" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="price" jdbcType="VARCHAR" property="price" />
        <result column="no_tax_price" jdbcType="VARCHAR" property="noTaxPrice" />
        <result column="material_channel" jdbcType="VARCHAR" property="materialChannel" />
        <result column="batch_id" jdbcType="VARCHAR" property="batchId" />
        <result column="amount" jdbcType="VARCHAR" property="amount" />
        <result column="no_tax_amount" jdbcType="VARCHAR" property="noTaxAmount" />
        <result column="inflow_code" jdbcType="VARCHAR" property="inflowCode" />
        <result column="inflow_name" jdbcType="VARCHAR" property="inflowName" />
        <result column="inflow_type" jdbcType="VARCHAR" property="inflowType" />
        <result column="operator" jdbcType="VARCHAR" property="operator" />
        <result column="outflow_code" jdbcType="VARCHAR" property="outflowCode" />
        <result column="outflow_name" jdbcType="VARCHAR" property="outflowName" />
        <result column="outflow_type" jdbcType="VARCHAR" property="outflowType" />
        <result column="m_inflow_code" jdbcType="VARCHAR" property="mInflowCode" />
        <result column="m_inflow_name" jdbcType="VARCHAR" property="mInflowName" />
        <result column="m_inflow_type" jdbcType="VARCHAR" property="mInflowType" />
        <result column="m_inflow_zzzshkc" jdbcType="VARCHAR" property="mInflowZzzshkc" />
        <result column="m_inflow_zzzwlms" jdbcType="VARCHAR" property="mInflowZzzwlms" />
        <result column="stoub_no" jdbcType="VARCHAR" property="stoubNo" />
        <result column="stoub_item" jdbcType="VARCHAR" property="stoubItem" />
        <result column="apprdocno" jdbcType="VARCHAR" property="apprdocno" />
        <result column="user_type" jdbcType="VARCHAR" property="userType" />
        <result column="inflow_company_code" jdbcType="VARCHAR" property="inflowCompanyCode" />
        <result column="inflow_company_name" jdbcType="VARCHAR" property="inflowCompanyName" />
        <result column="upstream_supplier" jdbcType="VARCHAR" property="upstreamSupplier" />
        <result column="upstream_supplier_name" jdbcType="VARCHAR" property="upstreamSupplierName" />
    </resultMap>

    <sql id="Base_Column_List">
        id, manufacturer_code, manufacturer, supplier_code, supplier_name, company_code,
    company_name, goods_code, goods_name, specifications, bar_code, unit, batch_number,
    validity_date, produce_date, quatity, tran_type, tran_type_name, order_no, old_order_no,
    business_date, gmt_create, price, no_tax_price, material_channel, batch_id, amount,
    no_tax_amount, inflow_code, inflow_name, inflow_type, operator, outflow_code, outflow_name,
    outflow_type, m_inflow_code, m_inflow_name, m_inflow_type, m_inflow_zzzshkc, m_inflow_zzzwlms,
    stoub_no, stoub_item, apprdocno, user_type, inflow_company_code, inflow_company_name, upstream_supplier, upstream_supplier_name
    </sql>

    <delete id="deleteByCondition" parameterType="java.lang.String">
        SET SESSION tidb_batch_delete=1;
        SET SESSION tidb_dml_batch_size=5000;
        delete from srm_business_data_flow_by_manu WHERE business_date = #{businessDate,jdbcType=VARCHAR};
    </delete>

    <select id="selectIdsByCondition" resultType="java.lang.Long">
        SELECT id FROM `srm_business_data_flow_by_manu` WHERE business_date in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <delete id="deleteBatchByCondition" parameterType="java.util.List">
        SET SESSION tidb_batch_delete=1;
        SET SESSION tidb_dml_batch_size=5000;
        delete from srm_business_data_flow_by_manu WHERE id in
        <foreach collection="list" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteBatchByCondition2">
        SET SESSION tidb_batch_delete=1;
        SET SESSION tidb_dml_batch_size=5000;
        SET SESSION tidb_mem_quota_query = 4;
        delete from srm_business_data_flow_by_manu WHERE id&gt;=#{minId} and id&lt;=#{maxId} and business_date in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>

    <select id="getCountId" parameterType="java.util.List" resultType="com.cowell.purchase.service.dto.DataInvYmDto">
        SELECT MAX(id) maxId, MIN(id) minId FROM `srm_business_data_flow_by_manu` WHERE business_date in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectCountByCondition" resultType="java.lang.Integer">
        SELECT COUNT(id) FROM `srm_business_data_flow_by_manu` WHERE business_date in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <sql id="searchFromWhere">
        <if test="param.sapCodeList != null and param.sapCodeList.size() != 0">
            and company_code IN
            <foreach collection="param.sapCodeList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.startDate != null and param.endDate != null">
            AND business_date BETWEEN #{param.startDate} AND #{param.endDate}
        </if>
        <if test="param.goodsNoList != null and param.goodsNoList.size() != 0">
            and goods_code IN
            <foreach collection="param.goodsNoList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.tranTypeList != null and param.tranTypeList.size() != 0">
            and tran_type IN
            <foreach collection="param.tranTypeList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.goodsPremList != null and param.goodsPremList.size() != 0">
            and material_channel IN
            <foreach collection="param.goodsPremList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.excludeCompanyCodeList != null and param.excludeCompanyCodeList.size() != 0">
            and inflow_company_code IN
            <foreach collection="param.excludeCompanyCodeList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.warehouseCodeList != null and param.warehouseCodeList.size() != 0">
            and outflow_code IN
            <foreach collection="param.warehouseCodeList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.dataExpireStart != null and param.dataExpireEnd != null">
            AND business_date BETWEEN #{param.dataExpireStart} AND #{param.dataExpireEnd}
        </if>
        <if test="param.excludeCompanyGoods != null and param.excludeCompanyGoods != ''">
            AND !( company_code in
            <foreach collection="param.filterCompany" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
              and goods_code in
            <foreach collection="param.filterGoodsCodeList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="param.storeDistribution != null and param.storeDistribution == true">
            AND !(outflow_type = 'B' AND inflow_type = 'B')
        </if>

    </sql>

    <select id="searchDataList" resultMap="BaseResultMap">
        select * from (
            select *, '' AS groupno from srm_business_data_flow_by_manu where 1=1 <include refid="searchFromWhere"></include>
            union all
            select * from srm_business_data_flow_by_manu_ex where data_flag=2 <include refid="searchFromWhere"></include>
        ) a
        order by a.id desc
        <if test="param.limit != null">
            <if test="param.offset != null">
                limit ${param.offset}, ${param.limit}
            </if>
            <if test="param.offset == null">
                limit ${param.limit}
            </if>
        </if>
    </select>
</mapper>
