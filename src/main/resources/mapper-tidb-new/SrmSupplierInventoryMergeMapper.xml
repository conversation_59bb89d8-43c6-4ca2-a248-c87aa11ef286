<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapperTidbNew.SrmSupplierInventoryMergeMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entityTidb.SrmSupplierInventoryMerge">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="manufacturer_code" jdbcType="VARCHAR" property="manufacturerCode" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="batch_number" jdbcType="VARCHAR" property="batchNumber" />
    <result column="validity_date" jdbcType="VARCHAR" property="validityDate" />
    <result column="produce_date" jdbcType="VARCHAR" property="produceDate" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="quatity" jdbcType="VARCHAR" property="quatity" />
    <result column="io_type" jdbcType="VARCHAR" property="ioType" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="old_order_no" jdbcType="VARCHAR" property="oldOrderNo" />
    <result column="business_date" jdbcType="VARCHAR" property="businessDate" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="material_channel" jdbcType="VARCHAR" property="materialChannel" />
    <result column="amount" jdbcType="VARCHAR" property="amount" />
    <result column="no_tax_amount" jdbcType="VARCHAR" property="noTaxAmount" />
    <result column="inventory_type" jdbcType="VARCHAR" property="inventoryType" />
    <result column="inventory_dimension" jdbcType="VARCHAR" property="inventoryDimension" />
    <result column="apprdocno" jdbcType="VARCHAR" property="apprdocno" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, manufacturer_code, manufacturer, supplier_code, supplier_name, company_code,
    company_name, store_code, store_name, goods_code, goods_name, specifications, bar_code,
    unit, batch_number, validity_date, produce_date, warehouse_code, warehouse_name,
    quatity, io_type, order_no, old_order_no, business_date, gmt_create, material_channel,
    amount, no_tax_amount, inventory_type, inventory_dimension, apprdocno
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entityTidb.SrmSupplierInventoryMergeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_supplier_inventory_merge FORCE INDEX(idx_g_b_c_t_m)
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from srm_supplier_inventory_merge
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from srm_supplier_inventory_merge
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entityTidb.SrmSupplierInventoryMergeExample">
    delete from srm_supplier_inventory_merge
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entityTidb.SrmSupplierInventoryMerge">
    insert into srm_supplier_inventory_merge (id, manufacturer_code, manufacturer,
      supplier_code, supplier_name, company_code,
      company_name, store_code, store_name,
      goods_code, goods_name, specifications,
      bar_code, unit, batch_number,
      validity_date, produce_date, warehouse_code,
      warehouse_name, quatity, io_type,
      order_no, old_order_no, business_date,
      gmt_create, material_channel, amount,
      no_tax_amount, inventory_type, inventory_dimension,
      apprdocno)
    values (#{id,jdbcType=BIGINT}, #{manufacturerCode,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR},
      #{supplierCode,jdbcType=VARCHAR}, #{supplierName,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR},
      #{companyName,jdbcType=VARCHAR}, #{storeCode,jdbcType=VARCHAR}, #{storeName,jdbcType=VARCHAR},
      #{goodsCode,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, #{specifications,jdbcType=VARCHAR},
      #{barCode,jdbcType=VARCHAR}, #{unit,jdbcType=VARCHAR}, #{batchNumber,jdbcType=VARCHAR},
      #{validityDate,jdbcType=VARCHAR}, #{produceDate,jdbcType=VARCHAR}, #{warehouseCode,jdbcType=VARCHAR},
      #{warehouseName,jdbcType=VARCHAR}, #{quatity,jdbcType=VARCHAR}, #{ioType,jdbcType=VARCHAR},
      #{orderNo,jdbcType=VARCHAR}, #{oldOrderNo,jdbcType=VARCHAR}, #{businessDate,jdbcType=VARCHAR},
      #{gmtCreate,jdbcType=TIMESTAMP}, #{materialChannel,jdbcType=VARCHAR}, #{amount,jdbcType=VARCHAR},
      #{noTaxAmount,jdbcType=VARCHAR}, #{inventoryType,jdbcType=VARCHAR}, #{inventoryDimension,jdbcType=VARCHAR},
      #{apprdocno,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entityTidb.SrmSupplierInventoryMerge">
    insert into srm_supplier_inventory_merge
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="manufacturerCode != null">
        manufacturer_code,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="supplierCode != null">
        supplier_code,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="goodsCode != null">
        goods_code,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="batchNumber != null">
        batch_number,
      </if>
      <if test="validityDate != null">
        validity_date,
      </if>
      <if test="produceDate != null">
        produce_date,
      </if>
      <if test="warehouseCode != null">
        warehouse_code,
      </if>
      <if test="warehouseName != null">
        warehouse_name,
      </if>
      <if test="quatity != null">
        quatity,
      </if>
      <if test="ioType != null">
        io_type,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="oldOrderNo != null">
        old_order_no,
      </if>
      <if test="businessDate != null">
        business_date,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="materialChannel != null">
        material_channel,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount,
      </if>
      <if test="inventoryType != null">
        inventory_type,
      </if>
      <if test="inventoryDimension != null">
        inventory_dimension,
      </if>
      <if test="apprdocno != null">
        apprdocno,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="manufacturerCode != null">
        #{manufacturerCode,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="supplierCode != null">
        #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="batchNumber != null">
        #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        #{validityDate,jdbcType=VARCHAR},
      </if>
      <if test="produceDate != null">
        #{produceDate,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="quatity != null">
        #{quatity,jdbcType=VARCHAR},
      </if>
      <if test="ioType != null">
        #{ioType,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="oldOrderNo != null">
        #{oldOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="businessDate != null">
        #{businessDate,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="materialChannel != null">
        #{materialChannel,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=VARCHAR},
      </if>
      <if test="noTaxAmount != null">
        #{noTaxAmount,jdbcType=VARCHAR},
      </if>
      <if test="inventoryType != null">
        #{inventoryType,jdbcType=VARCHAR},
      </if>
      <if test="inventoryDimension != null">
        #{inventoryDimension,jdbcType=VARCHAR},
      </if>
      <if test="apprdocno != null">
        #{apprdocno,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entityTidb.SrmSupplierInventoryMergeExample" resultType="java.lang.Long">
    select count(*) from srm_supplier_inventory_merge
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_supplier_inventory_merge
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.manufacturerCode != null">
        manufacturer_code = #{record.manufacturerCode,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierCode != null">
        supplier_code = #{record.supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierName != null">
        supplier_name = #{record.supplierName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCode != null">
        goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=VARCHAR},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNumber != null">
        batch_number = #{record.batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.validityDate != null">
        validity_date = #{record.validityDate,jdbcType=VARCHAR},
      </if>
      <if test="record.produceDate != null">
        produce_date = #{record.produceDate,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseCode != null">
        warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseName != null">
        warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="record.quatity != null">
        quatity = #{record.quatity,jdbcType=VARCHAR},
      </if>
      <if test="record.ioType != null">
        io_type = #{record.ioType,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNo != null">
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.oldOrderNo != null">
        old_order_no = #{record.oldOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.businessDate != null">
        business_date = #{record.businessDate,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.materialChannel != null">
        material_channel = #{record.materialChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=VARCHAR},
      </if>
      <if test="record.noTaxAmount != null">
        no_tax_amount = #{record.noTaxAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.inventoryType != null">
        inventory_type = #{record.inventoryType,jdbcType=VARCHAR},
      </if>
      <if test="record.inventoryDimension != null">
        inventory_dimension = #{record.inventoryDimension,jdbcType=VARCHAR},
      </if>
      <if test="record.apprdocno != null">
        apprdocno = #{record.apprdocno,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_supplier_inventory_merge
    set id = #{record.id,jdbcType=BIGINT},
      manufacturer_code = #{record.manufacturerCode,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      supplier_code = #{record.supplierCode,jdbcType=VARCHAR},
      supplier_name = #{record.supplierName,jdbcType=VARCHAR},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      specifications = #{record.specifications,jdbcType=VARCHAR},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      unit = #{record.unit,jdbcType=VARCHAR},
      batch_number = #{record.batchNumber,jdbcType=VARCHAR},
      validity_date = #{record.validityDate,jdbcType=VARCHAR},
      produce_date = #{record.produceDate,jdbcType=VARCHAR},
      warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      quatity = #{record.quatity,jdbcType=VARCHAR},
      io_type = #{record.ioType,jdbcType=VARCHAR},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      old_order_no = #{record.oldOrderNo,jdbcType=VARCHAR},
      business_date = #{record.businessDate,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      material_channel = #{record.materialChannel,jdbcType=VARCHAR},
      amount = #{record.amount,jdbcType=VARCHAR},
      no_tax_amount = #{record.noTaxAmount,jdbcType=VARCHAR},
      inventory_type = #{record.inventoryType,jdbcType=VARCHAR},
      inventory_dimension = #{record.inventoryDimension,jdbcType=VARCHAR},
      apprdocno = #{record.apprdocno,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entityTidb.SrmSupplierInventoryMerge">
    update srm_supplier_inventory_merge
    <set>
      <if test="manufacturerCode != null">
        manufacturer_code = #{manufacturerCode,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="supplierCode != null">
        supplier_code = #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        goods_code = #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="batchNumber != null">
        batch_number = #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        validity_date = #{validityDate,jdbcType=VARCHAR},
      </if>
      <if test="produceDate != null">
        produce_date = #{produceDate,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="quatity != null">
        quatity = #{quatity,jdbcType=VARCHAR},
      </if>
      <if test="ioType != null">
        io_type = #{ioType,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="oldOrderNo != null">
        old_order_no = #{oldOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="businessDate != null">
        business_date = #{businessDate,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="materialChannel != null">
        material_channel = #{materialChannel,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=VARCHAR},
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount = #{noTaxAmount,jdbcType=VARCHAR},
      </if>
      <if test="inventoryType != null">
        inventory_type = #{inventoryType,jdbcType=VARCHAR},
      </if>
      <if test="inventoryDimension != null">
        inventory_dimension = #{inventoryDimension,jdbcType=VARCHAR},
      </if>
      <if test="apprdocno != null">
        apprdocno = #{apprdocno,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entityTidb.SrmSupplierInventoryMerge">
    update srm_supplier_inventory_merge
    set manufacturer_code = #{manufacturerCode,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      supplier_code = #{supplierCode,jdbcType=VARCHAR},
      supplier_name = #{supplierName,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      store_code = #{storeCode,jdbcType=VARCHAR},
      store_name = #{storeName,jdbcType=VARCHAR},
      goods_code = #{goodsCode,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      specifications = #{specifications,jdbcType=VARCHAR},
      bar_code = #{barCode,jdbcType=VARCHAR},
      unit = #{unit,jdbcType=VARCHAR},
      batch_number = #{batchNumber,jdbcType=VARCHAR},
      validity_date = #{validityDate,jdbcType=VARCHAR},
      produce_date = #{produceDate,jdbcType=VARCHAR},
      warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      quatity = #{quatity,jdbcType=VARCHAR},
      io_type = #{ioType,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      old_order_no = #{oldOrderNo,jdbcType=VARCHAR},
      business_date = #{businessDate,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      material_channel = #{materialChannel,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=VARCHAR},
      no_tax_amount = #{noTaxAmount,jdbcType=VARCHAR},
      inventory_type = #{inventoryType,jdbcType=VARCHAR},
      inventory_dimension = #{inventoryDimension,jdbcType=VARCHAR},
      apprdocno = #{apprdocno,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
