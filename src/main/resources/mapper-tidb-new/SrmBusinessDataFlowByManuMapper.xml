<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapperTidbNew.TSrmBusinessDataFlowByManuMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entityTidb.SrmBusinessDataFlowByManu">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="manufacturer_code" jdbcType="VARCHAR" property="manufacturerCode" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="batch_number" jdbcType="VARCHAR" property="batchNumber" />
    <result column="validity_date" jdbcType="VARCHAR" property="validityDate" />
    <result column="produce_date" jdbcType="VARCHAR" property="produceDate" />
    <result column="quatity" jdbcType="VARCHAR" property="quatity" />
    <result column="tran_type" jdbcType="VARCHAR" property="tranType" />
    <result column="tran_type_name" jdbcType="VARCHAR" property="tranTypeName" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="old_order_no" jdbcType="VARCHAR" property="oldOrderNo" />
    <result column="business_date" jdbcType="VARCHAR" property="businessDate" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="price" jdbcType="VARCHAR" property="price" />
    <result column="no_tax_price" jdbcType="VARCHAR" property="noTaxPrice" />
    <result column="material_channel" jdbcType="VARCHAR" property="materialChannel" />
    <result column="batch_id" jdbcType="VARCHAR" property="batchId" />
    <result column="amount" jdbcType="VARCHAR" property="amount" />
    <result column="no_tax_amount" jdbcType="VARCHAR" property="noTaxAmount" />
    <result column="inflow_code" jdbcType="VARCHAR" property="inflowCode" />
    <result column="inflow_name" jdbcType="VARCHAR" property="inflowName" />
    <result column="inflow_type" jdbcType="VARCHAR" property="inflowType" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="outflow_code" jdbcType="VARCHAR" property="outflowCode" />
    <result column="outflow_name" jdbcType="VARCHAR" property="outflowName" />
    <result column="outflow_type" jdbcType="VARCHAR" property="outflowType" />
    <result column="m_inflow_code" jdbcType="VARCHAR" property="mInflowCode" />
    <result column="m_inflow_name" jdbcType="VARCHAR" property="mInflowName" />
    <result column="m_inflow_type" jdbcType="VARCHAR" property="mInflowType" />
    <result column="m_inflow_zzzshkc" jdbcType="VARCHAR" property="mInflowZzzshkc" />
    <result column="m_inflow_zzzwlms" jdbcType="VARCHAR" property="mInflowZzzwlms" />
    <result column="stoub_no" jdbcType="VARCHAR" property="stoubNo" />
    <result column="stoub_item" jdbcType="VARCHAR" property="stoubItem" />
    <result column="apprdocno" jdbcType="VARCHAR" property="apprdocno" />
    <result column="user_type" jdbcType="VARCHAR" property="userType" />
      <result column="inflow_company_code" jdbcType="VARCHAR" property="inflowCompanyCode" />
      <result column="inflow_company_name" jdbcType="VARCHAR" property="inflowCompanyName" />
      <result column="upstream_supplier" jdbcType="VARCHAR" property="upstreamSupplier" />
      <result column="upstream_supplier_name" jdbcType="VARCHAR" property="upstreamSupplierName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, manufacturer_code, manufacturer, supplier_code, supplier_name, company_code,
    company_name, goods_code, goods_name, specifications, bar_code, unit, batch_number,
    validity_date, produce_date, quatity, tran_type, tran_type_name, order_no, old_order_no,
    business_date, gmt_create, price, no_tax_price, material_channel, batch_id, amount,
    no_tax_amount, inflow_code, inflow_name, inflow_type, operator, outflow_code, outflow_name,
    outflow_type, m_inflow_code, m_inflow_name, m_inflow_type, m_inflow_zzzshkc, m_inflow_zzzwlms,
    stoub_no, stoub_item, apprdocno, user_type, inflow_company_code, inflow_company_name, upstream_supplier, upstream_supplier_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entityTidb.SrmBusinessDataFlowByManuExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_business_data_flow_by_manu FORCE INDEX(idx_c_g_b_t_m)
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from srm_business_data_flow_by_manu
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from srm_business_data_flow_by_manu
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entityTidb.SrmBusinessDataFlowByManuExample">
    delete from srm_business_data_flow_by_manu
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entityTidb.SrmBusinessDataFlowByManu">
    insert into srm_business_data_flow_by_manu (id, manufacturer_code, manufacturer,
      supplier_code, supplier_name, company_code,
      company_name, goods_code, goods_name,
      specifications, bar_code, unit,
      batch_number, validity_date, produce_date,
      quatity, tran_type, tran_type_name,
      order_no, old_order_no, business_date,
      gmt_create, price, no_tax_price,
      material_channel, batch_id, amount,
      no_tax_amount, inflow_code, inflow_name,
      inflow_type, operator, outflow_code,
      outflow_name, outflow_type, m_inflow_code,
      m_inflow_name, m_inflow_type, m_inflow_zzzshkc,
      m_inflow_zzzwlms, stoub_no, stoub_item,
      apprdocno, user_type)
    values (#{id,jdbcType=BIGINT}, #{manufacturerCode,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR},
      #{supplierCode,jdbcType=VARCHAR}, #{supplierName,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR},
      #{companyName,jdbcType=VARCHAR}, #{goodsCode,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR},
      #{specifications,jdbcType=VARCHAR}, #{barCode,jdbcType=VARCHAR}, #{unit,jdbcType=VARCHAR},
      #{batchNumber,jdbcType=VARCHAR}, #{validityDate,jdbcType=VARCHAR}, #{produceDate,jdbcType=VARCHAR},
      #{quatity,jdbcType=VARCHAR}, #{tranType,jdbcType=VARCHAR}, #{tranTypeName,jdbcType=VARCHAR},
      #{orderNo,jdbcType=VARCHAR}, #{oldOrderNo,jdbcType=VARCHAR}, #{businessDate,jdbcType=VARCHAR},
      #{gmtCreate,jdbcType=TIMESTAMP}, #{price,jdbcType=VARCHAR}, #{noTaxPrice,jdbcType=VARCHAR},
      #{materialChannel,jdbcType=VARCHAR}, #{batchId,jdbcType=VARCHAR}, #{amount,jdbcType=VARCHAR},
      #{noTaxAmount,jdbcType=VARCHAR}, #{inflowCode,jdbcType=VARCHAR}, #{inflowName,jdbcType=VARCHAR},
      #{inflowType,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{outflowCode,jdbcType=VARCHAR},
      #{outflowName,jdbcType=VARCHAR}, #{outflowType,jdbcType=VARCHAR}, #{mInflowCode,jdbcType=VARCHAR},
      #{mInflowName,jdbcType=VARCHAR}, #{mInflowType,jdbcType=VARCHAR}, #{mInflowZzzshkc,jdbcType=VARCHAR},
      #{mInflowZzzwlms,jdbcType=VARCHAR}, #{stoubNo,jdbcType=VARCHAR}, #{stoubItem,jdbcType=VARCHAR},
      #{apprdocno,jdbcType=VARCHAR}, #{userType,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entityTidb.SrmBusinessDataFlowByManu">
    insert into srm_business_data_flow_by_manu
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="manufacturerCode != null">
        manufacturer_code,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="supplierCode != null">
        supplier_code,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="goodsCode != null">
        goods_code,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="batchNumber != null">
        batch_number,
      </if>
      <if test="validityDate != null">
        validity_date,
      </if>
      <if test="produceDate != null">
        produce_date,
      </if>
      <if test="quatity != null">
        quatity,
      </if>
      <if test="tranType != null">
        tran_type,
      </if>
      <if test="tranTypeName != null">
        tran_type_name,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="oldOrderNo != null">
        old_order_no,
      </if>
      <if test="businessDate != null">
        business_date,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="noTaxPrice != null">
        no_tax_price,
      </if>
      <if test="materialChannel != null">
        material_channel,
      </if>
      <if test="batchId != null">
        batch_id,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount,
      </if>
      <if test="inflowCode != null">
        inflow_code,
      </if>
      <if test="inflowName != null">
        inflow_name,
      </if>
      <if test="inflowType != null">
        inflow_type,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="outflowCode != null">
        outflow_code,
      </if>
      <if test="outflowName != null">
        outflow_name,
      </if>
      <if test="outflowType != null">
        outflow_type,
      </if>
      <if test="mInflowCode != null">
        m_inflow_code,
      </if>
      <if test="mInflowName != null">
        m_inflow_name,
      </if>
      <if test="mInflowType != null">
        m_inflow_type,
      </if>
      <if test="mInflowZzzshkc != null">
        m_inflow_zzzshkc,
      </if>
      <if test="mInflowZzzwlms != null">
        m_inflow_zzzwlms,
      </if>
      <if test="stoubNo != null">
        stoub_no,
      </if>
      <if test="stoubItem != null">
        stoub_item,
      </if>
      <if test="apprdocno != null">
        apprdocno,
      </if>
      <if test="userType != null">
        user_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="manufacturerCode != null">
        #{manufacturerCode,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="supplierCode != null">
        #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="batchNumber != null">
        #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        #{validityDate,jdbcType=VARCHAR},
      </if>
      <if test="produceDate != null">
        #{produceDate,jdbcType=VARCHAR},
      </if>
      <if test="quatity != null">
        #{quatity,jdbcType=VARCHAR},
      </if>
      <if test="tranType != null">
        #{tranType,jdbcType=VARCHAR},
      </if>
      <if test="tranTypeName != null">
        #{tranTypeName,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="oldOrderNo != null">
        #{oldOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="businessDate != null">
        #{businessDate,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="price != null">
        #{price,jdbcType=VARCHAR},
      </if>
      <if test="noTaxPrice != null">
        #{noTaxPrice,jdbcType=VARCHAR},
      </if>
      <if test="materialChannel != null">
        #{materialChannel,jdbcType=VARCHAR},
      </if>
      <if test="batchId != null">
        #{batchId,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=VARCHAR},
      </if>
      <if test="noTaxAmount != null">
        #{noTaxAmount,jdbcType=VARCHAR},
      </if>
      <if test="inflowCode != null">
        #{inflowCode,jdbcType=VARCHAR},
      </if>
      <if test="inflowName != null">
        #{inflowName,jdbcType=VARCHAR},
      </if>
      <if test="inflowType != null">
        #{inflowType,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="outflowCode != null">
        #{outflowCode,jdbcType=VARCHAR},
      </if>
      <if test="outflowName != null">
        #{outflowName,jdbcType=VARCHAR},
      </if>
      <if test="outflowType != null">
        #{outflowType,jdbcType=VARCHAR},
      </if>
      <if test="mInflowCode != null">
        #{mInflowCode,jdbcType=VARCHAR},
      </if>
      <if test="mInflowName != null">
        #{mInflowName,jdbcType=VARCHAR},
      </if>
      <if test="mInflowType != null">
        #{mInflowType,jdbcType=VARCHAR},
      </if>
      <if test="mInflowZzzshkc != null">
        #{mInflowZzzshkc,jdbcType=VARCHAR},
      </if>
      <if test="mInflowZzzwlms != null">
        #{mInflowZzzwlms,jdbcType=VARCHAR},
      </if>
      <if test="stoubNo != null">
        #{stoubNo,jdbcType=VARCHAR},
      </if>
      <if test="stoubItem != null">
        #{stoubItem,jdbcType=VARCHAR},
      </if>
      <if test="apprdocno != null">
        #{apprdocno,jdbcType=VARCHAR},
      </if>
      <if test="userType != null">
        #{userType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entityTidb.SrmBusinessDataFlowByManuExample" resultType="java.lang.Long">
    select count(*) from srm_business_data_flow_by_manu
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_business_data_flow_by_manu
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.manufacturerCode != null">
        manufacturer_code = #{record.manufacturerCode,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierCode != null">
        supplier_code = #{record.supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierName != null">
        supplier_name = #{record.supplierName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCode != null">
        goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=VARCHAR},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNumber != null">
        batch_number = #{record.batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.validityDate != null">
        validity_date = #{record.validityDate,jdbcType=VARCHAR},
      </if>
      <if test="record.produceDate != null">
        produce_date = #{record.produceDate,jdbcType=VARCHAR},
      </if>
      <if test="record.quatity != null">
        quatity = #{record.quatity,jdbcType=VARCHAR},
      </if>
      <if test="record.tranType != null">
        tran_type = #{record.tranType,jdbcType=VARCHAR},
      </if>
      <if test="record.tranTypeName != null">
        tran_type_name = #{record.tranTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.orderNo != null">
        order_no = #{record.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.oldOrderNo != null">
        old_order_no = #{record.oldOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.businessDate != null">
        business_date = #{record.businessDate,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=VARCHAR},
      </if>
      <if test="record.noTaxPrice != null">
        no_tax_price = #{record.noTaxPrice,jdbcType=VARCHAR},
      </if>
      <if test="record.materialChannel != null">
        material_channel = #{record.materialChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.batchId != null">
        batch_id = #{record.batchId,jdbcType=VARCHAR},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=VARCHAR},
      </if>
      <if test="record.noTaxAmount != null">
        no_tax_amount = #{record.noTaxAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.inflowCode != null">
        inflow_code = #{record.inflowCode,jdbcType=VARCHAR},
      </if>
      <if test="record.inflowName != null">
        inflow_name = #{record.inflowName,jdbcType=VARCHAR},
      </if>
      <if test="record.inflowType != null">
        inflow_type = #{record.inflowType,jdbcType=VARCHAR},
      </if>
      <if test="record.operator != null">
        operator = #{record.operator,jdbcType=VARCHAR},
      </if>
      <if test="record.outflowCode != null">
        outflow_code = #{record.outflowCode,jdbcType=VARCHAR},
      </if>
      <if test="record.outflowName != null">
        outflow_name = #{record.outflowName,jdbcType=VARCHAR},
      </if>
      <if test="record.outflowType != null">
        outflow_type = #{record.outflowType,jdbcType=VARCHAR},
      </if>
      <if test="record.mInflowCode != null">
        m_inflow_code = #{record.mInflowCode,jdbcType=VARCHAR},
      </if>
      <if test="record.mInflowName != null">
        m_inflow_name = #{record.mInflowName,jdbcType=VARCHAR},
      </if>
      <if test="record.mInflowType != null">
        m_inflow_type = #{record.mInflowType,jdbcType=VARCHAR},
      </if>
      <if test="record.mInflowZzzshkc != null">
        m_inflow_zzzshkc = #{record.mInflowZzzshkc,jdbcType=VARCHAR},
      </if>
      <if test="record.mInflowZzzwlms != null">
        m_inflow_zzzwlms = #{record.mInflowZzzwlms,jdbcType=VARCHAR},
      </if>
      <if test="record.stoubNo != null">
        stoub_no = #{record.stoubNo,jdbcType=VARCHAR},
      </if>
      <if test="record.stoubItem != null">
        stoub_item = #{record.stoubItem,jdbcType=VARCHAR},
      </if>
      <if test="record.apprdocno != null">
        apprdocno = #{record.apprdocno,jdbcType=VARCHAR},
      </if>
      <if test="record.userType != null">
        user_type = #{record.userType,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_business_data_flow_by_manu
    set id = #{record.id,jdbcType=BIGINT},
      manufacturer_code = #{record.manufacturerCode,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      supplier_code = #{record.supplierCode,jdbcType=VARCHAR},
      supplier_name = #{record.supplierName,jdbcType=VARCHAR},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      specifications = #{record.specifications,jdbcType=VARCHAR},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      unit = #{record.unit,jdbcType=VARCHAR},
      batch_number = #{record.batchNumber,jdbcType=VARCHAR},
      validity_date = #{record.validityDate,jdbcType=VARCHAR},
      produce_date = #{record.produceDate,jdbcType=VARCHAR},
      quatity = #{record.quatity,jdbcType=VARCHAR},
      tran_type = #{record.tranType,jdbcType=VARCHAR},
      tran_type_name = #{record.tranTypeName,jdbcType=VARCHAR},
      order_no = #{record.orderNo,jdbcType=VARCHAR},
      old_order_no = #{record.oldOrderNo,jdbcType=VARCHAR},
      business_date = #{record.businessDate,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      price = #{record.price,jdbcType=VARCHAR},
      no_tax_price = #{record.noTaxPrice,jdbcType=VARCHAR},
      material_channel = #{record.materialChannel,jdbcType=VARCHAR},
      batch_id = #{record.batchId,jdbcType=VARCHAR},
      amount = #{record.amount,jdbcType=VARCHAR},
      no_tax_amount = #{record.noTaxAmount,jdbcType=VARCHAR},
      inflow_code = #{record.inflowCode,jdbcType=VARCHAR},
      inflow_name = #{record.inflowName,jdbcType=VARCHAR},
      inflow_type = #{record.inflowType,jdbcType=VARCHAR},
      operator = #{record.operator,jdbcType=VARCHAR},
      outflow_code = #{record.outflowCode,jdbcType=VARCHAR},
      outflow_name = #{record.outflowName,jdbcType=VARCHAR},
      outflow_type = #{record.outflowType,jdbcType=VARCHAR},
      m_inflow_code = #{record.mInflowCode,jdbcType=VARCHAR},
      m_inflow_name = #{record.mInflowName,jdbcType=VARCHAR},
      m_inflow_type = #{record.mInflowType,jdbcType=VARCHAR},
      m_inflow_zzzshkc = #{record.mInflowZzzshkc,jdbcType=VARCHAR},
      m_inflow_zzzwlms = #{record.mInflowZzzwlms,jdbcType=VARCHAR},
      stoub_no = #{record.stoubNo,jdbcType=VARCHAR},
      stoub_item = #{record.stoubItem,jdbcType=VARCHAR},
      apprdocno = #{record.apprdocno,jdbcType=VARCHAR},
      user_type = #{record.userType,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entityTidb.SrmBusinessDataFlowByManu">
    update srm_business_data_flow_by_manu
    <set>
      <if test="manufacturerCode != null">
        manufacturer_code = #{manufacturerCode,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="supplierCode != null">
        supplier_code = #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        goods_code = #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="batchNumber != null">
        batch_number = #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        validity_date = #{validityDate,jdbcType=VARCHAR},
      </if>
      <if test="produceDate != null">
        produce_date = #{produceDate,jdbcType=VARCHAR},
      </if>
      <if test="quatity != null">
        quatity = #{quatity,jdbcType=VARCHAR},
      </if>
      <if test="tranType != null">
        tran_type = #{tranType,jdbcType=VARCHAR},
      </if>
      <if test="tranTypeName != null">
        tran_type_name = #{tranTypeName,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="oldOrderNo != null">
        old_order_no = #{oldOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="businessDate != null">
        business_date = #{businessDate,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=VARCHAR},
      </if>
      <if test="noTaxPrice != null">
        no_tax_price = #{noTaxPrice,jdbcType=VARCHAR},
      </if>
      <if test="materialChannel != null">
        material_channel = #{materialChannel,jdbcType=VARCHAR},
      </if>
      <if test="batchId != null">
        batch_id = #{batchId,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=VARCHAR},
      </if>
      <if test="noTaxAmount != null">
        no_tax_amount = #{noTaxAmount,jdbcType=VARCHAR},
      </if>
      <if test="inflowCode != null">
        inflow_code = #{inflowCode,jdbcType=VARCHAR},
      </if>
      <if test="inflowName != null">
        inflow_name = #{inflowName,jdbcType=VARCHAR},
      </if>
      <if test="inflowType != null">
        inflow_type = #{inflowType,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="outflowCode != null">
        outflow_code = #{outflowCode,jdbcType=VARCHAR},
      </if>
      <if test="outflowName != null">
        outflow_name = #{outflowName,jdbcType=VARCHAR},
      </if>
      <if test="outflowType != null">
        outflow_type = #{outflowType,jdbcType=VARCHAR},
      </if>
      <if test="mInflowCode != null">
        m_inflow_code = #{mInflowCode,jdbcType=VARCHAR},
      </if>
      <if test="mInflowName != null">
        m_inflow_name = #{mInflowName,jdbcType=VARCHAR},
      </if>
      <if test="mInflowType != null">
        m_inflow_type = #{mInflowType,jdbcType=VARCHAR},
      </if>
      <if test="mInflowZzzshkc != null">
        m_inflow_zzzshkc = #{mInflowZzzshkc,jdbcType=VARCHAR},
      </if>
      <if test="mInflowZzzwlms != null">
        m_inflow_zzzwlms = #{mInflowZzzwlms,jdbcType=VARCHAR},
      </if>
      <if test="stoubNo != null">
        stoub_no = #{stoubNo,jdbcType=VARCHAR},
      </if>
      <if test="stoubItem != null">
        stoub_item = #{stoubItem,jdbcType=VARCHAR},
      </if>
      <if test="apprdocno != null">
        apprdocno = #{apprdocno,jdbcType=VARCHAR},
      </if>
      <if test="userType != null">
        user_type = #{userType,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entityTidb.SrmBusinessDataFlowByManu">
    update srm_business_data_flow_by_manu
    set manufacturer_code = #{manufacturerCode,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      supplier_code = #{supplierCode,jdbcType=VARCHAR},
      supplier_name = #{supplierName,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      goods_code = #{goodsCode,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      specifications = #{specifications,jdbcType=VARCHAR},
      bar_code = #{barCode,jdbcType=VARCHAR},
      unit = #{unit,jdbcType=VARCHAR},
      batch_number = #{batchNumber,jdbcType=VARCHAR},
      validity_date = #{validityDate,jdbcType=VARCHAR},
      produce_date = #{produceDate,jdbcType=VARCHAR},
      quatity = #{quatity,jdbcType=VARCHAR},
      tran_type = #{tranType,jdbcType=VARCHAR},
      tran_type_name = #{tranTypeName,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      old_order_no = #{oldOrderNo,jdbcType=VARCHAR},
      business_date = #{businessDate,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      price = #{price,jdbcType=VARCHAR},
      no_tax_price = #{noTaxPrice,jdbcType=VARCHAR},
      material_channel = #{materialChannel,jdbcType=VARCHAR},
      batch_id = #{batchId,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=VARCHAR},
      no_tax_amount = #{noTaxAmount,jdbcType=VARCHAR},
      inflow_code = #{inflowCode,jdbcType=VARCHAR},
      inflow_name = #{inflowName,jdbcType=VARCHAR},
      inflow_type = #{inflowType,jdbcType=VARCHAR},
      operator = #{operator,jdbcType=VARCHAR},
      outflow_code = #{outflowCode,jdbcType=VARCHAR},
      outflow_name = #{outflowName,jdbcType=VARCHAR},
      outflow_type = #{outflowType,jdbcType=VARCHAR},
      m_inflow_code = #{mInflowCode,jdbcType=VARCHAR},
      m_inflow_name = #{mInflowName,jdbcType=VARCHAR},
      m_inflow_type = #{mInflowType,jdbcType=VARCHAR},
      m_inflow_zzzshkc = #{mInflowZzzshkc,jdbcType=VARCHAR},
      m_inflow_zzzwlms = #{mInflowZzzwlms,jdbcType=VARCHAR},
      stoub_no = #{stoubNo,jdbcType=VARCHAR},
      stoub_item = #{stoubItem,jdbcType=VARCHAR},
      apprdocno = #{apprdocno,jdbcType=VARCHAR},
      user_type = #{userType,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
