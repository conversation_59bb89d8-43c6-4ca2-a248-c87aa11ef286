<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapperTidbNew.SrmStoreSalesBySupTempExtendMapper">
    <delete id="deleteByCondition" parameterType="java.lang.String">
        SET SESSION tidb_batch_delete=1;
        SET SESSION tidb_dml_batch_size=5000;
        delete from srm_store_sales_by_sup_temp WHERE business_date = #{businessDate,jdbcType=VARCHAR};
    </delete>
</mapper>
