<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapperTidbNew.TSrmStoreSalesByManuMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entityTidb.SrmStoreSalesByManu">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="manufacturer_code" jdbcType="VARCHAR" property="manufacturerCode" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="data_granularity" jdbcType="VARCHAR" property="dataGranularity" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="specifications" jdbcType="VARCHAR" property="specifications" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="batch_number" jdbcType="VARCHAR" property="batchNumber" />
    <result column="validity_date" jdbcType="VARCHAR" property="validityDate" />
    <result column="produce_date" jdbcType="VARCHAR" property="produceDate" />
    <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode" />
    <result column="warehouse_name" jdbcType="VARCHAR" property="warehouseName" />
    <result column="quatity" jdbcType="DECIMAL" property="quatity" />
    <result column="business_date" jdbcType="VARCHAR" property="businessDate" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="material_channel" jdbcType="VARCHAR" property="materialChannel" />
    <result column="retail_amount" jdbcType="DECIMAL" property="retailAmount" />
    <result column="cost_tax" jdbcType="DECIMAL" property="costTax" />
    <result column="cost_no_tax" jdbcType="DECIMAL" property="costNoTax" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, manufacturer_code, manufacturer, supplier_code, supplier_name, store_code, store_name,
    data_granularity, company_code, company_name, goods_code, goods_name, specifications,
    bar_code, unit, batch_number, validity_date, produce_date, warehouse_code, warehouse_name,
    quatity, business_date, gmt_create, material_channel, retail_amount, cost_tax, cost_no_tax
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entityTidb.SrmStoreSalesByManuExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from srm_store_sales_by_manu
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from srm_store_sales_by_manu
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from srm_store_sales_by_manu
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entityTidb.SrmStoreSalesByManuExample">
    delete from srm_store_sales_by_manu
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entityTidb.SrmStoreSalesByManu">
    insert into srm_store_sales_by_manu (id, manufacturer_code, manufacturer,
      supplier_code, supplier_name, store_code,
      store_name, data_granularity, company_code,
      company_name, goods_code, goods_name,
      specifications, bar_code, unit,
      batch_number, validity_date, produce_date,
      warehouse_code, warehouse_name, quatity,
      business_date, gmt_create, material_channel,
      retail_amount, cost_tax, cost_no_tax
      )
    values (#{id,jdbcType=INTEGER}, #{manufacturerCode,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR},
      #{supplierCode,jdbcType=VARCHAR}, #{supplierName,jdbcType=VARCHAR}, #{storeCode,jdbcType=VARCHAR},
      #{storeName,jdbcType=VARCHAR}, #{dataGranularity,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR},
      #{companyName,jdbcType=VARCHAR}, #{goodsCode,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR},
      #{specifications,jdbcType=VARCHAR}, #{barCode,jdbcType=VARCHAR}, #{unit,jdbcType=VARCHAR},
      #{batchNumber,jdbcType=VARCHAR}, #{validityDate,jdbcType=VARCHAR}, #{produceDate,jdbcType=VARCHAR},
      #{warehouseCode,jdbcType=VARCHAR}, #{warehouseName,jdbcType=VARCHAR}, #{quatity,jdbcType=DECIMAL},
      #{businessDate,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{materialChannel,jdbcType=VARCHAR},
      #{retailAmount,jdbcType=DECIMAL}, #{costTax,jdbcType=DECIMAL}, #{costNoTax,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entityTidb.SrmStoreSalesByManu">
    insert into srm_store_sales_by_manu
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="manufacturerCode != null">
        manufacturer_code,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="supplierCode != null">
        supplier_code,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="dataGranularity != null">
        data_granularity,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="goodsCode != null">
        goods_code,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="specifications != null">
        specifications,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="batchNumber != null">
        batch_number,
      </if>
      <if test="validityDate != null">
        validity_date,
      </if>
      <if test="produceDate != null">
        produce_date,
      </if>
      <if test="warehouseCode != null">
        warehouse_code,
      </if>
      <if test="warehouseName != null">
        warehouse_name,
      </if>
      <if test="quatity != null">
        quatity,
      </if>
      <if test="businessDate != null">
        business_date,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="materialChannel != null">
        material_channel,
      </if>
      <if test="retailAmount != null">
        retail_amount,
      </if>
      <if test="costTax != null">
        cost_tax,
      </if>
      <if test="costNoTax != null">
        cost_no_tax,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="manufacturerCode != null">
        #{manufacturerCode,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="supplierCode != null">
        #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="dataGranularity != null">
        #{dataGranularity,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="batchNumber != null">
        #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        #{validityDate,jdbcType=VARCHAR},
      </if>
      <if test="produceDate != null">
        #{produceDate,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="quatity != null">
        #{quatity,jdbcType=DECIMAL},
      </if>
      <if test="businessDate != null">
        #{businessDate,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="materialChannel != null">
        #{materialChannel,jdbcType=VARCHAR},
      </if>
      <if test="retailAmount != null">
        #{retailAmount,jdbcType=DECIMAL},
      </if>
      <if test="costTax != null">
        #{costTax,jdbcType=DECIMAL},
      </if>
      <if test="costNoTax != null">
        #{costNoTax,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entityTidb.SrmStoreSalesByManuExample" resultType="java.lang.Long">
    select count(*) from srm_store_sales_by_manu
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update srm_store_sales_by_manu
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.manufacturerCode != null">
        manufacturer_code = #{record.manufacturerCode,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierCode != null">
        supplier_code = #{record.supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="record.supplierName != null">
        supplier_name = #{record.supplierName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.dataGranularity != null">
        data_granularity = #{record.dataGranularity,jdbcType=VARCHAR},
      </if>
      <if test="record.companyCode != null">
        company_code = #{record.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCode != null">
        goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.specifications != null">
        specifications = #{record.specifications,jdbcType=VARCHAR},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=VARCHAR},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNumber != null">
        batch_number = #{record.batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.validityDate != null">
        validity_date = #{record.validityDate,jdbcType=VARCHAR},
      </if>
      <if test="record.produceDate != null">
        produce_date = #{record.produceDate,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseCode != null">
        warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="record.warehouseName != null">
        warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="record.quatity != null">
        quatity = #{record.quatity,jdbcType=DECIMAL},
      </if>
      <if test="record.businessDate != null">
        business_date = #{record.businessDate,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.materialChannel != null">
        material_channel = #{record.materialChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.retailAmount != null">
        retail_amount = #{record.retailAmount,jdbcType=DECIMAL},
      </if>
      <if test="record.costTax != null">
        cost_tax = #{record.costTax,jdbcType=DECIMAL},
      </if>
      <if test="record.costNoTax != null">
        cost_no_tax = #{record.costNoTax,jdbcType=DECIMAL},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update srm_store_sales_by_manu
    set id = #{record.id,jdbcType=INTEGER},
      manufacturer_code = #{record.manufacturerCode,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      supplier_code = #{record.supplierCode,jdbcType=VARCHAR},
      supplier_name = #{record.supplierName,jdbcType=VARCHAR},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      data_granularity = #{record.dataGranularity,jdbcType=VARCHAR},
      company_code = #{record.companyCode,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      specifications = #{record.specifications,jdbcType=VARCHAR},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      unit = #{record.unit,jdbcType=VARCHAR},
      batch_number = #{record.batchNumber,jdbcType=VARCHAR},
      validity_date = #{record.validityDate,jdbcType=VARCHAR},
      produce_date = #{record.produceDate,jdbcType=VARCHAR},
      warehouse_code = #{record.warehouseCode,jdbcType=VARCHAR},
      warehouse_name = #{record.warehouseName,jdbcType=VARCHAR},
      quatity = #{record.quatity,jdbcType=DECIMAL},
      business_date = #{record.businessDate,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      material_channel = #{record.materialChannel,jdbcType=VARCHAR},
      retail_amount = #{record.retailAmount,jdbcType=DECIMAL},
      cost_tax = #{record.costTax,jdbcType=DECIMAL},
      cost_no_tax = #{record.costNoTax,jdbcType=DECIMAL}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entityTidb.SrmStoreSalesByManu">
    update srm_store_sales_by_manu
    <set>
      <if test="manufacturerCode != null">
        manufacturer_code = #{manufacturerCode,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="supplierCode != null">
        supplier_code = #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="dataGranularity != null">
        data_granularity = #{dataGranularity,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="goodsCode != null">
        goods_code = #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="specifications != null">
        specifications = #{specifications,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="batchNumber != null">
        batch_number = #{batchNumber,jdbcType=VARCHAR},
      </if>
      <if test="validityDate != null">
        validity_date = #{validityDate,jdbcType=VARCHAR},
      </if>
      <if test="produceDate != null">
        produce_date = #{produceDate,jdbcType=VARCHAR},
      </if>
      <if test="warehouseCode != null">
        warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      </if>
      <if test="warehouseName != null">
        warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      </if>
      <if test="quatity != null">
        quatity = #{quatity,jdbcType=DECIMAL},
      </if>
      <if test="businessDate != null">
        business_date = #{businessDate,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="materialChannel != null">
        material_channel = #{materialChannel,jdbcType=VARCHAR},
      </if>
      <if test="retailAmount != null">
        retail_amount = #{retailAmount,jdbcType=DECIMAL},
      </if>
      <if test="costTax != null">
        cost_tax = #{costTax,jdbcType=DECIMAL},
      </if>
      <if test="costNoTax != null">
        cost_no_tax = #{costNoTax,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entityTidb.SrmStoreSalesByManu">
    update srm_store_sales_by_manu
    set manufacturer_code = #{manufacturerCode,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      supplier_code = #{supplierCode,jdbcType=VARCHAR},
      supplier_name = #{supplierName,jdbcType=VARCHAR},
      store_code = #{storeCode,jdbcType=VARCHAR},
      store_name = #{storeName,jdbcType=VARCHAR},
      data_granularity = #{dataGranularity,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      goods_code = #{goodsCode,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      specifications = #{specifications,jdbcType=VARCHAR},
      bar_code = #{barCode,jdbcType=VARCHAR},
      unit = #{unit,jdbcType=VARCHAR},
      batch_number = #{batchNumber,jdbcType=VARCHAR},
      validity_date = #{validityDate,jdbcType=VARCHAR},
      produce_date = #{produceDate,jdbcType=VARCHAR},
      warehouse_code = #{warehouseCode,jdbcType=VARCHAR},
      warehouse_name = #{warehouseName,jdbcType=VARCHAR},
      quatity = #{quatity,jdbcType=DECIMAL},
      business_date = #{businessDate,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      material_channel = #{materialChannel,jdbcType=VARCHAR},
      retail_amount = #{retailAmount,jdbcType=DECIMAL},
      cost_tax = #{costTax,jdbcType=DECIMAL},
      cost_no_tax = #{costNoTax,jdbcType=DECIMAL}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>
