<!--<?xml version="1.0" encoding="UTF-8"?>-->
<!--<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">-->
<!--<mapper namespace="com.cowell.purchase.mapperTidb.extend.MdmStoreBaseExtendMapper">-->
<!--  <resultMap id="BaseResultMap" type="com.cowell.purchase.entityTidb.MdmStoreBase">-->
<!--    <id column="id" jdbcType="BIGINT" property="id" />-->
<!--    <result column="business_id" jdbcType="BIGINT" property="businessId" />-->
<!--    <result column="store_id" jdbcType="BIGINT" property="storeId" />-->
<!--    <result column="store_name" jdbcType="VARCHAR" property="storeName" />-->
<!--    <result column="com_id" jdbcType="VARCHAR" property="comId" />-->
<!--    <result column="store_no" jdbcType="VARCHAR" property="storeNo" />-->
<!--  </resultMap>-->
<!--  <sql id="Base_Column_List">-->
<!--    id, business_id, store_id, store_name com_id, store_no-->
<!--  </sql>-->
<!--    <select id="selectByComIds" resultMap="BaseResultMap">-->
<!--        select id, business_id, store_id, store_name, com_id, store_no-->
<!--        from mdm_store_base-->
<!--        where com_id in-->
<!--        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">-->
<!--            #{item,jdbcType=VARCHAR}-->
<!--        </foreach>-->
<!--    </select>-->

<!--    <select id="getSopStoreCodeList" resultType="java.lang.String">-->
<!--        select store_no-->
<!--        from mdm_store_base-->
<!--        where com_id in-->
<!--        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">-->
<!--            #{item,jdbcType=VARCHAR}-->
<!--        </foreach>-->
<!--    </select>-->

<!--    <select id="getStoreIdList" resultType="java.lang.Long">-->
<!--        select store_id-->
<!--        from mdm_store_base-->
<!--        where com_id in-->
<!--        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">-->
<!--            #{item,jdbcType=VARCHAR}-->
<!--        </foreach>-->
<!--    </select>-->

<!--    <select id="getStoreIdListByNo" resultType="java.lang.Long">-->
<!--        select DISTINCT store_id-->
<!--        from mdm_store_base-->
<!--        where store_no in-->
<!--        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">-->
<!--            #{item,jdbcType=VARCHAR}-->
<!--        </foreach>-->
<!--    </select>-->

<!--</mapper>-->
