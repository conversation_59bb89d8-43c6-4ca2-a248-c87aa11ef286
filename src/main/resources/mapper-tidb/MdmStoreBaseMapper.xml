<!--<?xml version="1.0" encoding="UTF-8"?>-->
<!--<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">-->
<!--<mapper namespace="com.cowell.purchase.mapperTidb.MdmStoreBaseMapper">-->
<!--  <resultMap id="BaseResultMap" type="com.cowell.purchase.entityTidb.MdmStoreBase">-->
<!--    <id column="id" jdbcType="BIGINT" property="id" />-->
<!--    <result column="business_id" jdbcType="BIGINT" property="businessId" />-->
<!--    <result column="store_id" jdbcType="BIGINT" property="storeId" />-->
<!--    <result column="store_name" jdbcType="VARCHAR" property="storeName" />-->
<!--    <result column="com_id" jdbcType="VARCHAR" property="comId" />-->
<!--    <result column="store_no" jdbcType="VARCHAR" property="storeNo" />-->
<!--    <result column="ref_store_no" jdbcType="VARCHAR" property="refStoreNo" />-->
<!--    <result column="store_attr" jdbcType="VARCHAR" property="storeAttr" />-->
<!--    <result column="store_inner_no" jdbcType="VARCHAR" property="storeInnerNo" />-->
<!--    <result column="store_shot_name" jdbcType="VARCHAR" property="storeShotName" />-->
<!--    <result column="old_erp_no" jdbcType="VARCHAR" property="oldErpNo" />-->
<!--    <result column="op_code" jdbcType="VARCHAR" property="opCode" />-->
<!--    <result column="order_org" jdbcType="VARCHAR" property="orderOrg" />-->
<!--    <result column="sales_org" jdbcType="VARCHAR" property="salesOrg" />-->
<!--    <result column="sales_channel" jdbcType="VARCHAR" property="salesChannel" />-->
<!--    <result column="dept" jdbcType="VARCHAR" property="dept" />-->
<!--    <result column="lease_area" jdbcType="VARCHAR" property="leaseArea" />-->
<!--    <result column="outer_rent_area" jdbcType="VARCHAR" property="outerRentArea" />-->
<!--    <result column="operating_area" jdbcType="VARCHAR" property="operatingArea" />-->
<!--    <result column="tel" jdbcType="VARCHAR" property="tel" />-->
<!--    <result column="fax" jdbcType="VARCHAR" property="fax" />-->
<!--    <result column="contact" jdbcType="VARCHAR" property="contact" />-->
<!--    <result column="org_name" jdbcType="VARCHAR" property="orgName" />-->
<!--    <result column="business_time" jdbcType="VARCHAR" property="businessTime" />-->
<!--    <result column="open_date" jdbcType="VARCHAR" property="openDate" />-->
<!--    <result column="delivery_date" jdbcType="VARCHAR" property="deliveryDate" />-->
<!--    <result column="close_date" jdbcType="VARCHAR" property="closeDate" />-->
<!--    <result column="last_renovation_date" jdbcType="VARCHAR" property="lastRenovationDate" />-->
<!--    <result column="annual_rent" jdbcType="VARCHAR" property="annualRent" />-->
<!--    <result column="store_status" jdbcType="VARCHAR" property="storeStatus" />-->
<!--    <result column="org_people_num" jdbcType="VARCHAR" property="orgPeopleNum" />-->
<!--    <result column="calendar" jdbcType="VARCHAR" property="calendar" />-->
<!--    <result column="system_type" jdbcType="VARCHAR" property="systemType" />-->
<!--    <result column="province" jdbcType="VARCHAR" property="province" />-->
<!--    <result column="city" jdbcType="VARCHAR" property="city" />-->
<!--    <result column="area" jdbcType="VARCHAR" property="area" />-->
<!--    <result column="address" jdbcType="VARCHAR" property="address" />-->
<!--    <result column="region" jdbcType="VARCHAR" property="region" />-->
<!--    <result column="trading_area" jdbcType="VARCHAR" property="tradingArea" />-->
<!--    <result column="operation_type" jdbcType="VARCHAR" property="operationType" />-->
<!--    <result column="operation_area_sort" jdbcType="VARCHAR" property="operationAreaSort" />-->
<!--    <result column="sales_level" jdbcType="VARCHAR" property="salesLevel" />-->
<!--    <result column="insurance_sort" jdbcType="VARCHAR" property="insuranceSort" />-->
<!--    <result column="custome_rage" jdbcType="VARCHAR" property="customeRage" />-->
<!--    <result column="status" jdbcType="TINYINT" property="status" />-->
<!--    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />-->
<!--    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />-->
<!--    <result column="version" jdbcType="TINYINT" property="version" />-->
<!--    <result column="extend" jdbcType="VARCHAR" property="extend" />-->
<!--  </resultMap>-->
<!--  <sql id="Example_Where_Clause">-->
<!--    <where>-->
<!--      <foreach collection="oredCriteria" item="criteria" separator="or">-->
<!--        <if test="criteria.valid">-->
<!--          <trim prefix="(" prefixOverrides="and" suffix=")">-->
<!--            <foreach collection="criteria.criteria" item="criterion">-->
<!--              <choose>-->
<!--                <when test="criterion.noValue">-->
<!--                  and ${criterion.condition}-->
<!--                </when>-->
<!--                <when test="criterion.singleValue">-->
<!--                  and ${criterion.condition} #{criterion.value}-->
<!--                </when>-->
<!--                <when test="criterion.betweenValue">-->
<!--                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}-->
<!--                </when>-->
<!--                <when test="criterion.listValue">-->
<!--                  and ${criterion.condition}-->
<!--                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">-->
<!--                    #{listItem}-->
<!--                  </foreach>-->
<!--                </when>-->
<!--              </choose>-->
<!--            </foreach>-->
<!--          </trim>-->
<!--        </if>-->
<!--      </foreach>-->
<!--    </where>-->
<!--  </sql>-->
<!--  <sql id="Update_By_Example_Where_Clause">-->
<!--    <where>-->
<!--      <foreach collection="example.oredCriteria" item="criteria" separator="or">-->
<!--        <if test="criteria.valid">-->
<!--          <trim prefix="(" prefixOverrides="and" suffix=")">-->
<!--            <foreach collection="criteria.criteria" item="criterion">-->
<!--              <choose>-->
<!--                <when test="criterion.noValue">-->
<!--                  and ${criterion.condition}-->
<!--                </when>-->
<!--                <when test="criterion.singleValue">-->
<!--                  and ${criterion.condition} #{criterion.value}-->
<!--                </when>-->
<!--                <when test="criterion.betweenValue">-->
<!--                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}-->
<!--                </when>-->
<!--                <when test="criterion.listValue">-->
<!--                  and ${criterion.condition}-->
<!--                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">-->
<!--                    #{listItem}-->
<!--                  </foreach>-->
<!--                </when>-->
<!--              </choose>-->
<!--            </foreach>-->
<!--          </trim>-->
<!--        </if>-->
<!--      </foreach>-->
<!--    </where>-->
<!--  </sql>-->
<!--  <sql id="Base_Column_List">-->
<!--    id, business_id, store_id, store_name, com_id, store_no, ref_store_no, store_attr, -->
<!--    store_inner_no, store_shot_name, old_erp_no, op_code, order_org, sales_org, sales_channel, -->
<!--    dept, lease_area, outer_rent_area, operating_area, tel, fax, contact, org_name, business_time, -->
<!--    open_date, delivery_date, close_date, last_renovation_date, annual_rent, store_status, -->
<!--    org_people_num, calendar, system_type, province, city, area, address, region, trading_area, -->
<!--    operation_type, operation_area_sort, sales_level, insurance_sort, custome_rage, status, -->
<!--    gmt_create, gmt_update, version, extend-->
<!--  </sql>-->
<!--  <select id="selectByExample" parameterType="com.cowell.purchase.entityTidb.MdmStoreBaseExample" resultMap="BaseResultMap">-->
<!--    select-->
<!--    <if test="distinct">-->
<!--      distinct-->
<!--    </if>-->
<!--    <include refid="Base_Column_List" />-->
<!--    from mdm_store_base-->
<!--    <if test="_parameter != null">-->
<!--      <include refid="Example_Where_Clause" />-->
<!--    </if>-->
<!--    <if test="orderByClause != null">-->
<!--      order by ${orderByClause}-->
<!--    </if>-->
<!--    <if test="limit != null">-->
<!--      <if test="offset != null">-->
<!--        limit ${offset}, ${limit}-->
<!--      </if>-->
<!--      <if test="offset == null">-->
<!--        limit ${limit}-->
<!--      </if>-->
<!--    </if>-->
<!--  </select>-->
<!--  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">-->
<!--    select -->
<!--    <include refid="Base_Column_List" />-->
<!--    from mdm_store_base-->
<!--    where id = #{id,jdbcType=BIGINT}-->
<!--  </select>-->
<!--  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">-->
<!--    delete from mdm_store_base-->
<!--    where id = #{id,jdbcType=BIGINT}-->
<!--  </delete>-->
<!--  <delete id="deleteByExample" parameterType="com.cowell.purchase.entityTidb.MdmStoreBaseExample">-->
<!--    delete from mdm_store_base-->
<!--    <if test="_parameter != null">-->
<!--      <include refid="Example_Where_Clause" />-->
<!--    </if>-->
<!--  </delete>-->
<!--  <insert id="insert" parameterType="com.cowell.purchase.entityTidb.MdmStoreBase">-->
<!--    insert into mdm_store_base (id, business_id, store_id, -->
<!--      store_name, com_id, store_no, -->
<!--      ref_store_no, store_attr, store_inner_no, -->
<!--      store_shot_name, old_erp_no, op_code, -->
<!--      order_org, sales_org, sales_channel, -->
<!--      dept, lease_area, outer_rent_area, -->
<!--      operating_area, tel, fax, -->
<!--      contact, org_name, business_time, -->
<!--      open_date, delivery_date, close_date, -->
<!--      last_renovation_date, annual_rent, store_status, -->
<!--      org_people_num, calendar, system_type, -->
<!--      province, city, area, -->
<!--      address, region, trading_area, -->
<!--      operation_type, operation_area_sort, sales_level, -->
<!--      insurance_sort, custome_rage, status, -->
<!--      gmt_create, gmt_update, version, -->
<!--      extend)-->
<!--    values (#{id,jdbcType=BIGINT}, #{businessId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, -->
<!--      #{storeName,jdbcType=VARCHAR}, #{comId,jdbcType=VARCHAR}, #{storeNo,jdbcType=VARCHAR}, -->
<!--      #{refStoreNo,jdbcType=VARCHAR}, #{storeAttr,jdbcType=VARCHAR}, #{storeInnerNo,jdbcType=VARCHAR}, -->
<!--      #{storeShotName,jdbcType=VARCHAR}, #{oldErpNo,jdbcType=VARCHAR}, #{opCode,jdbcType=VARCHAR}, -->
<!--      #{orderOrg,jdbcType=VARCHAR}, #{salesOrg,jdbcType=VARCHAR}, #{salesChannel,jdbcType=VARCHAR}, -->
<!--      #{dept,jdbcType=VARCHAR}, #{leaseArea,jdbcType=VARCHAR}, #{outerRentArea,jdbcType=VARCHAR}, -->
<!--      #{operatingArea,jdbcType=VARCHAR}, #{tel,jdbcType=VARCHAR}, #{fax,jdbcType=VARCHAR}, -->
<!--      #{contact,jdbcType=VARCHAR}, #{orgName,jdbcType=VARCHAR}, #{businessTime,jdbcType=VARCHAR}, -->
<!--      #{openDate,jdbcType=VARCHAR}, #{deliveryDate,jdbcType=VARCHAR}, #{closeDate,jdbcType=VARCHAR}, -->
<!--      #{lastRenovationDate,jdbcType=VARCHAR}, #{annualRent,jdbcType=VARCHAR}, #{storeStatus,jdbcType=VARCHAR}, -->
<!--      #{orgPeopleNum,jdbcType=VARCHAR}, #{calendar,jdbcType=VARCHAR}, #{systemType,jdbcType=VARCHAR}, -->
<!--      #{province,jdbcType=VARCHAR}, #{city,jdbcType=VARCHAR}, #{area,jdbcType=VARCHAR}, -->
<!--      #{address,jdbcType=VARCHAR}, #{region,jdbcType=VARCHAR}, #{tradingArea,jdbcType=VARCHAR}, -->
<!--      #{operationType,jdbcType=VARCHAR}, #{operationAreaSort,jdbcType=VARCHAR}, #{salesLevel,jdbcType=VARCHAR}, -->
<!--      #{insuranceSort,jdbcType=VARCHAR}, #{customeRage,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, -->
<!--      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{version,jdbcType=TINYINT}, -->
<!--      #{extend,jdbcType=VARCHAR})-->
<!--  </insert>-->
<!--  <insert id="insertSelective" parameterType="com.cowell.purchase.entityTidb.MdmStoreBase">-->
<!--    insert into mdm_store_base-->
<!--    <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        id,-->
<!--      </if>-->
<!--      <if test="businessId != null">-->
<!--        business_id,-->
<!--      </if>-->
<!--      <if test="storeId != null">-->
<!--        store_id,-->
<!--      </if>-->
<!--      <if test="storeName != null">-->
<!--        store_name,-->
<!--      </if>-->
<!--      <if test="comId != null">-->
<!--        com_id,-->
<!--      </if>-->
<!--      <if test="storeNo != null">-->
<!--        store_no,-->
<!--      </if>-->
<!--      <if test="refStoreNo != null">-->
<!--        ref_store_no,-->
<!--      </if>-->
<!--      <if test="storeAttr != null">-->
<!--        store_attr,-->
<!--      </if>-->
<!--      <if test="storeInnerNo != null">-->
<!--        store_inner_no,-->
<!--      </if>-->
<!--      <if test="storeShotName != null">-->
<!--        store_shot_name,-->
<!--      </if>-->
<!--      <if test="oldErpNo != null">-->
<!--        old_erp_no,-->
<!--      </if>-->
<!--      <if test="opCode != null">-->
<!--        op_code,-->
<!--      </if>-->
<!--      <if test="orderOrg != null">-->
<!--        order_org,-->
<!--      </if>-->
<!--      <if test="salesOrg != null">-->
<!--        sales_org,-->
<!--      </if>-->
<!--      <if test="salesChannel != null">-->
<!--        sales_channel,-->
<!--      </if>-->
<!--      <if test="dept != null">-->
<!--        dept,-->
<!--      </if>-->
<!--      <if test="leaseArea != null">-->
<!--        lease_area,-->
<!--      </if>-->
<!--      <if test="outerRentArea != null">-->
<!--        outer_rent_area,-->
<!--      </if>-->
<!--      <if test="operatingArea != null">-->
<!--        operating_area,-->
<!--      </if>-->
<!--      <if test="tel != null">-->
<!--        tel,-->
<!--      </if>-->
<!--      <if test="fax != null">-->
<!--        fax,-->
<!--      </if>-->
<!--      <if test="contact != null">-->
<!--        contact,-->
<!--      </if>-->
<!--      <if test="orgName != null">-->
<!--        org_name,-->
<!--      </if>-->
<!--      <if test="businessTime != null">-->
<!--        business_time,-->
<!--      </if>-->
<!--      <if test="openDate != null">-->
<!--        open_date,-->
<!--      </if>-->
<!--      <if test="deliveryDate != null">-->
<!--        delivery_date,-->
<!--      </if>-->
<!--      <if test="closeDate != null">-->
<!--        close_date,-->
<!--      </if>-->
<!--      <if test="lastRenovationDate != null">-->
<!--        last_renovation_date,-->
<!--      </if>-->
<!--      <if test="annualRent != null">-->
<!--        annual_rent,-->
<!--      </if>-->
<!--      <if test="storeStatus != null">-->
<!--        store_status,-->
<!--      </if>-->
<!--      <if test="orgPeopleNum != null">-->
<!--        org_people_num,-->
<!--      </if>-->
<!--      <if test="calendar != null">-->
<!--        calendar,-->
<!--      </if>-->
<!--      <if test="systemType != null">-->
<!--        system_type,-->
<!--      </if>-->
<!--      <if test="province != null">-->
<!--        province,-->
<!--      </if>-->
<!--      <if test="city != null">-->
<!--        city,-->
<!--      </if>-->
<!--      <if test="area != null">-->
<!--        area,-->
<!--      </if>-->
<!--      <if test="address != null">-->
<!--        address,-->
<!--      </if>-->
<!--      <if test="region != null">-->
<!--        region,-->
<!--      </if>-->
<!--      <if test="tradingArea != null">-->
<!--        trading_area,-->
<!--      </if>-->
<!--      <if test="operationType != null">-->
<!--        operation_type,-->
<!--      </if>-->
<!--      <if test="operationAreaSort != null">-->
<!--        operation_area_sort,-->
<!--      </if>-->
<!--      <if test="salesLevel != null">-->
<!--        sales_level,-->
<!--      </if>-->
<!--      <if test="insuranceSort != null">-->
<!--        insurance_sort,-->
<!--      </if>-->
<!--      <if test="customeRage != null">-->
<!--        custome_rage,-->
<!--      </if>-->
<!--      <if test="status != null">-->
<!--        status,-->
<!--      </if>-->
<!--      <if test="gmtCreate != null">-->
<!--        gmt_create,-->
<!--      </if>-->
<!--      <if test="gmtUpdate != null">-->
<!--        gmt_update,-->
<!--      </if>-->
<!--      <if test="version != null">-->
<!--        version,-->
<!--      </if>-->
<!--      <if test="extend != null">-->
<!--        extend,-->
<!--      </if>-->
<!--    </trim>-->
<!--    <trim prefix="values (" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        #{id,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="businessId != null">-->
<!--        #{businessId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="storeId != null">-->
<!--        #{storeId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="storeName != null">-->
<!--        #{storeName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="comId != null">-->
<!--        #{comId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="storeNo != null">-->
<!--        #{storeNo,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="refStoreNo != null">-->
<!--        #{refStoreNo,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="storeAttr != null">-->
<!--        #{storeAttr,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="storeInnerNo != null">-->
<!--        #{storeInnerNo,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="storeShotName != null">-->
<!--        #{storeShotName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="oldErpNo != null">-->
<!--        #{oldErpNo,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="opCode != null">-->
<!--        #{opCode,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="orderOrg != null">-->
<!--        #{orderOrg,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="salesOrg != null">-->
<!--        #{salesOrg,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="salesChannel != null">-->
<!--        #{salesChannel,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="dept != null">-->
<!--        #{dept,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="leaseArea != null">-->
<!--        #{leaseArea,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="outerRentArea != null">-->
<!--        #{outerRentArea,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="operatingArea != null">-->
<!--        #{operatingArea,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="tel != null">-->
<!--        #{tel,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="fax != null">-->
<!--        #{fax,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="contact != null">-->
<!--        #{contact,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="orgName != null">-->
<!--        #{orgName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="businessTime != null">-->
<!--        #{businessTime,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="openDate != null">-->
<!--        #{openDate,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="deliveryDate != null">-->
<!--        #{deliveryDate,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="closeDate != null">-->
<!--        #{closeDate,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="lastRenovationDate != null">-->
<!--        #{lastRenovationDate,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="annualRent != null">-->
<!--        #{annualRent,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="storeStatus != null">-->
<!--        #{storeStatus,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="orgPeopleNum != null">-->
<!--        #{orgPeopleNum,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="calendar != null">-->
<!--        #{calendar,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="systemType != null">-->
<!--        #{systemType,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="province != null">-->
<!--        #{province,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="city != null">-->
<!--        #{city,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="area != null">-->
<!--        #{area,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="address != null">-->
<!--        #{address,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="region != null">-->
<!--        #{region,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="tradingArea != null">-->
<!--        #{tradingArea,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="operationType != null">-->
<!--        #{operationType,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="operationAreaSort != null">-->
<!--        #{operationAreaSort,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="salesLevel != null">-->
<!--        #{salesLevel,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="insuranceSort != null">-->
<!--        #{insuranceSort,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="customeRage != null">-->
<!--        #{customeRage,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="status != null">-->
<!--        #{status,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="gmtCreate != null">-->
<!--        #{gmtCreate,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtUpdate != null">-->
<!--        #{gmtUpdate,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="version != null">-->
<!--        #{version,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="extend != null">-->
<!--        #{extend,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--    </trim>-->
<!--  </insert>-->
<!--  <select id="countByExample" parameterType="com.cowell.purchase.entityTidb.MdmStoreBaseExample" resultType="java.lang.Long">-->
<!--    select count(*) from mdm_store_base-->
<!--    <if test="_parameter != null">-->
<!--      <include refid="Example_Where_Clause" />-->
<!--    </if>-->
<!--  </select>-->
<!--  <update id="updateByExampleSelective" parameterType="map">-->
<!--    update mdm_store_base-->
<!--    <set>-->
<!--      <if test="record.id != null">-->
<!--        id = #{record.id,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="record.businessId != null">-->
<!--        business_id = #{record.businessId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="record.storeId != null">-->
<!--        store_id = #{record.storeId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="record.storeName != null">-->
<!--        store_name = #{record.storeName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.comId != null">-->
<!--        com_id = #{record.comId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.storeNo != null">-->
<!--        store_no = #{record.storeNo,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.refStoreNo != null">-->
<!--        ref_store_no = #{record.refStoreNo,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.storeAttr != null">-->
<!--        store_attr = #{record.storeAttr,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.storeInnerNo != null">-->
<!--        store_inner_no = #{record.storeInnerNo,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.storeShotName != null">-->
<!--        store_shot_name = #{record.storeShotName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.oldErpNo != null">-->
<!--        old_erp_no = #{record.oldErpNo,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.opCode != null">-->
<!--        op_code = #{record.opCode,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.orderOrg != null">-->
<!--        order_org = #{record.orderOrg,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.salesOrg != null">-->
<!--        sales_org = #{record.salesOrg,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.salesChannel != null">-->
<!--        sales_channel = #{record.salesChannel,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.dept != null">-->
<!--        dept = #{record.dept,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.leaseArea != null">-->
<!--        lease_area = #{record.leaseArea,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.outerRentArea != null">-->
<!--        outer_rent_area = #{record.outerRentArea,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.operatingArea != null">-->
<!--        operating_area = #{record.operatingArea,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.tel != null">-->
<!--        tel = #{record.tel,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.fax != null">-->
<!--        fax = #{record.fax,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.contact != null">-->
<!--        contact = #{record.contact,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.orgName != null">-->
<!--        org_name = #{record.orgName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.businessTime != null">-->
<!--        business_time = #{record.businessTime,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.openDate != null">-->
<!--        open_date = #{record.openDate,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.deliveryDate != null">-->
<!--        delivery_date = #{record.deliveryDate,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.closeDate != null">-->
<!--        close_date = #{record.closeDate,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.lastRenovationDate != null">-->
<!--        last_renovation_date = #{record.lastRenovationDate,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.annualRent != null">-->
<!--        annual_rent = #{record.annualRent,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.storeStatus != null">-->
<!--        store_status = #{record.storeStatus,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.orgPeopleNum != null">-->
<!--        org_people_num = #{record.orgPeopleNum,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.calendar != null">-->
<!--        calendar = #{record.calendar,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.systemType != null">-->
<!--        system_type = #{record.systemType,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.province != null">-->
<!--        province = #{record.province,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.city != null">-->
<!--        city = #{record.city,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.area != null">-->
<!--        area = #{record.area,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.address != null">-->
<!--        address = #{record.address,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.region != null">-->
<!--        region = #{record.region,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.tradingArea != null">-->
<!--        trading_area = #{record.tradingArea,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.operationType != null">-->
<!--        operation_type = #{record.operationType,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.operationAreaSort != null">-->
<!--        operation_area_sort = #{record.operationAreaSort,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.salesLevel != null">-->
<!--        sales_level = #{record.salesLevel,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.insuranceSort != null">-->
<!--        insurance_sort = #{record.insuranceSort,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.customeRage != null">-->
<!--        custome_rage = #{record.customeRage,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.status != null">-->
<!--        status = #{record.status,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="record.gmtCreate != null">-->
<!--        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="record.gmtUpdate != null">-->
<!--        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="record.version != null">-->
<!--        version = #{record.version,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="record.extend != null">-->
<!--        extend = #{record.extend,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--    </set>-->
<!--    <if test="_parameter != null">-->
<!--      <include refid="Update_By_Example_Where_Clause" />-->
<!--    </if>-->
<!--  </update>-->
<!--  <update id="updateByExample" parameterType="map">-->
<!--    update mdm_store_base-->
<!--    set id = #{record.id,jdbcType=BIGINT},-->
<!--      business_id = #{record.businessId,jdbcType=BIGINT},-->
<!--      store_id = #{record.storeId,jdbcType=BIGINT},-->
<!--      store_name = #{record.storeName,jdbcType=VARCHAR},-->
<!--      com_id = #{record.comId,jdbcType=VARCHAR},-->
<!--      store_no = #{record.storeNo,jdbcType=VARCHAR},-->
<!--      ref_store_no = #{record.refStoreNo,jdbcType=VARCHAR},-->
<!--      store_attr = #{record.storeAttr,jdbcType=VARCHAR},-->
<!--      store_inner_no = #{record.storeInnerNo,jdbcType=VARCHAR},-->
<!--      store_shot_name = #{record.storeShotName,jdbcType=VARCHAR},-->
<!--      old_erp_no = #{record.oldErpNo,jdbcType=VARCHAR},-->
<!--      op_code = #{record.opCode,jdbcType=VARCHAR},-->
<!--      order_org = #{record.orderOrg,jdbcType=VARCHAR},-->
<!--      sales_org = #{record.salesOrg,jdbcType=VARCHAR},-->
<!--      sales_channel = #{record.salesChannel,jdbcType=VARCHAR},-->
<!--      dept = #{record.dept,jdbcType=VARCHAR},-->
<!--      lease_area = #{record.leaseArea,jdbcType=VARCHAR},-->
<!--      outer_rent_area = #{record.outerRentArea,jdbcType=VARCHAR},-->
<!--      operating_area = #{record.operatingArea,jdbcType=VARCHAR},-->
<!--      tel = #{record.tel,jdbcType=VARCHAR},-->
<!--      fax = #{record.fax,jdbcType=VARCHAR},-->
<!--      contact = #{record.contact,jdbcType=VARCHAR},-->
<!--      org_name = #{record.orgName,jdbcType=VARCHAR},-->
<!--      business_time = #{record.businessTime,jdbcType=VARCHAR},-->
<!--      open_date = #{record.openDate,jdbcType=VARCHAR},-->
<!--      delivery_date = #{record.deliveryDate,jdbcType=VARCHAR},-->
<!--      close_date = #{record.closeDate,jdbcType=VARCHAR},-->
<!--      last_renovation_date = #{record.lastRenovationDate,jdbcType=VARCHAR},-->
<!--      annual_rent = #{record.annualRent,jdbcType=VARCHAR},-->
<!--      store_status = #{record.storeStatus,jdbcType=VARCHAR},-->
<!--      org_people_num = #{record.orgPeopleNum,jdbcType=VARCHAR},-->
<!--      calendar = #{record.calendar,jdbcType=VARCHAR},-->
<!--      system_type = #{record.systemType,jdbcType=VARCHAR},-->
<!--      province = #{record.province,jdbcType=VARCHAR},-->
<!--      city = #{record.city,jdbcType=VARCHAR},-->
<!--      area = #{record.area,jdbcType=VARCHAR},-->
<!--      address = #{record.address,jdbcType=VARCHAR},-->
<!--      region = #{record.region,jdbcType=VARCHAR},-->
<!--      trading_area = #{record.tradingArea,jdbcType=VARCHAR},-->
<!--      operation_type = #{record.operationType,jdbcType=VARCHAR},-->
<!--      operation_area_sort = #{record.operationAreaSort,jdbcType=VARCHAR},-->
<!--      sales_level = #{record.salesLevel,jdbcType=VARCHAR},-->
<!--      insurance_sort = #{record.insuranceSort,jdbcType=VARCHAR},-->
<!--      custome_rage = #{record.customeRage,jdbcType=VARCHAR},-->
<!--      status = #{record.status,jdbcType=TINYINT},-->
<!--      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},-->
<!--      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},-->
<!--      version = #{record.version,jdbcType=TINYINT},-->
<!--      extend = #{record.extend,jdbcType=VARCHAR}-->
<!--    <if test="_parameter != null">-->
<!--      <include refid="Update_By_Example_Where_Clause" />-->
<!--    </if>-->
<!--  </update>-->
<!--  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entityTidb.MdmStoreBase">-->
<!--    update mdm_store_base-->
<!--    <set>-->
<!--      <if test="businessId != null">-->
<!--        business_id = #{businessId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="storeId != null">-->
<!--        store_id = #{storeId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="storeName != null">-->
<!--        store_name = #{storeName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="comId != null">-->
<!--        com_id = #{comId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="storeNo != null">-->
<!--        store_no = #{storeNo,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="refStoreNo != null">-->
<!--        ref_store_no = #{refStoreNo,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="storeAttr != null">-->
<!--        store_attr = #{storeAttr,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="storeInnerNo != null">-->
<!--        store_inner_no = #{storeInnerNo,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="storeShotName != null">-->
<!--        store_shot_name = #{storeShotName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="oldErpNo != null">-->
<!--        old_erp_no = #{oldErpNo,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="opCode != null">-->
<!--        op_code = #{opCode,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="orderOrg != null">-->
<!--        order_org = #{orderOrg,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="salesOrg != null">-->
<!--        sales_org = #{salesOrg,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="salesChannel != null">-->
<!--        sales_channel = #{salesChannel,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="dept != null">-->
<!--        dept = #{dept,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="leaseArea != null">-->
<!--        lease_area = #{leaseArea,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="outerRentArea != null">-->
<!--        outer_rent_area = #{outerRentArea,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="operatingArea != null">-->
<!--        operating_area = #{operatingArea,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="tel != null">-->
<!--        tel = #{tel,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="fax != null">-->
<!--        fax = #{fax,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="contact != null">-->
<!--        contact = #{contact,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="orgName != null">-->
<!--        org_name = #{orgName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="businessTime != null">-->
<!--        business_time = #{businessTime,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="openDate != null">-->
<!--        open_date = #{openDate,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="deliveryDate != null">-->
<!--        delivery_date = #{deliveryDate,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="closeDate != null">-->
<!--        close_date = #{closeDate,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="lastRenovationDate != null">-->
<!--        last_renovation_date = #{lastRenovationDate,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="annualRent != null">-->
<!--        annual_rent = #{annualRent,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="storeStatus != null">-->
<!--        store_status = #{storeStatus,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="orgPeopleNum != null">-->
<!--        org_people_num = #{orgPeopleNum,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="calendar != null">-->
<!--        calendar = #{calendar,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="systemType != null">-->
<!--        system_type = #{systemType,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="province != null">-->
<!--        province = #{province,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="city != null">-->
<!--        city = #{city,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="area != null">-->
<!--        area = #{area,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="address != null">-->
<!--        address = #{address,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="region != null">-->
<!--        region = #{region,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="tradingArea != null">-->
<!--        trading_area = #{tradingArea,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="operationType != null">-->
<!--        operation_type = #{operationType,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="operationAreaSort != null">-->
<!--        operation_area_sort = #{operationAreaSort,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="salesLevel != null">-->
<!--        sales_level = #{salesLevel,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="insuranceSort != null">-->
<!--        insurance_sort = #{insuranceSort,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="customeRage != null">-->
<!--        custome_rage = #{customeRage,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="status != null">-->
<!--        status = #{status,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="gmtCreate != null">-->
<!--        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtUpdate != null">-->
<!--        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="version != null">-->
<!--        version = #{version,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="extend != null">-->
<!--        extend = #{extend,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--    </set>-->
<!--    where id = #{id,jdbcType=BIGINT}-->
<!--  </update>-->
<!--  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entityTidb.MdmStoreBase">-->
<!--    update mdm_store_base-->
<!--    set business_id = #{businessId,jdbcType=BIGINT},-->
<!--      store_id = #{storeId,jdbcType=BIGINT},-->
<!--      store_name = #{storeName,jdbcType=VARCHAR},-->
<!--      com_id = #{comId,jdbcType=VARCHAR},-->
<!--      store_no = #{storeNo,jdbcType=VARCHAR},-->
<!--      ref_store_no = #{refStoreNo,jdbcType=VARCHAR},-->
<!--      store_attr = #{storeAttr,jdbcType=VARCHAR},-->
<!--      store_inner_no = #{storeInnerNo,jdbcType=VARCHAR},-->
<!--      store_shot_name = #{storeShotName,jdbcType=VARCHAR},-->
<!--      old_erp_no = #{oldErpNo,jdbcType=VARCHAR},-->
<!--      op_code = #{opCode,jdbcType=VARCHAR},-->
<!--      order_org = #{orderOrg,jdbcType=VARCHAR},-->
<!--      sales_org = #{salesOrg,jdbcType=VARCHAR},-->
<!--      sales_channel = #{salesChannel,jdbcType=VARCHAR},-->
<!--      dept = #{dept,jdbcType=VARCHAR},-->
<!--      lease_area = #{leaseArea,jdbcType=VARCHAR},-->
<!--      outer_rent_area = #{outerRentArea,jdbcType=VARCHAR},-->
<!--      operating_area = #{operatingArea,jdbcType=VARCHAR},-->
<!--      tel = #{tel,jdbcType=VARCHAR},-->
<!--      fax = #{fax,jdbcType=VARCHAR},-->
<!--      contact = #{contact,jdbcType=VARCHAR},-->
<!--      org_name = #{orgName,jdbcType=VARCHAR},-->
<!--      business_time = #{businessTime,jdbcType=VARCHAR},-->
<!--      open_date = #{openDate,jdbcType=VARCHAR},-->
<!--      delivery_date = #{deliveryDate,jdbcType=VARCHAR},-->
<!--      close_date = #{closeDate,jdbcType=VARCHAR},-->
<!--      last_renovation_date = #{lastRenovationDate,jdbcType=VARCHAR},-->
<!--      annual_rent = #{annualRent,jdbcType=VARCHAR},-->
<!--      store_status = #{storeStatus,jdbcType=VARCHAR},-->
<!--      org_people_num = #{orgPeopleNum,jdbcType=VARCHAR},-->
<!--      calendar = #{calendar,jdbcType=VARCHAR},-->
<!--      system_type = #{systemType,jdbcType=VARCHAR},-->
<!--      province = #{province,jdbcType=VARCHAR},-->
<!--      city = #{city,jdbcType=VARCHAR},-->
<!--      area = #{area,jdbcType=VARCHAR},-->
<!--      address = #{address,jdbcType=VARCHAR},-->
<!--      region = #{region,jdbcType=VARCHAR},-->
<!--      trading_area = #{tradingArea,jdbcType=VARCHAR},-->
<!--      operation_type = #{operationType,jdbcType=VARCHAR},-->
<!--      operation_area_sort = #{operationAreaSort,jdbcType=VARCHAR},-->
<!--      sales_level = #{salesLevel,jdbcType=VARCHAR},-->
<!--      insurance_sort = #{insuranceSort,jdbcType=VARCHAR},-->
<!--      custome_rage = #{customeRage,jdbcType=VARCHAR},-->
<!--      status = #{status,jdbcType=TINYINT},-->
<!--      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},-->
<!--      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},-->
<!--      version = #{version,jdbcType=TINYINT},-->
<!--      extend = #{extend,jdbcType=VARCHAR}-->
<!--    where id = #{id,jdbcType=BIGINT}-->
<!--  </update>-->
<!--</mapper>-->