<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapperTidb.OrderDetailOtterMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entityTidb.OrderDetailOtter">
    <id column="detail_id" jdbcType="BIGINT" property="detailId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="sku_snap_id" jdbcType="BIGINT" property="skuSnapId" />
    <result column="sku_id" jdbcType="BIGINT" property="skuId" />
    <result column="sku_name" jdbcType="VARCHAR" property="skuName" />
    <result column="specifications_name" jdbcType="VARCHAR" property="specificationsName" />
    <result column="sku_image" jdbcType="VARCHAR" property="skuImage" />
    <result column="sku_price" jdbcType="INTEGER" property="skuPrice" />
    <result column="coupon_amount" jdbcType="INTEGER" property="couponAmount" />
    <result column="settle_price" jdbcType="INTEGER" property="settlePrice" />
    <result column="sku_count" jdbcType="INTEGER" property="skuCount" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="points" jdbcType="INTEGER" property="points" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="item_id" jdbcType="BIGINT" property="itemId" />
    <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
    <result column="promotion_price" jdbcType="INTEGER" property="promotionPrice" />
    <result column="member_price" jdbcType="INTEGER" property="memberPrice" />
    <result column="member_plus_price" jdbcType="INTEGER" property="memberPlusPrice" />
    <result column="display_price" jdbcType="INTEGER" property="displayPrice" />
    <result column="display_price_type" jdbcType="INTEGER" property="displayPriceType" />
    <result column="member_plus_discounts" jdbcType="INTEGER" property="memberPlusDiscounts" />
    <result column="promotion_discounts" jdbcType="INTEGER" property="promotionDiscounts" />
    <result column="dismantled" jdbcType="INTEGER" property="dismantled" />
    <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="promotion_gift" jdbcType="INTEGER" property="promotionGift" />
    <result column="batch_code" jdbcType="VARCHAR" property="batchCode" />
    <result column="salesman" jdbcType="VARCHAR" property="salesman" />
    <result column="salesman_id" jdbcType="VARCHAR" property="salesmanId" />
    <result column="item_code" jdbcType="VARCHAR" property="itemCode" />
    <result column="sku_label" jdbcType="VARCHAR" property="skuLabel" />
    <result column="authorizer" jdbcType="VARCHAR" property="authorizer" />
    <result column="authorizer_id" jdbcType="BIGINT" property="authorizerId" />
    <result column="points_times" jdbcType="INTEGER" property="pointsTimes" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="order_status_cur" jdbcType="INTEGER" property="orderStatusCur" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="cashier" jdbcType="VARCHAR" property="cashier" />
    <result column="cashier_id" jdbcType="BIGINT" property="cashierId" />
    <result column="user_card_num" jdbcType="VARCHAR" property="userCardNum" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="user_phone" jdbcType="VARCHAR" property="userPhone" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="mnemonic_code" jdbcType="VARCHAR" property="mnemonicCode" />
    <result column="dosage_forms" jdbcType="VARCHAR" property="dosageForms" />
    <result column="expire_date" jdbcType="VARCHAR" property="expireDate" />
    <result column="producter" jdbcType="VARCHAR" property="producter" />
    <result column="promotion_share_total" jdbcType="INTEGER" property="promotionShareTotal" />
    <result column="prodarea" jdbcType="VARCHAR" property="prodarea" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="market_cate_ids" jdbcType="VARCHAR" property="marketCateIds" />
    <result column="market_cate_names" jdbcType="VARCHAR" property="marketCateNames" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.cowell.purchase.entityTidb.OrderDetailOtter">
    <result column="promotion_info" jdbcType="LONGVARCHAR" property="promotionInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    detail_id, order_id, sku_snap_id, sku_id, sku_name, specifications_name, sku_image, 
    sku_price, coupon_amount, settle_price, sku_count, gmt_create, gmt_update, status, 
    extend, points, version, item_id, batch_no, promotion_price, member_price, member_plus_price, 
    display_price, display_price_type, member_plus_discounts, promotion_discounts, dismantled, 
    goods_unit, goods_no, promotion_gift, batch_code, salesman, salesman_id, item_code, 
    sku_label, authorizer, authorizer_id, points_times, business_id, store_id, order_status_cur, 
    store_name, cashier, cashier_id, user_card_num, username, user_phone, name, bar_code, 
    mnemonic_code, dosage_forms, expire_date, producter, promotion_share_total, prodarea, 
    type, market_cate_ids, market_cate_names, brand
  </sql>
  <sql id="Blob_Column_List">
    promotion_info
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.cowell.purchase.entityTidb.OrderDetailOtterExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from order_detail_otter
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.cowell.purchase.entityTidb.OrderDetailOtterExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from order_detail_otter
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from order_detail_otter
    where detail_id = #{detailId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_detail_otter
    where detail_id = #{detailId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entityTidb.OrderDetailOtterExample">
    delete from order_detail_otter
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entityTidb.OrderDetailOtter">
    insert into order_detail_otter (detail_id, order_id, sku_snap_id, 
      sku_id, sku_name, specifications_name, 
      sku_image, sku_price, coupon_amount, 
      settle_price, sku_count, gmt_create, 
      gmt_update, status, extend, 
      points, version, item_id, 
      batch_no, promotion_price, member_price, 
      member_plus_price, display_price, display_price_type, 
      member_plus_discounts, promotion_discounts, 
      dismantled, goods_unit, goods_no, 
      promotion_gift, batch_code, salesman, 
      salesman_id, item_code, sku_label, 
      authorizer, authorizer_id, points_times, 
      business_id, store_id, order_status_cur, 
      store_name, cashier, cashier_id, 
      user_card_num, username, user_phone, 
      name, bar_code, mnemonic_code, 
      dosage_forms, expire_date, producter, 
      promotion_share_total, prodarea, type, 
      market_cate_ids, market_cate_names, brand, 
      promotion_info)
    values (#{detailId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{skuSnapId,jdbcType=BIGINT}, 
      #{skuId,jdbcType=BIGINT}, #{skuName,jdbcType=VARCHAR}, #{specificationsName,jdbcType=VARCHAR}, 
      #{skuImage,jdbcType=VARCHAR}, #{skuPrice,jdbcType=INTEGER}, #{couponAmount,jdbcType=INTEGER}, 
      #{settlePrice,jdbcType=INTEGER}, #{skuCount,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, 
      #{points,jdbcType=INTEGER}, #{version,jdbcType=INTEGER}, #{itemId,jdbcType=BIGINT}, 
      #{batchNo,jdbcType=VARCHAR}, #{promotionPrice,jdbcType=INTEGER}, #{memberPrice,jdbcType=INTEGER}, 
      #{memberPlusPrice,jdbcType=INTEGER}, #{displayPrice,jdbcType=INTEGER}, #{displayPriceType,jdbcType=INTEGER}, 
      #{memberPlusDiscounts,jdbcType=INTEGER}, #{promotionDiscounts,jdbcType=INTEGER}, 
      #{dismantled,jdbcType=INTEGER}, #{goodsUnit,jdbcType=VARCHAR}, #{goodsNo,jdbcType=VARCHAR}, 
      #{promotionGift,jdbcType=INTEGER}, #{batchCode,jdbcType=VARCHAR}, #{salesman,jdbcType=VARCHAR}, 
      #{salesmanId,jdbcType=VARCHAR}, #{itemCode,jdbcType=VARCHAR}, #{skuLabel,jdbcType=VARCHAR}, 
      #{authorizer,jdbcType=VARCHAR}, #{authorizerId,jdbcType=BIGINT}, #{pointsTimes,jdbcType=INTEGER}, 
      #{businessId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{orderStatusCur,jdbcType=INTEGER}, 
      #{storeName,jdbcType=VARCHAR}, #{cashier,jdbcType=VARCHAR}, #{cashierId,jdbcType=BIGINT}, 
      #{userCardNum,jdbcType=VARCHAR}, #{username,jdbcType=VARCHAR}, #{userPhone,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{barCode,jdbcType=VARCHAR}, #{mnemonicCode,jdbcType=VARCHAR}, 
      #{dosageForms,jdbcType=VARCHAR}, #{expireDate,jdbcType=VARCHAR}, #{producter,jdbcType=VARCHAR}, 
      #{promotionShareTotal,jdbcType=INTEGER}, #{prodarea,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, 
      #{marketCateIds,jdbcType=VARCHAR}, #{marketCateNames,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR}, 
      #{promotionInfo,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entityTidb.OrderDetailOtter">
    insert into order_detail_otter
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="detailId != null">
        detail_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="skuSnapId != null">
        sku_snap_id,
      </if>
      <if test="skuId != null">
        sku_id,
      </if>
      <if test="skuName != null">
        sku_name,
      </if>
      <if test="specificationsName != null">
        specifications_name,
      </if>
      <if test="skuImage != null">
        sku_image,
      </if>
      <if test="skuPrice != null">
        sku_price,
      </if>
      <if test="couponAmount != null">
        coupon_amount,
      </if>
      <if test="settlePrice != null">
        settle_price,
      </if>
      <if test="skuCount != null">
        sku_count,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="points != null">
        points,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="itemId != null">
        item_id,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="promotionPrice != null">
        promotion_price,
      </if>
      <if test="memberPrice != null">
        member_price,
      </if>
      <if test="memberPlusPrice != null">
        member_plus_price,
      </if>
      <if test="displayPrice != null">
        display_price,
      </if>
      <if test="displayPriceType != null">
        display_price_type,
      </if>
      <if test="memberPlusDiscounts != null">
        member_plus_discounts,
      </if>
      <if test="promotionDiscounts != null">
        promotion_discounts,
      </if>
      <if test="dismantled != null">
        dismantled,
      </if>
      <if test="goodsUnit != null">
        goods_unit,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="promotionGift != null">
        promotion_gift,
      </if>
      <if test="batchCode != null">
        batch_code,
      </if>
      <if test="salesman != null">
        salesman,
      </if>
      <if test="salesmanId != null">
        salesman_id,
      </if>
      <if test="itemCode != null">
        item_code,
      </if>
      <if test="skuLabel != null">
        sku_label,
      </if>
      <if test="authorizer != null">
        authorizer,
      </if>
      <if test="authorizerId != null">
        authorizer_id,
      </if>
      <if test="pointsTimes != null">
        points_times,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="orderStatusCur != null">
        order_status_cur,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="cashier != null">
        cashier,
      </if>
      <if test="cashierId != null">
        cashier_id,
      </if>
      <if test="userCardNum != null">
        user_card_num,
      </if>
      <if test="username != null">
        username,
      </if>
      <if test="userPhone != null">
        user_phone,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="mnemonicCode != null">
        mnemonic_code,
      </if>
      <if test="dosageForms != null">
        dosage_forms,
      </if>
      <if test="expireDate != null">
        expire_date,
      </if>
      <if test="producter != null">
        producter,
      </if>
      <if test="promotionShareTotal != null">
        promotion_share_total,
      </if>
      <if test="prodarea != null">
        prodarea,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="marketCateIds != null">
        market_cate_ids,
      </if>
      <if test="marketCateNames != null">
        market_cate_names,
      </if>
      <if test="brand != null">
        brand,
      </if>
      <if test="promotionInfo != null">
        promotion_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="detailId != null">
        #{detailId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="skuSnapId != null">
        #{skuSnapId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=BIGINT},
      </if>
      <if test="skuName != null">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="specificationsName != null">
        #{specificationsName,jdbcType=VARCHAR},
      </if>
      <if test="skuImage != null">
        #{skuImage,jdbcType=VARCHAR},
      </if>
      <if test="skuPrice != null">
        #{skuPrice,jdbcType=INTEGER},
      </if>
      <if test="couponAmount != null">
        #{couponAmount,jdbcType=INTEGER},
      </if>
      <if test="settlePrice != null">
        #{settlePrice,jdbcType=INTEGER},
      </if>
      <if test="skuCount != null">
        #{skuCount,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="points != null">
        #{points,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="itemId != null">
        #{itemId,jdbcType=BIGINT},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="promotionPrice != null">
        #{promotionPrice,jdbcType=INTEGER},
      </if>
      <if test="memberPrice != null">
        #{memberPrice,jdbcType=INTEGER},
      </if>
      <if test="memberPlusPrice != null">
        #{memberPlusPrice,jdbcType=INTEGER},
      </if>
      <if test="displayPrice != null">
        #{displayPrice,jdbcType=INTEGER},
      </if>
      <if test="displayPriceType != null">
        #{displayPriceType,jdbcType=INTEGER},
      </if>
      <if test="memberPlusDiscounts != null">
        #{memberPlusDiscounts,jdbcType=INTEGER},
      </if>
      <if test="promotionDiscounts != null">
        #{promotionDiscounts,jdbcType=INTEGER},
      </if>
      <if test="dismantled != null">
        #{dismantled,jdbcType=INTEGER},
      </if>
      <if test="goodsUnit != null">
        #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="promotionGift != null">
        #{promotionGift,jdbcType=INTEGER},
      </if>
      <if test="batchCode != null">
        #{batchCode,jdbcType=VARCHAR},
      </if>
      <if test="salesman != null">
        #{salesman,jdbcType=VARCHAR},
      </if>
      <if test="salesmanId != null">
        #{salesmanId,jdbcType=VARCHAR},
      </if>
      <if test="itemCode != null">
        #{itemCode,jdbcType=VARCHAR},
      </if>
      <if test="skuLabel != null">
        #{skuLabel,jdbcType=VARCHAR},
      </if>
      <if test="authorizer != null">
        #{authorizer,jdbcType=VARCHAR},
      </if>
      <if test="authorizerId != null">
        #{authorizerId,jdbcType=BIGINT},
      </if>
      <if test="pointsTimes != null">
        #{pointsTimes,jdbcType=INTEGER},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="orderStatusCur != null">
        #{orderStatusCur,jdbcType=INTEGER},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="cashier != null">
        #{cashier,jdbcType=VARCHAR},
      </if>
      <if test="cashierId != null">
        #{cashierId,jdbcType=BIGINT},
      </if>
      <if test="userCardNum != null">
        #{userCardNum,jdbcType=VARCHAR},
      </if>
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="userPhone != null">
        #{userPhone,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="mnemonicCode != null">
        #{mnemonicCode,jdbcType=VARCHAR},
      </if>
      <if test="dosageForms != null">
        #{dosageForms,jdbcType=VARCHAR},
      </if>
      <if test="expireDate != null">
        #{expireDate,jdbcType=VARCHAR},
      </if>
      <if test="producter != null">
        #{producter,jdbcType=VARCHAR},
      </if>
      <if test="promotionShareTotal != null">
        #{promotionShareTotal,jdbcType=INTEGER},
      </if>
      <if test="prodarea != null">
        #{prodarea,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="marketCateIds != null">
        #{marketCateIds,jdbcType=VARCHAR},
      </if>
      <if test="marketCateNames != null">
        #{marketCateNames,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="promotionInfo != null">
        #{promotionInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entityTidb.OrderDetailOtterExample" resultType="java.lang.Long">
    select count(*) from order_detail_otter
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update order_detail_otter
    <set>
      <if test="record.detailId != null">
        detail_id = #{record.detailId,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.skuSnapId != null">
        sku_snap_id = #{record.skuSnapId,jdbcType=BIGINT},
      </if>
      <if test="record.skuId != null">
        sku_id = #{record.skuId,jdbcType=BIGINT},
      </if>
      <if test="record.skuName != null">
        sku_name = #{record.skuName,jdbcType=VARCHAR},
      </if>
      <if test="record.specificationsName != null">
        specifications_name = #{record.specificationsName,jdbcType=VARCHAR},
      </if>
      <if test="record.skuImage != null">
        sku_image = #{record.skuImage,jdbcType=VARCHAR},
      </if>
      <if test="record.skuPrice != null">
        sku_price = #{record.skuPrice,jdbcType=INTEGER},
      </if>
      <if test="record.couponAmount != null">
        coupon_amount = #{record.couponAmount,jdbcType=INTEGER},
      </if>
      <if test="record.settlePrice != null">
        settle_price = #{record.settlePrice,jdbcType=INTEGER},
      </if>
      <if test="record.skuCount != null">
        sku_count = #{record.skuCount,jdbcType=INTEGER},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.points != null">
        points = #{record.points,jdbcType=INTEGER},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.itemId != null">
        item_id = #{record.itemId,jdbcType=BIGINT},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=VARCHAR},
      </if>
      <if test="record.promotionPrice != null">
        promotion_price = #{record.promotionPrice,jdbcType=INTEGER},
      </if>
      <if test="record.memberPrice != null">
        member_price = #{record.memberPrice,jdbcType=INTEGER},
      </if>
      <if test="record.memberPlusPrice != null">
        member_plus_price = #{record.memberPlusPrice,jdbcType=INTEGER},
      </if>
      <if test="record.displayPrice != null">
        display_price = #{record.displayPrice,jdbcType=INTEGER},
      </if>
      <if test="record.displayPriceType != null">
        display_price_type = #{record.displayPriceType,jdbcType=INTEGER},
      </if>
      <if test="record.memberPlusDiscounts != null">
        member_plus_discounts = #{record.memberPlusDiscounts,jdbcType=INTEGER},
      </if>
      <if test="record.promotionDiscounts != null">
        promotion_discounts = #{record.promotionDiscounts,jdbcType=INTEGER},
      </if>
      <if test="record.dismantled != null">
        dismantled = #{record.dismantled,jdbcType=INTEGER},
      </if>
      <if test="record.goodsUnit != null">
        goods_unit = #{record.goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.promotionGift != null">
        promotion_gift = #{record.promotionGift,jdbcType=INTEGER},
      </if>
      <if test="record.batchCode != null">
        batch_code = #{record.batchCode,jdbcType=VARCHAR},
      </if>
      <if test="record.salesman != null">
        salesman = #{record.salesman,jdbcType=VARCHAR},
      </if>
      <if test="record.salesmanId != null">
        salesman_id = #{record.salesmanId,jdbcType=VARCHAR},
      </if>
      <if test="record.itemCode != null">
        item_code = #{record.itemCode,jdbcType=VARCHAR},
      </if>
      <if test="record.skuLabel != null">
        sku_label = #{record.skuLabel,jdbcType=VARCHAR},
      </if>
      <if test="record.authorizer != null">
        authorizer = #{record.authorizer,jdbcType=VARCHAR},
      </if>
      <if test="record.authorizerId != null">
        authorizer_id = #{record.authorizerId,jdbcType=BIGINT},
      </if>
      <if test="record.pointsTimes != null">
        points_times = #{record.pointsTimes,jdbcType=INTEGER},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.orderStatusCur != null">
        order_status_cur = #{record.orderStatusCur,jdbcType=INTEGER},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.cashier != null">
        cashier = #{record.cashier,jdbcType=VARCHAR},
      </if>
      <if test="record.cashierId != null">
        cashier_id = #{record.cashierId,jdbcType=BIGINT},
      </if>
      <if test="record.userCardNum != null">
        user_card_num = #{record.userCardNum,jdbcType=VARCHAR},
      </if>
      <if test="record.username != null">
        username = #{record.username,jdbcType=VARCHAR},
      </if>
      <if test="record.userPhone != null">
        user_phone = #{record.userPhone,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=VARCHAR},
      </if>
      <if test="record.mnemonicCode != null">
        mnemonic_code = #{record.mnemonicCode,jdbcType=VARCHAR},
      </if>
      <if test="record.dosageForms != null">
        dosage_forms = #{record.dosageForms,jdbcType=VARCHAR},
      </if>
      <if test="record.expireDate != null">
        expire_date = #{record.expireDate,jdbcType=VARCHAR},
      </if>
      <if test="record.producter != null">
        producter = #{record.producter,jdbcType=VARCHAR},
      </if>
      <if test="record.promotionShareTotal != null">
        promotion_share_total = #{record.promotionShareTotal,jdbcType=INTEGER},
      </if>
      <if test="record.prodarea != null">
        prodarea = #{record.prodarea,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.marketCateIds != null">
        market_cate_ids = #{record.marketCateIds,jdbcType=VARCHAR},
      </if>
      <if test="record.marketCateNames != null">
        market_cate_names = #{record.marketCateNames,jdbcType=VARCHAR},
      </if>
      <if test="record.brand != null">
        brand = #{record.brand,jdbcType=VARCHAR},
      </if>
      <if test="record.promotionInfo != null">
        promotion_info = #{record.promotionInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update order_detail_otter
    set detail_id = #{record.detailId,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      sku_snap_id = #{record.skuSnapId,jdbcType=BIGINT},
      sku_id = #{record.skuId,jdbcType=BIGINT},
      sku_name = #{record.skuName,jdbcType=VARCHAR},
      specifications_name = #{record.specificationsName,jdbcType=VARCHAR},
      sku_image = #{record.skuImage,jdbcType=VARCHAR},
      sku_price = #{record.skuPrice,jdbcType=INTEGER},
      coupon_amount = #{record.couponAmount,jdbcType=INTEGER},
      settle_price = #{record.settlePrice,jdbcType=INTEGER},
      sku_count = #{record.skuCount,jdbcType=INTEGER},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      points = #{record.points,jdbcType=INTEGER},
      version = #{record.version,jdbcType=INTEGER},
      item_id = #{record.itemId,jdbcType=BIGINT},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      promotion_price = #{record.promotionPrice,jdbcType=INTEGER},
      member_price = #{record.memberPrice,jdbcType=INTEGER},
      member_plus_price = #{record.memberPlusPrice,jdbcType=INTEGER},
      display_price = #{record.displayPrice,jdbcType=INTEGER},
      display_price_type = #{record.displayPriceType,jdbcType=INTEGER},
      member_plus_discounts = #{record.memberPlusDiscounts,jdbcType=INTEGER},
      promotion_discounts = #{record.promotionDiscounts,jdbcType=INTEGER},
      dismantled = #{record.dismantled,jdbcType=INTEGER},
      goods_unit = #{record.goodsUnit,jdbcType=VARCHAR},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      promotion_gift = #{record.promotionGift,jdbcType=INTEGER},
      batch_code = #{record.batchCode,jdbcType=VARCHAR},
      salesman = #{record.salesman,jdbcType=VARCHAR},
      salesman_id = #{record.salesmanId,jdbcType=VARCHAR},
      item_code = #{record.itemCode,jdbcType=VARCHAR},
      sku_label = #{record.skuLabel,jdbcType=VARCHAR},
      authorizer = #{record.authorizer,jdbcType=VARCHAR},
      authorizer_id = #{record.authorizerId,jdbcType=BIGINT},
      points_times = #{record.pointsTimes,jdbcType=INTEGER},
      business_id = #{record.businessId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      order_status_cur = #{record.orderStatusCur,jdbcType=INTEGER},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      cashier = #{record.cashier,jdbcType=VARCHAR},
      cashier_id = #{record.cashierId,jdbcType=BIGINT},
      user_card_num = #{record.userCardNum,jdbcType=VARCHAR},
      username = #{record.username,jdbcType=VARCHAR},
      user_phone = #{record.userPhone,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      mnemonic_code = #{record.mnemonicCode,jdbcType=VARCHAR},
      dosage_forms = #{record.dosageForms,jdbcType=VARCHAR},
      expire_date = #{record.expireDate,jdbcType=VARCHAR},
      producter = #{record.producter,jdbcType=VARCHAR},
      promotion_share_total = #{record.promotionShareTotal,jdbcType=INTEGER},
      prodarea = #{record.prodarea,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=INTEGER},
      market_cate_ids = #{record.marketCateIds,jdbcType=VARCHAR},
      market_cate_names = #{record.marketCateNames,jdbcType=VARCHAR},
      brand = #{record.brand,jdbcType=VARCHAR},
      promotion_info = #{record.promotionInfo,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update order_detail_otter
    set detail_id = #{record.detailId,jdbcType=BIGINT},
      order_id = #{record.orderId,jdbcType=BIGINT},
      sku_snap_id = #{record.skuSnapId,jdbcType=BIGINT},
      sku_id = #{record.skuId,jdbcType=BIGINT},
      sku_name = #{record.skuName,jdbcType=VARCHAR},
      specifications_name = #{record.specificationsName,jdbcType=VARCHAR},
      sku_image = #{record.skuImage,jdbcType=VARCHAR},
      sku_price = #{record.skuPrice,jdbcType=INTEGER},
      coupon_amount = #{record.couponAmount,jdbcType=INTEGER},
      settle_price = #{record.settlePrice,jdbcType=INTEGER},
      sku_count = #{record.skuCount,jdbcType=INTEGER},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      points = #{record.points,jdbcType=INTEGER},
      version = #{record.version,jdbcType=INTEGER},
      item_id = #{record.itemId,jdbcType=BIGINT},
      batch_no = #{record.batchNo,jdbcType=VARCHAR},
      promotion_price = #{record.promotionPrice,jdbcType=INTEGER},
      member_price = #{record.memberPrice,jdbcType=INTEGER},
      member_plus_price = #{record.memberPlusPrice,jdbcType=INTEGER},
      display_price = #{record.displayPrice,jdbcType=INTEGER},
      display_price_type = #{record.displayPriceType,jdbcType=INTEGER},
      member_plus_discounts = #{record.memberPlusDiscounts,jdbcType=INTEGER},
      promotion_discounts = #{record.promotionDiscounts,jdbcType=INTEGER},
      dismantled = #{record.dismantled,jdbcType=INTEGER},
      goods_unit = #{record.goodsUnit,jdbcType=VARCHAR},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      promotion_gift = #{record.promotionGift,jdbcType=INTEGER},
      batch_code = #{record.batchCode,jdbcType=VARCHAR},
      salesman = #{record.salesman,jdbcType=VARCHAR},
      salesman_id = #{record.salesmanId,jdbcType=VARCHAR},
      item_code = #{record.itemCode,jdbcType=VARCHAR},
      sku_label = #{record.skuLabel,jdbcType=VARCHAR},
      authorizer = #{record.authorizer,jdbcType=VARCHAR},
      authorizer_id = #{record.authorizerId,jdbcType=BIGINT},
      points_times = #{record.pointsTimes,jdbcType=INTEGER},
      business_id = #{record.businessId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      order_status_cur = #{record.orderStatusCur,jdbcType=INTEGER},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      cashier = #{record.cashier,jdbcType=VARCHAR},
      cashier_id = #{record.cashierId,jdbcType=BIGINT},
      user_card_num = #{record.userCardNum,jdbcType=VARCHAR},
      username = #{record.username,jdbcType=VARCHAR},
      user_phone = #{record.userPhone,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      mnemonic_code = #{record.mnemonicCode,jdbcType=VARCHAR},
      dosage_forms = #{record.dosageForms,jdbcType=VARCHAR},
      expire_date = #{record.expireDate,jdbcType=VARCHAR},
      producter = #{record.producter,jdbcType=VARCHAR},
      promotion_share_total = #{record.promotionShareTotal,jdbcType=INTEGER},
      prodarea = #{record.prodarea,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=INTEGER},
      market_cate_ids = #{record.marketCateIds,jdbcType=VARCHAR},
      market_cate_names = #{record.marketCateNames,jdbcType=VARCHAR},
      brand = #{record.brand,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entityTidb.OrderDetailOtter">
    update order_detail_otter
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="skuSnapId != null">
        sku_snap_id = #{skuSnapId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        sku_id = #{skuId,jdbcType=BIGINT},
      </if>
      <if test="skuName != null">
        sku_name = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="specificationsName != null">
        specifications_name = #{specificationsName,jdbcType=VARCHAR},
      </if>
      <if test="skuImage != null">
        sku_image = #{skuImage,jdbcType=VARCHAR},
      </if>
      <if test="skuPrice != null">
        sku_price = #{skuPrice,jdbcType=INTEGER},
      </if>
      <if test="couponAmount != null">
        coupon_amount = #{couponAmount,jdbcType=INTEGER},
      </if>
      <if test="settlePrice != null">
        settle_price = #{settlePrice,jdbcType=INTEGER},
      </if>
      <if test="skuCount != null">
        sku_count = #{skuCount,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="points != null">
        points = #{points,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="itemId != null">
        item_id = #{itemId,jdbcType=BIGINT},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=VARCHAR},
      </if>
      <if test="promotionPrice != null">
        promotion_price = #{promotionPrice,jdbcType=INTEGER},
      </if>
      <if test="memberPrice != null">
        member_price = #{memberPrice,jdbcType=INTEGER},
      </if>
      <if test="memberPlusPrice != null">
        member_plus_price = #{memberPlusPrice,jdbcType=INTEGER},
      </if>
      <if test="displayPrice != null">
        display_price = #{displayPrice,jdbcType=INTEGER},
      </if>
      <if test="displayPriceType != null">
        display_price_type = #{displayPriceType,jdbcType=INTEGER},
      </if>
      <if test="memberPlusDiscounts != null">
        member_plus_discounts = #{memberPlusDiscounts,jdbcType=INTEGER},
      </if>
      <if test="promotionDiscounts != null">
        promotion_discounts = #{promotionDiscounts,jdbcType=INTEGER},
      </if>
      <if test="dismantled != null">
        dismantled = #{dismantled,jdbcType=INTEGER},
      </if>
      <if test="goodsUnit != null">
        goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="promotionGift != null">
        promotion_gift = #{promotionGift,jdbcType=INTEGER},
      </if>
      <if test="batchCode != null">
        batch_code = #{batchCode,jdbcType=VARCHAR},
      </if>
      <if test="salesman != null">
        salesman = #{salesman,jdbcType=VARCHAR},
      </if>
      <if test="salesmanId != null">
        salesman_id = #{salesmanId,jdbcType=VARCHAR},
      </if>
      <if test="itemCode != null">
        item_code = #{itemCode,jdbcType=VARCHAR},
      </if>
      <if test="skuLabel != null">
        sku_label = #{skuLabel,jdbcType=VARCHAR},
      </if>
      <if test="authorizer != null">
        authorizer = #{authorizer,jdbcType=VARCHAR},
      </if>
      <if test="authorizerId != null">
        authorizer_id = #{authorizerId,jdbcType=BIGINT},
      </if>
      <if test="pointsTimes != null">
        points_times = #{pointsTimes,jdbcType=INTEGER},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="orderStatusCur != null">
        order_status_cur = #{orderStatusCur,jdbcType=INTEGER},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="cashier != null">
        cashier = #{cashier,jdbcType=VARCHAR},
      </if>
      <if test="cashierId != null">
        cashier_id = #{cashierId,jdbcType=BIGINT},
      </if>
      <if test="userCardNum != null">
        user_card_num = #{userCardNum,jdbcType=VARCHAR},
      </if>
      <if test="username != null">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="userPhone != null">
        user_phone = #{userPhone,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="mnemonicCode != null">
        mnemonic_code = #{mnemonicCode,jdbcType=VARCHAR},
      </if>
      <if test="dosageForms != null">
        dosage_forms = #{dosageForms,jdbcType=VARCHAR},
      </if>
      <if test="expireDate != null">
        expire_date = #{expireDate,jdbcType=VARCHAR},
      </if>
      <if test="producter != null">
        producter = #{producter,jdbcType=VARCHAR},
      </if>
      <if test="promotionShareTotal != null">
        promotion_share_total = #{promotionShareTotal,jdbcType=INTEGER},
      </if>
      <if test="prodarea != null">
        prodarea = #{prodarea,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="marketCateIds != null">
        market_cate_ids = #{marketCateIds,jdbcType=VARCHAR},
      </if>
      <if test="marketCateNames != null">
        market_cate_names = #{marketCateNames,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="promotionInfo != null">
        promotion_info = #{promotionInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where detail_id = #{detailId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.cowell.purchase.entityTidb.OrderDetailOtter">
    update order_detail_otter
    set order_id = #{orderId,jdbcType=BIGINT},
      sku_snap_id = #{skuSnapId,jdbcType=BIGINT},
      sku_id = #{skuId,jdbcType=BIGINT},
      sku_name = #{skuName,jdbcType=VARCHAR},
      specifications_name = #{specificationsName,jdbcType=VARCHAR},
      sku_image = #{skuImage,jdbcType=VARCHAR},
      sku_price = #{skuPrice,jdbcType=INTEGER},
      coupon_amount = #{couponAmount,jdbcType=INTEGER},
      settle_price = #{settlePrice,jdbcType=INTEGER},
      sku_count = #{skuCount,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      points = #{points,jdbcType=INTEGER},
      version = #{version,jdbcType=INTEGER},
      item_id = #{itemId,jdbcType=BIGINT},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      promotion_price = #{promotionPrice,jdbcType=INTEGER},
      member_price = #{memberPrice,jdbcType=INTEGER},
      member_plus_price = #{memberPlusPrice,jdbcType=INTEGER},
      display_price = #{displayPrice,jdbcType=INTEGER},
      display_price_type = #{displayPriceType,jdbcType=INTEGER},
      member_plus_discounts = #{memberPlusDiscounts,jdbcType=INTEGER},
      promotion_discounts = #{promotionDiscounts,jdbcType=INTEGER},
      dismantled = #{dismantled,jdbcType=INTEGER},
      goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      promotion_gift = #{promotionGift,jdbcType=INTEGER},
      batch_code = #{batchCode,jdbcType=VARCHAR},
      salesman = #{salesman,jdbcType=VARCHAR},
      salesman_id = #{salesmanId,jdbcType=VARCHAR},
      item_code = #{itemCode,jdbcType=VARCHAR},
      sku_label = #{skuLabel,jdbcType=VARCHAR},
      authorizer = #{authorizer,jdbcType=VARCHAR},
      authorizer_id = #{authorizerId,jdbcType=BIGINT},
      points_times = #{pointsTimes,jdbcType=INTEGER},
      business_id = #{businessId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      order_status_cur = #{orderStatusCur,jdbcType=INTEGER},
      store_name = #{storeName,jdbcType=VARCHAR},
      cashier = #{cashier,jdbcType=VARCHAR},
      cashier_id = #{cashierId,jdbcType=BIGINT},
      user_card_num = #{userCardNum,jdbcType=VARCHAR},
      username = #{username,jdbcType=VARCHAR},
      user_phone = #{userPhone,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      bar_code = #{barCode,jdbcType=VARCHAR},
      mnemonic_code = #{mnemonicCode,jdbcType=VARCHAR},
      dosage_forms = #{dosageForms,jdbcType=VARCHAR},
      expire_date = #{expireDate,jdbcType=VARCHAR},
      producter = #{producter,jdbcType=VARCHAR},
      promotion_share_total = #{promotionShareTotal,jdbcType=INTEGER},
      prodarea = #{prodarea,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      market_cate_ids = #{marketCateIds,jdbcType=VARCHAR},
      market_cate_names = #{marketCateNames,jdbcType=VARCHAR},
      brand = #{brand,jdbcType=VARCHAR},
      promotion_info = #{promotionInfo,jdbcType=LONGVARCHAR}
    where detail_id = #{detailId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entityTidb.OrderDetailOtter">
    update order_detail_otter
    set order_id = #{orderId,jdbcType=BIGINT},
      sku_snap_id = #{skuSnapId,jdbcType=BIGINT},
      sku_id = #{skuId,jdbcType=BIGINT},
      sku_name = #{skuName,jdbcType=VARCHAR},
      specifications_name = #{specificationsName,jdbcType=VARCHAR},
      sku_image = #{skuImage,jdbcType=VARCHAR},
      sku_price = #{skuPrice,jdbcType=INTEGER},
      coupon_amount = #{couponAmount,jdbcType=INTEGER},
      settle_price = #{settlePrice,jdbcType=INTEGER},
      sku_count = #{skuCount,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      points = #{points,jdbcType=INTEGER},
      version = #{version,jdbcType=INTEGER},
      item_id = #{itemId,jdbcType=BIGINT},
      batch_no = #{batchNo,jdbcType=VARCHAR},
      promotion_price = #{promotionPrice,jdbcType=INTEGER},
      member_price = #{memberPrice,jdbcType=INTEGER},
      member_plus_price = #{memberPlusPrice,jdbcType=INTEGER},
      display_price = #{displayPrice,jdbcType=INTEGER},
      display_price_type = #{displayPriceType,jdbcType=INTEGER},
      member_plus_discounts = #{memberPlusDiscounts,jdbcType=INTEGER},
      promotion_discounts = #{promotionDiscounts,jdbcType=INTEGER},
      dismantled = #{dismantled,jdbcType=INTEGER},
      goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      promotion_gift = #{promotionGift,jdbcType=INTEGER},
      batch_code = #{batchCode,jdbcType=VARCHAR},
      salesman = #{salesman,jdbcType=VARCHAR},
      salesman_id = #{salesmanId,jdbcType=VARCHAR},
      item_code = #{itemCode,jdbcType=VARCHAR},
      sku_label = #{skuLabel,jdbcType=VARCHAR},
      authorizer = #{authorizer,jdbcType=VARCHAR},
      authorizer_id = #{authorizerId,jdbcType=BIGINT},
      points_times = #{pointsTimes,jdbcType=INTEGER},
      business_id = #{businessId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      order_status_cur = #{orderStatusCur,jdbcType=INTEGER},
      store_name = #{storeName,jdbcType=VARCHAR},
      cashier = #{cashier,jdbcType=VARCHAR},
      cashier_id = #{cashierId,jdbcType=BIGINT},
      user_card_num = #{userCardNum,jdbcType=VARCHAR},
      username = #{username,jdbcType=VARCHAR},
      user_phone = #{userPhone,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      bar_code = #{barCode,jdbcType=VARCHAR},
      mnemonic_code = #{mnemonicCode,jdbcType=VARCHAR},
      dosage_forms = #{dosageForms,jdbcType=VARCHAR},
      expire_date = #{expireDate,jdbcType=VARCHAR},
      producter = #{producter,jdbcType=VARCHAR},
      promotion_share_total = #{promotionShareTotal,jdbcType=INTEGER},
      prodarea = #{prodarea,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      market_cate_ids = #{marketCateIds,jdbcType=VARCHAR},
      market_cate_names = #{marketCateNames,jdbcType=VARCHAR},
      brand = #{brand,jdbcType=VARCHAR}
    where detail_id = #{detailId,jdbcType=BIGINT}
  </update>
</mapper>