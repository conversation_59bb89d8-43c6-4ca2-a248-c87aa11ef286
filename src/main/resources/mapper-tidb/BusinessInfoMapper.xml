<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapperTidb.BusinessInfoMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entityTidb.BusinessInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="short_name" jdbcType="VARCHAR" property="shortName" />
    <result column="organ_code" jdbcType="VARCHAR" property="organCode" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="logo_url" jdbcType="VARCHAR" property="logoUrl" />
    <result column="business_type" jdbcType="TINYINT" property="businessType" />
    <result column="belong_to" jdbcType="BIGINT" property="belongTo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_name, short_name, organ_code, extend, version, status, gmt_create, gmt_update, 
    created_by, updated_by, logo_url, business_type, belong_to
  </sql>
  <select id="selectByExample" parameterType="com.cowell.purchase.entityTidb.BusinessInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from business_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from business_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from business_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entityTidb.BusinessInfoExample">
    delete from business_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entityTidb.BusinessInfo">
    insert into business_info (id, business_name, short_name, 
      organ_code, extend, version, 
      status, gmt_create, gmt_update, 
      created_by, updated_by, logo_url, 
      business_type, belong_to)
    values (#{id,jdbcType=BIGINT}, #{businessName,jdbcType=VARCHAR}, #{shortName,jdbcType=VARCHAR}, 
      #{organCode,jdbcType=VARCHAR}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, 
      #{createdBy,jdbcType=BIGINT}, #{updatedBy,jdbcType=BIGINT}, #{logoUrl,jdbcType=VARCHAR}, 
      #{businessType,jdbcType=TINYINT}, #{belongTo,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entityTidb.BusinessInfo">
    insert into business_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessName != null">
        business_name,
      </if>
      <if test="shortName != null">
        short_name,
      </if>
      <if test="organCode != null">
        organ_code,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="logoUrl != null">
        logo_url,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="belongTo != null">
        belong_to,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessName != null">
        #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="shortName != null">
        #{shortName,jdbcType=VARCHAR},
      </if>
      <if test="organCode != null">
        #{organCode,jdbcType=VARCHAR},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="logoUrl != null">
        #{logoUrl,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=TINYINT},
      </if>
      <if test="belongTo != null">
        #{belongTo,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entityTidb.BusinessInfoExample" resultType="java.lang.Long">
    select count(*) from business_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update business_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.businessName != null">
        business_name = #{record.businessName,jdbcType=VARCHAR},
      </if>
      <if test="record.shortName != null">
        short_name = #{record.shortName,jdbcType=VARCHAR},
      </if>
      <if test="record.organCode != null">
        organ_code = #{record.organCode,jdbcType=VARCHAR},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.logoUrl != null">
        logo_url = #{record.logoUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.businessType != null">
        business_type = #{record.businessType,jdbcType=TINYINT},
      </if>
      <if test="record.belongTo != null">
        belong_to = #{record.belongTo,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update business_info
    set id = #{record.id,jdbcType=BIGINT},
      business_name = #{record.businessName,jdbcType=VARCHAR},
      short_name = #{record.shortName,jdbcType=VARCHAR},
      organ_code = #{record.organCode,jdbcType=VARCHAR},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      status = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      logo_url = #{record.logoUrl,jdbcType=VARCHAR},
      business_type = #{record.businessType,jdbcType=TINYINT},
      belong_to = #{record.belongTo,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entityTidb.BusinessInfo">
    update business_info
    <set>
      <if test="businessName != null">
        business_name = #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="shortName != null">
        short_name = #{shortName,jdbcType=VARCHAR},
      </if>
      <if test="organCode != null">
        organ_code = #{organCode,jdbcType=VARCHAR},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="logoUrl != null">
        logo_url = #{logoUrl,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=TINYINT},
      </if>
      <if test="belongTo != null">
        belong_to = #{belongTo,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entityTidb.BusinessInfo">
    update business_info
    set business_name = #{businessName,jdbcType=VARCHAR},
      short_name = #{shortName,jdbcType=VARCHAR},
      organ_code = #{organCode,jdbcType=VARCHAR},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=BIGINT},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      logo_url = #{logoUrl,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=TINYINT},
      belong_to = #{belongTo,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>