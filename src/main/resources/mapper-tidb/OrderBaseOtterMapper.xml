<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.purchase.mapperTidb.OrderBaseOtterMapper">
  <resultMap id="BaseResultMap" type="com.cowell.purchase.entityTidb.OrderBaseOtter">
    <id column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="order_status" jdbcType="BIGINT" property="orderStatus" />
    <result column="order_status_cur" jdbcType="INTEGER" property="orderStatusCur" />
    <result column="pay_amount" jdbcType="INTEGER" property="payAmount" />
    <result column="total_amount" jdbcType="INTEGER" property="totalAmount" />
    <result column="coupon_amount" jdbcType="INTEGER" property="couponAmount" />
    <result column="express_no" jdbcType="VARCHAR" property="expressNo" />
    <result column="express_fee" jdbcType="INTEGER" property="expressFee" />
    <result column="consignee_id" jdbcType="BIGINT" property="consigneeId" />
    <result column="consignee" jdbcType="VARCHAR" property="consignee" />
    <result column="consignee_phone" jdbcType="VARCHAR" property="consigneePhone" />
    <result column="consignee_province" jdbcType="VARCHAR" property="consigneeProvince" />
    <result column="consignee_city" jdbcType="VARCHAR" property="consigneeCity" />
    <result column="consignee_area" jdbcType="VARCHAR" property="consigneeArea" />
    <result column="consignee_address" jdbcType="VARCHAR" property="consigneeAddress" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="pay_channel" jdbcType="VARCHAR" property="payChannel" />
    <result column="refund_amount" jdbcType="INTEGER" property="refundAmount" />
    <result column="refund_reason" jdbcType="VARCHAR" property="refundReason" />
    <result column="refund_remarks" jdbcType="VARCHAR" property="refundRemarks" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="refund_time" jdbcType="TIMESTAMP" property="refundTime" />
    <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="pay_points" jdbcType="INTEGER" property="payPoints" />
    <result column="third_party_order_no" jdbcType="VARCHAR" property="thirdPartyOrderNo" />
    <result column="promotion_discounts" jdbcType="INTEGER" property="promotionDiscounts" />
    <result column="member_plus_discounts" jdbcType="INTEGER" property="memberPlusDiscounts" />
    <result column="item_type" jdbcType="INTEGER" property="itemType" />
    <result column="wipe_money" jdbcType="INTEGER" property="wipeMoney" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="total_original_amount" jdbcType="INTEGER" property="totalOriginalAmount" />
    <result column="promotion_label" jdbcType="VARCHAR" property="promotionLabel" />
    <result column="cashier" jdbcType="VARCHAR" property="cashier" />
    <result column="cashier_id" jdbcType="BIGINT" property="cashierId" />
    <result column="purchase_pay_amount" jdbcType="INTEGER" property="purchasePayAmount" />
    <result column="order_sign" jdbcType="VARCHAR" property="orderSign" />
    <result column="offline_coupon_amount" jdbcType="BIGINT" property="offlineCouponAmount" />
    <result column="points_discount_amount" jdbcType="BIGINT" property="pointsDiscountAmount" />
    <result column="shift_id" jdbcType="BIGINT" property="shiftId" />
    <result column="order_source" jdbcType="INTEGER" property="orderSource" />
    <result column="present_points" jdbcType="BIGINT" property="presentPoints" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.cowell.purchase.entityTidb.OrderBaseOtter">
    <result column="promotion_info" jdbcType="LONGVARCHAR" property="promotionInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    order_id, type, user_id, business_id, store_id, order_status, order_status_cur, pay_amount, 
    total_amount, coupon_amount, express_no, express_fee, consignee_id, consignee, consignee_phone, 
    consignee_province, consignee_city, consignee_area, consignee_address, remarks, pay_channel, 
    refund_amount, refund_reason, refund_remarks, pay_time, complete_time, refund_time, 
    cancel_time, gmt_create, gmt_update, status, extend, version, pay_points, third_party_order_no, 
    promotion_discounts, member_plus_discounts, item_type, wipe_money, pay_type, store_name, 
    total_original_amount, promotion_label, cashier, cashier_id, purchase_pay_amount, 
    order_sign, offline_coupon_amount, points_discount_amount, shift_id, order_source, 
    present_points
  </sql>
  <sql id="Blob_Column_List">
    promotion_info
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.cowell.purchase.entityTidb.OrderBaseOtterExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from order_base_otter
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.cowell.purchase.entityTidb.OrderBaseOtterExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from order_base_otter
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from order_base_otter
    where order_id = #{orderId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_base_otter
    where order_id = #{orderId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.purchase.entityTidb.OrderBaseOtterExample">
    delete from order_base_otter
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.purchase.entityTidb.OrderBaseOtter">
    insert into order_base_otter (order_id, type, user_id, 
      business_id, store_id, order_status, 
      order_status_cur, pay_amount, total_amount, 
      coupon_amount, express_no, express_fee, 
      consignee_id, consignee, consignee_phone, 
      consignee_province, consignee_city, consignee_area, 
      consignee_address, remarks, pay_channel, 
      refund_amount, refund_reason, refund_remarks, 
      pay_time, complete_time, refund_time, 
      cancel_time, gmt_create, gmt_update, 
      status, extend, version, 
      pay_points, third_party_order_no, promotion_discounts, 
      member_plus_discounts, item_type, wipe_money, 
      pay_type, store_name, total_original_amount, 
      promotion_label, cashier, cashier_id, 
      purchase_pay_amount, order_sign, offline_coupon_amount, 
      points_discount_amount, shift_id, order_source, 
      present_points, promotion_info)
    values (#{orderId,jdbcType=BIGINT}, #{type,jdbcType=INTEGER}, #{userId,jdbcType=BIGINT}, 
      #{businessId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{orderStatus,jdbcType=BIGINT}, 
      #{orderStatusCur,jdbcType=INTEGER}, #{payAmount,jdbcType=INTEGER}, #{totalAmount,jdbcType=INTEGER}, 
      #{couponAmount,jdbcType=INTEGER}, #{expressNo,jdbcType=VARCHAR}, #{expressFee,jdbcType=INTEGER}, 
      #{consigneeId,jdbcType=BIGINT}, #{consignee,jdbcType=VARCHAR}, #{consigneePhone,jdbcType=VARCHAR}, 
      #{consigneeProvince,jdbcType=VARCHAR}, #{consigneeCity,jdbcType=VARCHAR}, #{consigneeArea,jdbcType=VARCHAR}, 
      #{consigneeAddress,jdbcType=VARCHAR}, #{remarks,jdbcType=VARCHAR}, #{payChannel,jdbcType=VARCHAR}, 
      #{refundAmount,jdbcType=INTEGER}, #{refundReason,jdbcType=VARCHAR}, #{refundRemarks,jdbcType=VARCHAR}, 
      #{payTime,jdbcType=TIMESTAMP}, #{completeTime,jdbcType=TIMESTAMP}, #{refundTime,jdbcType=TIMESTAMP}, 
      #{cancelTime,jdbcType=TIMESTAMP}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, 
      #{status,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{payPoints,jdbcType=INTEGER}, #{thirdPartyOrderNo,jdbcType=VARCHAR}, #{promotionDiscounts,jdbcType=INTEGER}, 
      #{memberPlusDiscounts,jdbcType=INTEGER}, #{itemType,jdbcType=INTEGER}, #{wipeMoney,jdbcType=INTEGER}, 
      #{payType,jdbcType=INTEGER}, #{storeName,jdbcType=VARCHAR}, #{totalOriginalAmount,jdbcType=INTEGER}, 
      #{promotionLabel,jdbcType=VARCHAR}, #{cashier,jdbcType=VARCHAR}, #{cashierId,jdbcType=BIGINT}, 
      #{purchasePayAmount,jdbcType=INTEGER}, #{orderSign,jdbcType=VARCHAR}, #{offlineCouponAmount,jdbcType=BIGINT}, 
      #{pointsDiscountAmount,jdbcType=BIGINT}, #{shiftId,jdbcType=BIGINT}, #{orderSource,jdbcType=INTEGER}, 
      #{presentPoints,jdbcType=BIGINT}, #{promotionInfo,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.purchase.entityTidb.OrderBaseOtter">
    insert into order_base_otter
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="orderStatusCur != null">
        order_status_cur,
      </if>
      <if test="payAmount != null">
        pay_amount,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="couponAmount != null">
        coupon_amount,
      </if>
      <if test="expressNo != null">
        express_no,
      </if>
      <if test="expressFee != null">
        express_fee,
      </if>
      <if test="consigneeId != null">
        consignee_id,
      </if>
      <if test="consignee != null">
        consignee,
      </if>
      <if test="consigneePhone != null">
        consignee_phone,
      </if>
      <if test="consigneeProvince != null">
        consignee_province,
      </if>
      <if test="consigneeCity != null">
        consignee_city,
      </if>
      <if test="consigneeArea != null">
        consignee_area,
      </if>
      <if test="consigneeAddress != null">
        consignee_address,
      </if>
      <if test="remarks != null">
        remarks,
      </if>
      <if test="payChannel != null">
        pay_channel,
      </if>
      <if test="refundAmount != null">
        refund_amount,
      </if>
      <if test="refundReason != null">
        refund_reason,
      </if>
      <if test="refundRemarks != null">
        refund_remarks,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="refundTime != null">
        refund_time,
      </if>
      <if test="cancelTime != null">
        cancel_time,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="payPoints != null">
        pay_points,
      </if>
      <if test="thirdPartyOrderNo != null">
        third_party_order_no,
      </if>
      <if test="promotionDiscounts != null">
        promotion_discounts,
      </if>
      <if test="memberPlusDiscounts != null">
        member_plus_discounts,
      </if>
      <if test="itemType != null">
        item_type,
      </if>
      <if test="wipeMoney != null">
        wipe_money,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="totalOriginalAmount != null">
        total_original_amount,
      </if>
      <if test="promotionLabel != null">
        promotion_label,
      </if>
      <if test="cashier != null">
        cashier,
      </if>
      <if test="cashierId != null">
        cashier_id,
      </if>
      <if test="purchasePayAmount != null">
        purchase_pay_amount,
      </if>
      <if test="orderSign != null">
        order_sign,
      </if>
      <if test="offlineCouponAmount != null">
        offline_coupon_amount,
      </if>
      <if test="pointsDiscountAmount != null">
        points_discount_amount,
      </if>
      <if test="shiftId != null">
        shift_id,
      </if>
      <if test="orderSource != null">
        order_source,
      </if>
      <if test="presentPoints != null">
        present_points,
      </if>
      <if test="promotionInfo != null">
        promotion_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=BIGINT},
      </if>
      <if test="orderStatusCur != null">
        #{orderStatusCur,jdbcType=INTEGER},
      </if>
      <if test="payAmount != null">
        #{payAmount,jdbcType=INTEGER},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=INTEGER},
      </if>
      <if test="couponAmount != null">
        #{couponAmount,jdbcType=INTEGER},
      </if>
      <if test="expressNo != null">
        #{expressNo,jdbcType=VARCHAR},
      </if>
      <if test="expressFee != null">
        #{expressFee,jdbcType=INTEGER},
      </if>
      <if test="consigneeId != null">
        #{consigneeId,jdbcType=BIGINT},
      </if>
      <if test="consignee != null">
        #{consignee,jdbcType=VARCHAR},
      </if>
      <if test="consigneePhone != null">
        #{consigneePhone,jdbcType=VARCHAR},
      </if>
      <if test="consigneeProvince != null">
        #{consigneeProvince,jdbcType=VARCHAR},
      </if>
      <if test="consigneeCity != null">
        #{consigneeCity,jdbcType=VARCHAR},
      </if>
      <if test="consigneeArea != null">
        #{consigneeArea,jdbcType=VARCHAR},
      </if>
      <if test="consigneeAddress != null">
        #{consigneeAddress,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="payChannel != null">
        #{payChannel,jdbcType=VARCHAR},
      </if>
      <if test="refundAmount != null">
        #{refundAmount,jdbcType=INTEGER},
      </if>
      <if test="refundReason != null">
        #{refundReason,jdbcType=VARCHAR},
      </if>
      <if test="refundRemarks != null">
        #{refundRemarks,jdbcType=VARCHAR},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundTime != null">
        #{refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelTime != null">
        #{cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="payPoints != null">
        #{payPoints,jdbcType=INTEGER},
      </if>
      <if test="thirdPartyOrderNo != null">
        #{thirdPartyOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="promotionDiscounts != null">
        #{promotionDiscounts,jdbcType=INTEGER},
      </if>
      <if test="memberPlusDiscounts != null">
        #{memberPlusDiscounts,jdbcType=INTEGER},
      </if>
      <if test="itemType != null">
        #{itemType,jdbcType=INTEGER},
      </if>
      <if test="wipeMoney != null">
        #{wipeMoney,jdbcType=INTEGER},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=INTEGER},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="totalOriginalAmount != null">
        #{totalOriginalAmount,jdbcType=INTEGER},
      </if>
      <if test="promotionLabel != null">
        #{promotionLabel,jdbcType=VARCHAR},
      </if>
      <if test="cashier != null">
        #{cashier,jdbcType=VARCHAR},
      </if>
      <if test="cashierId != null">
        #{cashierId,jdbcType=BIGINT},
      </if>
      <if test="purchasePayAmount != null">
        #{purchasePayAmount,jdbcType=INTEGER},
      </if>
      <if test="orderSign != null">
        #{orderSign,jdbcType=VARCHAR},
      </if>
      <if test="offlineCouponAmount != null">
        #{offlineCouponAmount,jdbcType=BIGINT},
      </if>
      <if test="pointsDiscountAmount != null">
        #{pointsDiscountAmount,jdbcType=BIGINT},
      </if>
      <if test="shiftId != null">
        #{shiftId,jdbcType=BIGINT},
      </if>
      <if test="orderSource != null">
        #{orderSource,jdbcType=INTEGER},
      </if>
      <if test="presentPoints != null">
        #{presentPoints,jdbcType=BIGINT},
      </if>
      <if test="promotionInfo != null">
        #{promotionInfo,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.purchase.entityTidb.OrderBaseOtterExample" resultType="java.lang.Long">
    select count(*) from order_base_otter
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update order_base_otter
    <set>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.orderStatus != null">
        order_status = #{record.orderStatus,jdbcType=BIGINT},
      </if>
      <if test="record.orderStatusCur != null">
        order_status_cur = #{record.orderStatusCur,jdbcType=INTEGER},
      </if>
      <if test="record.payAmount != null">
        pay_amount = #{record.payAmount,jdbcType=INTEGER},
      </if>
      <if test="record.totalAmount != null">
        total_amount = #{record.totalAmount,jdbcType=INTEGER},
      </if>
      <if test="record.couponAmount != null">
        coupon_amount = #{record.couponAmount,jdbcType=INTEGER},
      </if>
      <if test="record.expressNo != null">
        express_no = #{record.expressNo,jdbcType=VARCHAR},
      </if>
      <if test="record.expressFee != null">
        express_fee = #{record.expressFee,jdbcType=INTEGER},
      </if>
      <if test="record.consigneeId != null">
        consignee_id = #{record.consigneeId,jdbcType=BIGINT},
      </if>
      <if test="record.consignee != null">
        consignee = #{record.consignee,jdbcType=VARCHAR},
      </if>
      <if test="record.consigneePhone != null">
        consignee_phone = #{record.consigneePhone,jdbcType=VARCHAR},
      </if>
      <if test="record.consigneeProvince != null">
        consignee_province = #{record.consigneeProvince,jdbcType=VARCHAR},
      </if>
      <if test="record.consigneeCity != null">
        consignee_city = #{record.consigneeCity,jdbcType=VARCHAR},
      </if>
      <if test="record.consigneeArea != null">
        consignee_area = #{record.consigneeArea,jdbcType=VARCHAR},
      </if>
      <if test="record.consigneeAddress != null">
        consignee_address = #{record.consigneeAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.remarks != null">
        remarks = #{record.remarks,jdbcType=VARCHAR},
      </if>
      <if test="record.payChannel != null">
        pay_channel = #{record.payChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.refundAmount != null">
        refund_amount = #{record.refundAmount,jdbcType=INTEGER},
      </if>
      <if test="record.refundReason != null">
        refund_reason = #{record.refundReason,jdbcType=VARCHAR},
      </if>
      <if test="record.refundRemarks != null">
        refund_remarks = #{record.refundRemarks,jdbcType=VARCHAR},
      </if>
      <if test="record.payTime != null">
        pay_time = #{record.payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.completeTime != null">
        complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.refundTime != null">
        refund_time = #{record.refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.cancelTime != null">
        cancel_time = #{record.cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.payPoints != null">
        pay_points = #{record.payPoints,jdbcType=INTEGER},
      </if>
      <if test="record.thirdPartyOrderNo != null">
        third_party_order_no = #{record.thirdPartyOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="record.promotionDiscounts != null">
        promotion_discounts = #{record.promotionDiscounts,jdbcType=INTEGER},
      </if>
      <if test="record.memberPlusDiscounts != null">
        member_plus_discounts = #{record.memberPlusDiscounts,jdbcType=INTEGER},
      </if>
      <if test="record.itemType != null">
        item_type = #{record.itemType,jdbcType=INTEGER},
      </if>
      <if test="record.wipeMoney != null">
        wipe_money = #{record.wipeMoney,jdbcType=INTEGER},
      </if>
      <if test="record.payType != null">
        pay_type = #{record.payType,jdbcType=INTEGER},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.totalOriginalAmount != null">
        total_original_amount = #{record.totalOriginalAmount,jdbcType=INTEGER},
      </if>
      <if test="record.promotionLabel != null">
        promotion_label = #{record.promotionLabel,jdbcType=VARCHAR},
      </if>
      <if test="record.cashier != null">
        cashier = #{record.cashier,jdbcType=VARCHAR},
      </if>
      <if test="record.cashierId != null">
        cashier_id = #{record.cashierId,jdbcType=BIGINT},
      </if>
      <if test="record.purchasePayAmount != null">
        purchase_pay_amount = #{record.purchasePayAmount,jdbcType=INTEGER},
      </if>
      <if test="record.orderSign != null">
        order_sign = #{record.orderSign,jdbcType=VARCHAR},
      </if>
      <if test="record.offlineCouponAmount != null">
        offline_coupon_amount = #{record.offlineCouponAmount,jdbcType=BIGINT},
      </if>
      <if test="record.pointsDiscountAmount != null">
        points_discount_amount = #{record.pointsDiscountAmount,jdbcType=BIGINT},
      </if>
      <if test="record.shiftId != null">
        shift_id = #{record.shiftId,jdbcType=BIGINT},
      </if>
      <if test="record.orderSource != null">
        order_source = #{record.orderSource,jdbcType=INTEGER},
      </if>
      <if test="record.presentPoints != null">
        present_points = #{record.presentPoints,jdbcType=BIGINT},
      </if>
      <if test="record.promotionInfo != null">
        promotion_info = #{record.promotionInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update order_base_otter
    set order_id = #{record.orderId,jdbcType=BIGINT},
      type = #{record.type,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=BIGINT},
      business_id = #{record.businessId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      order_status = #{record.orderStatus,jdbcType=BIGINT},
      order_status_cur = #{record.orderStatusCur,jdbcType=INTEGER},
      pay_amount = #{record.payAmount,jdbcType=INTEGER},
      total_amount = #{record.totalAmount,jdbcType=INTEGER},
      coupon_amount = #{record.couponAmount,jdbcType=INTEGER},
      express_no = #{record.expressNo,jdbcType=VARCHAR},
      express_fee = #{record.expressFee,jdbcType=INTEGER},
      consignee_id = #{record.consigneeId,jdbcType=BIGINT},
      consignee = #{record.consignee,jdbcType=VARCHAR},
      consignee_phone = #{record.consigneePhone,jdbcType=VARCHAR},
      consignee_province = #{record.consigneeProvince,jdbcType=VARCHAR},
      consignee_city = #{record.consigneeCity,jdbcType=VARCHAR},
      consignee_area = #{record.consigneeArea,jdbcType=VARCHAR},
      consignee_address = #{record.consigneeAddress,jdbcType=VARCHAR},
      remarks = #{record.remarks,jdbcType=VARCHAR},
      pay_channel = #{record.payChannel,jdbcType=VARCHAR},
      refund_amount = #{record.refundAmount,jdbcType=INTEGER},
      refund_reason = #{record.refundReason,jdbcType=VARCHAR},
      refund_remarks = #{record.refundRemarks,jdbcType=VARCHAR},
      pay_time = #{record.payTime,jdbcType=TIMESTAMP},
      complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      refund_time = #{record.refundTime,jdbcType=TIMESTAMP},
      cancel_time = #{record.cancelTime,jdbcType=TIMESTAMP},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      pay_points = #{record.payPoints,jdbcType=INTEGER},
      third_party_order_no = #{record.thirdPartyOrderNo,jdbcType=VARCHAR},
      promotion_discounts = #{record.promotionDiscounts,jdbcType=INTEGER},
      member_plus_discounts = #{record.memberPlusDiscounts,jdbcType=INTEGER},
      item_type = #{record.itemType,jdbcType=INTEGER},
      wipe_money = #{record.wipeMoney,jdbcType=INTEGER},
      pay_type = #{record.payType,jdbcType=INTEGER},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      total_original_amount = #{record.totalOriginalAmount,jdbcType=INTEGER},
      promotion_label = #{record.promotionLabel,jdbcType=VARCHAR},
      cashier = #{record.cashier,jdbcType=VARCHAR},
      cashier_id = #{record.cashierId,jdbcType=BIGINT},
      purchase_pay_amount = #{record.purchasePayAmount,jdbcType=INTEGER},
      order_sign = #{record.orderSign,jdbcType=VARCHAR},
      offline_coupon_amount = #{record.offlineCouponAmount,jdbcType=BIGINT},
      points_discount_amount = #{record.pointsDiscountAmount,jdbcType=BIGINT},
      shift_id = #{record.shiftId,jdbcType=BIGINT},
      order_source = #{record.orderSource,jdbcType=INTEGER},
      present_points = #{record.presentPoints,jdbcType=BIGINT},
      promotion_info = #{record.promotionInfo,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update order_base_otter
    set order_id = #{record.orderId,jdbcType=BIGINT},
      type = #{record.type,jdbcType=INTEGER},
      user_id = #{record.userId,jdbcType=BIGINT},
      business_id = #{record.businessId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      order_status = #{record.orderStatus,jdbcType=BIGINT},
      order_status_cur = #{record.orderStatusCur,jdbcType=INTEGER},
      pay_amount = #{record.payAmount,jdbcType=INTEGER},
      total_amount = #{record.totalAmount,jdbcType=INTEGER},
      coupon_amount = #{record.couponAmount,jdbcType=INTEGER},
      express_no = #{record.expressNo,jdbcType=VARCHAR},
      express_fee = #{record.expressFee,jdbcType=INTEGER},
      consignee_id = #{record.consigneeId,jdbcType=BIGINT},
      consignee = #{record.consignee,jdbcType=VARCHAR},
      consignee_phone = #{record.consigneePhone,jdbcType=VARCHAR},
      consignee_province = #{record.consigneeProvince,jdbcType=VARCHAR},
      consignee_city = #{record.consigneeCity,jdbcType=VARCHAR},
      consignee_area = #{record.consigneeArea,jdbcType=VARCHAR},
      consignee_address = #{record.consigneeAddress,jdbcType=VARCHAR},
      remarks = #{record.remarks,jdbcType=VARCHAR},
      pay_channel = #{record.payChannel,jdbcType=VARCHAR},
      refund_amount = #{record.refundAmount,jdbcType=INTEGER},
      refund_reason = #{record.refundReason,jdbcType=VARCHAR},
      refund_remarks = #{record.refundRemarks,jdbcType=VARCHAR},
      pay_time = #{record.payTime,jdbcType=TIMESTAMP},
      complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      refund_time = #{record.refundTime,jdbcType=TIMESTAMP},
      cancel_time = #{record.cancelTime,jdbcType=TIMESTAMP},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      pay_points = #{record.payPoints,jdbcType=INTEGER},
      third_party_order_no = #{record.thirdPartyOrderNo,jdbcType=VARCHAR},
      promotion_discounts = #{record.promotionDiscounts,jdbcType=INTEGER},
      member_plus_discounts = #{record.memberPlusDiscounts,jdbcType=INTEGER},
      item_type = #{record.itemType,jdbcType=INTEGER},
      wipe_money = #{record.wipeMoney,jdbcType=INTEGER},
      pay_type = #{record.payType,jdbcType=INTEGER},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      total_original_amount = #{record.totalOriginalAmount,jdbcType=INTEGER},
      promotion_label = #{record.promotionLabel,jdbcType=VARCHAR},
      cashier = #{record.cashier,jdbcType=VARCHAR},
      cashier_id = #{record.cashierId,jdbcType=BIGINT},
      purchase_pay_amount = #{record.purchasePayAmount,jdbcType=INTEGER},
      order_sign = #{record.orderSign,jdbcType=VARCHAR},
      offline_coupon_amount = #{record.offlineCouponAmount,jdbcType=BIGINT},
      points_discount_amount = #{record.pointsDiscountAmount,jdbcType=BIGINT},
      shift_id = #{record.shiftId,jdbcType=BIGINT},
      order_source = #{record.orderSource,jdbcType=INTEGER},
      present_points = #{record.presentPoints,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.purchase.entityTidb.OrderBaseOtter">
    update order_base_otter
    <set>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=BIGINT},
      </if>
      <if test="orderStatusCur != null">
        order_status_cur = #{orderStatusCur,jdbcType=INTEGER},
      </if>
      <if test="payAmount != null">
        pay_amount = #{payAmount,jdbcType=INTEGER},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=INTEGER},
      </if>
      <if test="couponAmount != null">
        coupon_amount = #{couponAmount,jdbcType=INTEGER},
      </if>
      <if test="expressNo != null">
        express_no = #{expressNo,jdbcType=VARCHAR},
      </if>
      <if test="expressFee != null">
        express_fee = #{expressFee,jdbcType=INTEGER},
      </if>
      <if test="consigneeId != null">
        consignee_id = #{consigneeId,jdbcType=BIGINT},
      </if>
      <if test="consignee != null">
        consignee = #{consignee,jdbcType=VARCHAR},
      </if>
      <if test="consigneePhone != null">
        consignee_phone = #{consigneePhone,jdbcType=VARCHAR},
      </if>
      <if test="consigneeProvince != null">
        consignee_province = #{consigneeProvince,jdbcType=VARCHAR},
      </if>
      <if test="consigneeCity != null">
        consignee_city = #{consigneeCity,jdbcType=VARCHAR},
      </if>
      <if test="consigneeArea != null">
        consignee_area = #{consigneeArea,jdbcType=VARCHAR},
      </if>
      <if test="consigneeAddress != null">
        consignee_address = #{consigneeAddress,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="payChannel != null">
        pay_channel = #{payChannel,jdbcType=VARCHAR},
      </if>
      <if test="refundAmount != null">
        refund_amount = #{refundAmount,jdbcType=INTEGER},
      </if>
      <if test="refundReason != null">
        refund_reason = #{refundReason,jdbcType=VARCHAR},
      </if>
      <if test="refundRemarks != null">
        refund_remarks = #{refundRemarks,jdbcType=VARCHAR},
      </if>
      <if test="payTime != null">
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundTime != null">
        refund_time = #{refundTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelTime != null">
        cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="payPoints != null">
        pay_points = #{payPoints,jdbcType=INTEGER},
      </if>
      <if test="thirdPartyOrderNo != null">
        third_party_order_no = #{thirdPartyOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="promotionDiscounts != null">
        promotion_discounts = #{promotionDiscounts,jdbcType=INTEGER},
      </if>
      <if test="memberPlusDiscounts != null">
        member_plus_discounts = #{memberPlusDiscounts,jdbcType=INTEGER},
      </if>
      <if test="itemType != null">
        item_type = #{itemType,jdbcType=INTEGER},
      </if>
      <if test="wipeMoney != null">
        wipe_money = #{wipeMoney,jdbcType=INTEGER},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=INTEGER},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="totalOriginalAmount != null">
        total_original_amount = #{totalOriginalAmount,jdbcType=INTEGER},
      </if>
      <if test="promotionLabel != null">
        promotion_label = #{promotionLabel,jdbcType=VARCHAR},
      </if>
      <if test="cashier != null">
        cashier = #{cashier,jdbcType=VARCHAR},
      </if>
      <if test="cashierId != null">
        cashier_id = #{cashierId,jdbcType=BIGINT},
      </if>
      <if test="purchasePayAmount != null">
        purchase_pay_amount = #{purchasePayAmount,jdbcType=INTEGER},
      </if>
      <if test="orderSign != null">
        order_sign = #{orderSign,jdbcType=VARCHAR},
      </if>
      <if test="offlineCouponAmount != null">
        offline_coupon_amount = #{offlineCouponAmount,jdbcType=BIGINT},
      </if>
      <if test="pointsDiscountAmount != null">
        points_discount_amount = #{pointsDiscountAmount,jdbcType=BIGINT},
      </if>
      <if test="shiftId != null">
        shift_id = #{shiftId,jdbcType=BIGINT},
      </if>
      <if test="orderSource != null">
        order_source = #{orderSource,jdbcType=INTEGER},
      </if>
      <if test="presentPoints != null">
        present_points = #{presentPoints,jdbcType=BIGINT},
      </if>
      <if test="promotionInfo != null">
        promotion_info = #{promotionInfo,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where order_id = #{orderId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.cowell.purchase.entityTidb.OrderBaseOtter">
    update order_base_otter
    set type = #{type,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=BIGINT},
      business_id = #{businessId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      order_status = #{orderStatus,jdbcType=BIGINT},
      order_status_cur = #{orderStatusCur,jdbcType=INTEGER},
      pay_amount = #{payAmount,jdbcType=INTEGER},
      total_amount = #{totalAmount,jdbcType=INTEGER},
      coupon_amount = #{couponAmount,jdbcType=INTEGER},
      express_no = #{expressNo,jdbcType=VARCHAR},
      express_fee = #{expressFee,jdbcType=INTEGER},
      consignee_id = #{consigneeId,jdbcType=BIGINT},
      consignee = #{consignee,jdbcType=VARCHAR},
      consignee_phone = #{consigneePhone,jdbcType=VARCHAR},
      consignee_province = #{consigneeProvince,jdbcType=VARCHAR},
      consignee_city = #{consigneeCity,jdbcType=VARCHAR},
      consignee_area = #{consigneeArea,jdbcType=VARCHAR},
      consignee_address = #{consigneeAddress,jdbcType=VARCHAR},
      remarks = #{remarks,jdbcType=VARCHAR},
      pay_channel = #{payChannel,jdbcType=VARCHAR},
      refund_amount = #{refundAmount,jdbcType=INTEGER},
      refund_reason = #{refundReason,jdbcType=VARCHAR},
      refund_remarks = #{refundRemarks,jdbcType=VARCHAR},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      complete_time = #{completeTime,jdbcType=TIMESTAMP},
      refund_time = #{refundTime,jdbcType=TIMESTAMP},
      cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      pay_points = #{payPoints,jdbcType=INTEGER},
      third_party_order_no = #{thirdPartyOrderNo,jdbcType=VARCHAR},
      promotion_discounts = #{promotionDiscounts,jdbcType=INTEGER},
      member_plus_discounts = #{memberPlusDiscounts,jdbcType=INTEGER},
      item_type = #{itemType,jdbcType=INTEGER},
      wipe_money = #{wipeMoney,jdbcType=INTEGER},
      pay_type = #{payType,jdbcType=INTEGER},
      store_name = #{storeName,jdbcType=VARCHAR},
      total_original_amount = #{totalOriginalAmount,jdbcType=INTEGER},
      promotion_label = #{promotionLabel,jdbcType=VARCHAR},
      cashier = #{cashier,jdbcType=VARCHAR},
      cashier_id = #{cashierId,jdbcType=BIGINT},
      purchase_pay_amount = #{purchasePayAmount,jdbcType=INTEGER},
      order_sign = #{orderSign,jdbcType=VARCHAR},
      offline_coupon_amount = #{offlineCouponAmount,jdbcType=BIGINT},
      points_discount_amount = #{pointsDiscountAmount,jdbcType=BIGINT},
      shift_id = #{shiftId,jdbcType=BIGINT},
      order_source = #{orderSource,jdbcType=INTEGER},
      present_points = #{presentPoints,jdbcType=BIGINT},
      promotion_info = #{promotionInfo,jdbcType=LONGVARCHAR}
    where order_id = #{orderId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.purchase.entityTidb.OrderBaseOtter">
    update order_base_otter
    set type = #{type,jdbcType=INTEGER},
      user_id = #{userId,jdbcType=BIGINT},
      business_id = #{businessId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      order_status = #{orderStatus,jdbcType=BIGINT},
      order_status_cur = #{orderStatusCur,jdbcType=INTEGER},
      pay_amount = #{payAmount,jdbcType=INTEGER},
      total_amount = #{totalAmount,jdbcType=INTEGER},
      coupon_amount = #{couponAmount,jdbcType=INTEGER},
      express_no = #{expressNo,jdbcType=VARCHAR},
      express_fee = #{expressFee,jdbcType=INTEGER},
      consignee_id = #{consigneeId,jdbcType=BIGINT},
      consignee = #{consignee,jdbcType=VARCHAR},
      consignee_phone = #{consigneePhone,jdbcType=VARCHAR},
      consignee_province = #{consigneeProvince,jdbcType=VARCHAR},
      consignee_city = #{consigneeCity,jdbcType=VARCHAR},
      consignee_area = #{consigneeArea,jdbcType=VARCHAR},
      consignee_address = #{consigneeAddress,jdbcType=VARCHAR},
      remarks = #{remarks,jdbcType=VARCHAR},
      pay_channel = #{payChannel,jdbcType=VARCHAR},
      refund_amount = #{refundAmount,jdbcType=INTEGER},
      refund_reason = #{refundReason,jdbcType=VARCHAR},
      refund_remarks = #{refundRemarks,jdbcType=VARCHAR},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      complete_time = #{completeTime,jdbcType=TIMESTAMP},
      refund_time = #{refundTime,jdbcType=TIMESTAMP},
      cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      pay_points = #{payPoints,jdbcType=INTEGER},
      third_party_order_no = #{thirdPartyOrderNo,jdbcType=VARCHAR},
      promotion_discounts = #{promotionDiscounts,jdbcType=INTEGER},
      member_plus_discounts = #{memberPlusDiscounts,jdbcType=INTEGER},
      item_type = #{itemType,jdbcType=INTEGER},
      wipe_money = #{wipeMoney,jdbcType=INTEGER},
      pay_type = #{payType,jdbcType=INTEGER},
      store_name = #{storeName,jdbcType=VARCHAR},
      total_original_amount = #{totalOriginalAmount,jdbcType=INTEGER},
      promotion_label = #{promotionLabel,jdbcType=VARCHAR},
      cashier = #{cashier,jdbcType=VARCHAR},
      cashier_id = #{cashierId,jdbcType=BIGINT},
      purchase_pay_amount = #{purchasePayAmount,jdbcType=INTEGER},
      order_sign = #{orderSign,jdbcType=VARCHAR},
      offline_coupon_amount = #{offlineCouponAmount,jdbcType=BIGINT},
      points_discount_amount = #{pointsDiscountAmount,jdbcType=BIGINT},
      shift_id = #{shiftId,jdbcType=BIGINT},
      order_source = #{orderSource,jdbcType=INTEGER},
      present_points = #{presentPoints,jdbcType=BIGINT}
    where order_id = #{orderId,jdbcType=BIGINT}
  </update>
</mapper>