package com.cowell.purchase.util;

public class RestUtil {
//    private static RestTemplate restTemplate = new RestTemplate();
//    public static ResponseEntity<String> post(String url, String param, HttpMethod httpMethod){
//        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
//        HttpHeaders httpHeaders = new HttpHeaders();
//        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
//        HttpEntity httpEntity = new HttpEntity(param, httpHeaders);
//        return restTemplate.exchange(url, httpMethod, httpEntity, String.class);
//    }

}
