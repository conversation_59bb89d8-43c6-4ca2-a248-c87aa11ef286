//package com.cowell.purchase.util;
//
//import com.sap.conn.jco.*;
//import com.sap.conn.jco.ext.DestinationDataProvider;
//
//import java.io.File;
//import java.io.FileOutputStream;
//import java.io.IOException;
//import java.util.Properties;
//
//public class SapUtil {
//
//    //mvn install:install-file -Dfile=sapjco3.jar -Dpackaging=jar -DgroupId=com.sap -DartifactId=sapjco -Dversion=3.0
//    private static final String ABAP_AS_SAP = "ABAP_AS_SAP";
//    private static JCoDestination destination = null;
//    static{
//        Properties connectProperties = new Properties();
//        connectProperties.setProperty(DestinationDataProvider.JCO_ASHOST, "**********");//服务器
//        connectProperties.setProperty(DestinationDataProvider.JCO_SYSNR,  "00");        //系统编号
//        connectProperties.setProperty(DestinationDataProvider.JCO_CLIENT, "120");       //SAP集团
//        connectProperties.setProperty(DestinationDataProvider.JCO_USER,   "JCWEI");  //SAP用户名
//        connectProperties.setProperty(DestinationDataProvider.JCO_PASSWD, "201909");     //密码
//        connectProperties.setProperty(DestinationDataProvider.JCO_LANG,   "ZH");        //登录语言
//        connectProperties.setProperty(DestinationDataProvider.JCO_POOL_CAPACITY, "3");  //最大连接数
//        connectProperties.setProperty(DestinationDataProvider.JCO_PEAK_LIMIT, "10");     //最大连接线程
//
//        createDataFile(ABAP_AS_SAP, "jcoDestination", connectProperties);
//    }
//
//    /**
//     * 创建SAP接口属性文件。
//     * @param name  ABAP管道名称
//     * @param suffix  属性文件后缀
//     * @param properties  属性文件内容
//     */
//    private static void createDataFile(String name, String suffix, Properties properties){
//        File cfg = new File(name+"."+suffix);
//        if(cfg.exists()){
//            cfg.deleteOnExit();
//        }
//        FileOutputStream fos = null;
//        try{
//            fos = new FileOutputStream(cfg, false);
//            properties.store(fos, "for connection !");
//        }catch (Exception e){
//            throw new RuntimeException("Unable to create the destination file " + cfg.getName(), e);
//        }finally {
//            if(null != fos){
//                try {
//                    fos.close();
//                } catch (IOException e) {
//                    throw new RuntimeException(e);
//                }
//            }
//        }
//    }
//
//    /*
//     * * 获取SAP连接
//     *
//     * @return SAP连接对象
//     */
//    public static JCoDestination connect() {
//        try {
//            if(null == destination)
//                destination = JCoDestinationManager.getDestination(ABAP_AS_SAP);
//        } catch (JCoException e) {
//            System.out.println("Connect SAP fault, error msg: " + e.toString());
//        }
//        return destination;
//    }
//
//    public static void main(String[] args) {
//        JCoFunction function = null;
//        JCoDestination destination = SapUtil.connect();
//        int result=0;//调用接口返回状态
//        String message="";//调用接口返回信息
//        try {
//            function = destination.getRepository().getFunctionTemplate("ZRFC_DEMO").getFunction();
//            JCoParameterList input = function.getImportParameterList();
//            input.setValue("IV_BUKRS","32R0");//输入参数
////            input.setValue("I_ADD","X");
//            function.execute(destination);
////            result= function.getExportParameterList().getInt("RESULT");//调用接口返回结果
////            message= function.getExportParameterList().getString("MSG");//调用接口返回信息
//            JCoTable table =  function.getTableParameterList().getTable("OT_ITEM");
//            JCoMetaData jCoMetaData = table.getMetaData();
//            System.out.println(jCoMetaData);
//            for(int i = 0; i<table.getNumRows(); i++){
//                table.setRow(i);
//               System.out.println(table.getString("ZZZMDSQ"));
////               System.out.println(table.getString("IDAT2").equals("0000-00-00"));
//            }
//            System.out.println("调用返回结果--->"+result+";调用返回状态--->"+message);
//        }catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
////    "D:\Program Files\Java\jdk-1.8\bin\java.exe" -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:62095,suspend=y,server=n -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2023.1\captureAgent\debugger-agent.jar -Dfile.encoding=UTF-8 -classpath C:\Users\<USER>\AppData\Local\Temp\classpath2133347346.jar com.cowell.purchase.util.SapUtil
////    Connected to the target VM, address: '127.0.0.1:62095', transport: 'socket'
////    com.sap.conn.jco.JCoException: (102) JCO_ERROR_COMMUNICATION: Initialization of repository destination ABAP_AS_SAP failed: Connect to SAP gateway failed
////    Connection parameters: TYPE=A DEST=ABAP_AS_SAP ASHOST=********** SYSNR=00 PCS=1
////
////    LOCATION    CPIC (TCP/IP) on local host with Unicode
////    ERROR       partner '**********:3300' not reached
////    TIME        Sat Apr 27 22:51:47 2024
////    RELEASE     721
////    COMPONENT   NI (network interface)
////    VERSION     40
////    RC          -10
////    MODULE      nixxi.cpp
////    LINE        3289
////    DETAIL      NiPConnect2: **********:3300
////    SYSTEM CALL connect
////    ERRNO       10060
////    ERRNO TEXT  WSAETIMEDOUT: Connection timed out
////    COUNTER     2
////
////    at com.sap.conn.jco.rt.RfcDestination.getSystemID(RfcDestination.java:1100)
////    at com.sap.conn.jco.rt.RepositoryManager.getRepository(RepositoryManager.java:29)
////    at com.sap.conn.jco.rt.RfcDestination.initRepository(RfcDestination.java:1049)
////    at com.sap.conn.jco.rt.RfcDestination.getRepository(RfcDestination.java:1193)
////    at com.sap.conn.jco.rt.RfcDestination.getRepository(RfcDestination.java:33)
////    at com.cowell.purchase.util.SapUtil.main(SapUtil.java:79)
////    Caused by: com.sap.conn.jco.JCoException: (102) JCO_ERROR_COMMUNICATION: Connect to SAP gateway failed
////    Connection parameters: TYPE=A DEST=ABAP_AS_SAP ASHOST=********** SYSNR=00 PCS=1
////
////    LOCATION    CPIC (TCP/IP) on local host with Unicode
////    ERROR       partner '**********:3300' not reached
////    TIME        Sat Apr 27 22:51:47 2024
////    RELEASE     721
////    COMPONENT   NI (network interface)
////    VERSION     40
////    RC          -10
////    MODULE      nixxi.cpp
////    LINE        3289
////    DETAIL      NiPConnect2: **********:3300
////    SYSTEM CALL connect
////    ERRNO       10060
////    ERRNO TEXT  WSAETIMEDOUT: Connection timed out
////    COUNTER     2
////
////    at com.sap.conn.jco.rt.MiddlewareJavaRfc.generateJCoException(MiddlewareJavaRfc.java:625)
////    at com.sap.conn.jco.rt.MiddlewareJavaRfc$JavaRfcClient.connect(MiddlewareJavaRfc.java:1384)
////    at com.sap.conn.jco.rt.ClientConnection.connect(ClientConnection.java:704)
////    at com.sap.conn.jco.rt.RepositoryConnection.connect(RepositoryConnection.java:72)
////    at com.sap.conn.jco.rt.PoolingFactory.init(PoolingFactory.java:115)
////    at com.sap.conn.jco.rt.ConnectionManager.createFactory(ConnectionManager.java:349)
////    at com.sap.conn.jco.rt.DefaultConnectionManager.createFactory(DefaultConnectionManager.java:46)
////    at com.sap.conn.jco.rt.ConnectionManager.getFactory(ConnectionManager.java:323)
////    at com.sap.conn.jco.rt.RfcDestination.getSystemID(RfcDestination.java:1094)
////        ... 5 more
////    Caused by: RfcException:
////    message: Connect to SAP gateway failed
////    Connection parameters: TYPE=A DEST=ABAP_AS_SAP ASHOST=********** SYSNR=00 PCS=1
////
////    LOCATION    CPIC (TCP/IP) on local host with Unicode
////    ERROR       partner '**********:3300' not reached
////    TIME        Sat Apr 27 22:51:47 2024
////    RELEASE     721
////    COMPONENT   NI (network interface)
////    VERSION     40
////    RC          -10
////    MODULE      nixxi.cpp
////    LINE        3289
////    DETAIL      NiPConnect2: **********:3300
////    SYSTEM CALL connect
////    ERRNO       10060
////    ERRNO TEXT  WSAETIMEDOUT: Connection timed out
////    COUNTER     2
////
////        return code: RFC_FAILURE (1)
////    error group: 102
////    key: RFC_ERROR_COMMUNICATION
////    at com.sap.conn.rfc.engine.RfcIoControl.error_end(RfcIoControl.java:259)
////    at com.sap.conn.rfc.engine.RfcIoControl.ab_rfcopen(RfcIoControl.java:101)
////    at com.sap.conn.rfc.api.RfcApi.RfcOpen(RfcApi.java:81)
////    at com.sap.conn.jco.rt.MiddlewareJavaRfc$JavaRfcClient.connect(MiddlewareJavaRfc.java:1348)
////        ... 12 more
////    Disconnected from the target VM, address: '127.0.0.1:62095', transport: 'socket'
////
////    Process finished with exit code 0
//
//}
