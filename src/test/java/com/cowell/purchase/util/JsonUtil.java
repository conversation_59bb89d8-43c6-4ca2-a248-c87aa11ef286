package com.cowell.purchase.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.assertj.core.util.Lists;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/3/24
 */
public class JsonUtil {

    private static final  JsonUtil jsonUtil = new JsonUtil();

    public static final JsonUtil Builder() {
        return jsonUtil;
    }

    /**
     * json转map
     * @param jsonStr
     * @return
     */
    public Map<String, Object> json2Map(String jsonStr){
        Map<String, Object> map = new HashMap<>();
        JSONObject json = JSONObject.parseObject(jsonStr);
        for(Object key : json.keySet()){
            Object value = json.get(key);
            if(value instanceof JSONArray){
                List<Map<String, Object>> list = Lists.newArrayList();
                Iterator<Object> iterator = ((JSONArray) value).iterator();
                while (iterator.hasNext()){
                    Object next = iterator.next();
                    list.add(json2Map(next.toString()));
                }
                map.put(key.toString(), list);
            }else{
                map.put(key.toString(), value);
            }
        }
        return map;
    }
}
