package com.cowell.purchase.service;

import com.cowell.purchase.util.JsonUtil;
import com.cowell.purchase.util.OKHttpUtilForSelfSSL;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/3/24
 */
public class SopHttpTest {

    public static void main(String[] args) throws Exception {
        Map<String, String> map = new HashMap<>();
        map.put("Host", "entrusttest.com");

        OKHttpUtilForSelfSSL http = OKHttpUtilForSelfSSL.Builder();
        String codeUrl = "https://***************:8443/oauth2/authorize";
        String codeParam = "response_type=code&client_id=EntrustDistribution&provision_key=9331835a6850490b973de622385f89f4&authenticated_userid=xx";
        Object codePostResult = http.post(codeUrl, codeParam, map);

        Map<String, Object> codeResultMapForGetCode = JsonUtil.Builder().json2Map(codePostResult.toString());
        System.out.println("codeResult: "+codeResultMapForGetCode);
        String code = codeResultMapForGetCode.get("redirect_uri").toString().split("=")[1];
        System.out.println("code: "+code);

        String tokenUrl = "https://***************:8443/oauth2/token";
        String tokenParam = "grant_type=authorization_code&client_id=EntrustDistribution&client_secret=hzwmqe001001&code="+code;
        Object tokenPostResult = http.post(tokenUrl, tokenParam, map);
        System.out.println("tokenResult: "+tokenPostResult);

    }

}
