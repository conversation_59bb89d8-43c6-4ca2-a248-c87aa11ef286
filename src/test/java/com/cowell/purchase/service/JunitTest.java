package com.cowell.purchase.service;

import com.cowell.purchase.util.OKHttpUtilForSelfSSL;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.cloud.netflix.feign.EnableFeignClients;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2021/7/16
 */
@RunWith(SpringJUnit4ClassRunner.class)
//@SpringBootTest(classes = PurchaseApp.class)
//@EnableApolloConfig
@WebAppConfiguration
@EnableFeignClients
public class JunitTest {

    //国药-上传商品资料
    private static String goodsUrl = "https://api-lusu-test.gaojihealth.cn/guoyao-test/custsku/add";

    //国药-上传采购请货信息
    private static String planUrl = "https://api-lusu-test.gaojihealth.cn/guoyao-custpo/custpo/add";

    //国药-获取已出库单据
    private static String outUrl = "https://api-lusu-test.gaojihealth.cn/guoyao-list/postockin/list";

    //国药-获取发货信息
    private static String sendUrl = "https://api-lusu-test.gaojihealth.cn/guoyao-infobyid/postockin/infobyid";

    private static OKHttpUtilForSelfSSL http = OKHttpUtilForSelfSSL.Builder();

    @Test
    public void testGoods(){
        try {
            String goodsParam = "[{\"ApproveNbr\":\"国药准字H53020929\",\"IsProhibit\":0,\"PackageQty\":1,\"SkuFactory\":\"云南白药集团大理药业有限责任公司\",\"SkuID\":\"1018553\",\"SkuName\":\"乳酸菌素片\",\"SkuSpec\":\"0.4g*12片*3板\",\"SkuUnit\":\"盒\",\"TaxRate\":\"0.13\"},{\"ApproveNbr\":\"国药准字H20003795\",\"IsProhibit\":0,\"PackageQty\":1,\"SkuFactory\":\"杭州民生健康药业有限公司\",\"SkuID\":\"1000015\",\"SkuName\":\"(21金维他)多维元素片(21)\",\"SkuSpec\":\"60片\",\"SkuUnit\":\"瓶\",\"TaxRate\":\"0.13\"},{\"ApproveNbr\":\"国药准字H20045562\",\"IsProhibit\":0,\"PackageQty\":1,\"SkuFactory\":\"江苏远恒药业有限公司\",\"SkuID\":\"1000020\",\"SkuName\":\"氧氟沙星滴眼液\",\"SkuSpec\":\"8ml:24mg\",\"SkuUnit\":\"盒\",\"TaxRate\":\"0.13\"},{\"ApproveNbr\":\"国药准字B20021029\",\"IsProhibit\":0,\"PackageQty\":1,\"SkuFactory\":\"广西邦琪药业集团有限公司\",\"SkuID\":\"1000022\",\"SkuName\":\"复方双花藤止痒搽剂\",\"SkuSpec\":\"150ml\",\"SkuUnit\":\"瓶\",\"TaxRate\":\"0.13\"},{\"ApproveNbr\":\"国药准字Z22022453\",\"IsProhibit\":0,\"PackageQty\":1,\"SkuFactory\":\"吉林敖东延边药业股份有限公司\",\"SkuID\":\"1000048\",\"SkuName\":\"安神补脑液\",\"SkuSpec\":\"10ml*10支\",\"SkuUnit\":\"盒\",\"TaxRate\":\"0.13\"},{\"ApproveNbr\":\"国药准字HJ20150130/国药准字J20150097\",\"IsProhibit\":0,\"PackageQty\":1,\"SkuFactory\":\"昆明积大制药股份有限公司\",\"SkuID\":\"1000031\",\"SkuName\":\"双醋瑞因胶囊\",\"SkuSpec\":\"50mg*30粒\",\"SkuUnit\":\"盒\",\"TaxRate\":\"0.13\"},{\"ApproveNbr\":\"国药准字Z20040078\",\"IsProhibit\":0,\"PackageQty\":1,\"SkuFactory\":\"天士力医药集团股份有限公司\",\"SkuID\":\"1000045\",\"SkuName\":\"穿心莲内酯滴丸\",\"SkuSpec\":\"0.15g*12袋\",\"SkuUnit\":\"盒\",\"TaxRate\":\"0.13\"},{\"ApproveNbr\":\"国食健字G20040673\",\"IsProhibit\":0,\"PackageQty\":1,\"SkuFactory\":\"河北中诺果维康保健品有限公司/石药集团中诺药业(泰州)有限公司\",\"SkuID\":\"1011762\",\"SkuName\":\"(果维康)维生素C含片(苹果味)\",\"SkuSpec\":\"0.79g*60片\",\"SkuUnit\":\"盒\",\"TaxRate\":\"0.13\"},{\"ApproveNbr\":\"国药准字H20041114\",\"IsProhibit\":0,\"PackageQty\":1,\"SkuFactory\":\"广州白云山医药集团股份有限公司白云山制药总厂\",\"SkuID\":\"1004863\",\"SkuName\":\"阿莫西林克拉维酸钾片\",\"SkuSpec\":\"0.457g*8片\",\"SkuUnit\":\"盒\",\"TaxRate\":\"0.13\"}]";
            Object responseEntity = http.post(goodsUrl, goodsParam, Collections.emptyMap());
            System.out.println(responseEntity);
            //{"result":{"Result":null,"code":0,"date":"2021-08-05 13:34:36","desc":"成功","remark":null}}
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testPlan(){
        try {
            String goodsParam = "{\"PODate\":\"2021-08-02 00:00:00\",\"ECCustID\":\"20009\",\"PONbr\":\"PR00000188\",\"Items\":[{\"POPrice\":11.00,\"RowSeqID\":*********,\"SkuUnit\":\"盒\",\"TaxRate\":\"0.13\",\"ApproveNbr\":\"国药准字H20045562\",\"SkuName\":\"氧氟沙星滴眼液\",\"SkuFactory\":\"江苏远恒药业有限公司\",\"POQty\":10.000,\"SkuSpec\":\"8ml:24mg\",\"SkuID\":\"1000020\"},{\"POPrice\":25.00,\"RowSeqID\":*********,\"SkuUnit\":\"瓶\",\"TaxRate\":\"0.13\",\"ApproveNbr\":\"国药准字B20021029\",\"SkuName\":\"复方双花藤止痒搽剂\",\"SkuFactory\":\"广西邦琪药业集团有限公司\",\"POQty\":12.000,\"SkuSpec\":\"150ml\",\"SkuID\":\"1000022\"}],\"BranchCode\":\"ABAX\",\"CustVendorID\":\"10008576\"}";
            Object responseEntity = http.post(planUrl, goodsParam, Collections.emptyMap());
            System.out.println(responseEntity);
            //{"result":{"Result":null,"code":0,"date":"2021-08-05 13:35:14","desc":"成功","remark":null}}
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testOut(){
        try {
            //http://122.192.167.100:7676/postockin/list?access_token=828A5527E8624FC089A7C396E61967C9&eccustid=20009&ECCustID=20009&cvendorid=10008576&isneeddw=1&dtbegin=2021-07-29&dtend=2021-08-05&syncid=0
            String goodsParam = outUrl+"?eccustid=20009&ECCustID=20009&cvendorid=10008576&isneeddw=1&dtbegin=2021-07-29&dtend=2021-08-05&syncid=0";
            Object responseEntity = http.get(goodsParam, Collections.emptyMap());
            System.out.println(responseEntity);
            //{"result":{"code":0,"date":"2021-08-05 13:36:17","desc":"成功","remark":null,"Result":[{"Amount":null,"BillTypeID":0,"BranchCode":"ABAX","BranchName":null,"CreateDate":"2021-07-21 16:22:24","DownLoadDate":null,"DownLoadTimes":0,"ExtNumAttr1":null,"ExtNumAttr2":null,"ExtNumAttr3":null,"ExtNumAttr4":null,"ExtNumAttr5":null,"ExtStringAttr1":null,"ExtStringAttr2":null,"ExtStringAttr3":null,"ExtStringAttr4":null,"ExtStringAttr5":null,"HasProhibit":null,"HasTempSku":null,"ItemCount":null,"PONbr":"PR00000180","PrintCount":0,"PrintDate":null,"SODate":"2021-07-21 16:22:24","SONbr":"2021072115648","StatusCode":1,"StatusDesc":"已验证","SyncID":15648,"Vcomment":null,"VendorCustID":"1234"},{"Amount":null,"BillTypeID":0,"BranchCode":"AB8T","BranchName":null,"CreateDate":"2021-07-21 16:22:24","DownLoadDate":null,"DownLoadTimes":0,"ExtNumAttr1":null,"ExtNumAttr2":null,"ExtNumAttr3":null,"ExtNumAttr4":null,"ExtNumAttr5":null,"ExtStringAttr1":null,"ExtStringAttr2":null,"ExtStringAttr3":null,"ExtStringAttr4":null,"ExtStringAttr5":null,"HasProhibit":null,"HasTempSku":null,"ItemCount":null,"PONbr":"PR00000181","PrintCount":0,"PrintDate":null,"SODate":"2021-07-21 16:22:24","SONbr":"2021072115649","StatusCode":1,"StatusDesc":"已验证","SyncID":15649,"Vcomment":null,"VendorCustID":"1234"},{"Amount":null,"BillTypeID":0,"BranchCode":"ABAX","BranchName":null,"CreateDate":"2021-07-21 16:22:24","DownLoadDate":null,"DownLoadTimes":0,"ExtNumAttr1":null,"ExtNumAttr2":null,"ExtNumAttr3":null,"ExtNumAttr4":null,"ExtNumAttr5":null,"ExtStringAttr1":null,"ExtStringAttr2":null,"ExtStringAttr3":null,"ExtStringAttr4":null,"ExtStringAttr5":null,"HasProhibit":null,"HasTempSku":null,"ItemCount":null,"PONbr":"PR00000182","PrintCount":0,"PrintDate":null,"SODate":"2021-07-21 16:22:24","SONbr":"2021072115650","StatusCode":1,"StatusDesc":"已验证","SyncID":15650,"Vcomment":null,"VendorCustID":"1234"},{"Amount":null,"BillTypeID":0,"BranchCode":"ABAX","BranchName":null,"CreateDate":"2021-07-22 15:13:53","DownLoadDate":null,"DownLoadTimes":0,"ExtNumAttr1":null,"ExtNumAttr2":null,"ExtNumAttr3":null,"ExtNumAttr4":null,"ExtNumAttr5":null,"ExtStringAttr1":null,"ExtStringAttr2":null,"ExtStringAttr3":null,"ExtStringAttr4":null,"ExtStringAttr5":null,"HasProhibit":null,"HasTempSku":null,"ItemCount":null,"PONbr":"PR00000184","PrintCount":0,"PrintDate":null,"SODate":"2021-07-22 15:13:53","SONbr":"2021072215655","StatusCode":1,"StatusDesc":"已验证","SyncID":15655,"Vcomment":null,"VendorCustID":"1234"},{"Amount":null,"BillTypeID":0,"BranchCode":"ABAX","BranchName":null,"CreateDate":"2021-07-22 15:13:53","DownLoadDate":null,"DownLoadTimes":0,"ExtNumAttr1":null,"ExtNumAttr2":null,"ExtNumAttr3":null,"ExtNumAttr4":null,"ExtNumAttr5":null,"ExtStringAttr1":null,"ExtStringAttr2":null,"ExtStringAttr3":null,"ExtStringAttr4":null,"ExtStringAttr5":null,"HasProhibit":null,"HasTempSku":null,"ItemCount":null,"PONbr":"PR00000183","PrintCount":0,"PrintDate":null,"SODate":"2021-07-22 15:13:53","SONbr":"2021072215656","StatusCode":1,"StatusDesc":"已验证","SyncID":15656,"Vcomment":null,"VendorCustID":"1234"},{"Amount":null,"BillTypeID":0,"BranchCode":"ABAX","BranchName":null,"CreateDate":"2021-07-22 15:13:53","DownLoadDate":null,"DownLoadTimes":0,"ExtNumAttr1":null,"ExtNumAttr2":null,"ExtNumAttr3":null,"ExtNumAttr4":null,"ExtNumAttr5":null,"ExtStringAttr1":null,"ExtStringAttr2":null,"ExtStringAttr3":null,"ExtStringAttr4":null,"ExtStringAttr5":null,"HasProhibit":null,"HasTempSku":null,"ItemCount":null,"PONbr":"PR00000186","PrintCount":0,"PrintDate":null,"SODate":"2021-07-22 15:13:53","SONbr":"2021072215657","StatusCode":1,"StatusDesc":"已验证","SyncID":15657,"Vcomment":null,"VendorCustID":"1234"},{"Amount":null,"BillTypeID":0,"BranchCode":"ABAX","BranchName":null,"CreateDate":"2021-07-22 15:13:53","DownLoadDate":null,"DownLoadTimes":0,"ExtNumAttr1":null,"ExtNumAttr2":null,"ExtNumAttr3":null,"ExtNumAttr4":null,"ExtNumAttr5":null,"ExtStringAttr1":null,"ExtStringAttr2":null,"ExtStringAttr3":null,"ExtStringAttr4":null,"ExtStringAttr5":null,"HasProhibit":null,"HasTempSku":null,"ItemCount":null,"PONbr":"PR00000185","PrintCount":0,"PrintDate":null,"SODate":"2021-07-22 15:13:53","SONbr":"2021072215658","StatusCode":1,"StatusDesc":"已验证","SyncID":15658,"Vcomment":null,"VendorCustID":"1234"}]}}
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testSend(){
        try {
            //http://122.192.167.100:7676/postockin/infobyid?access_token=A10EA1A9F86348B68294B6A43C8250C7&eccustid=20009&ECCustID=20009&cvendorid=10008576&isneeddw=0&syncid=15648
            String goodsParam = sendUrl+"?eccustid=20009&ECCustID=20009&cvendorid=10008576&isneeddw=0&syncid=15648";
            Object responseEntity = http.get(goodsParam, Collections.emptyMap());
            System.out.println(responseEntity);
            //{"result":{"code":0,"date":"2021-08-05 13:37:07","desc":"成功","remark":null,"Result":{"Amount":431,"BillTypeID":0,"BranchCode":"ABAX","BranchName":null,"CreateDate":"2021-07-21 16:22:24","DownLoadDate":null,"DownLoadTimes":0,"ExtNumAttr1":null,"ExtNumAttr2":null,"ExtNumAttr3":null,"ExtNumAttr4":null,"ExtNumAttr5":null,"ExtStringAttr1":null,"ExtStringAttr2":null,"ExtStringAttr3":null,"ExtStringAttr4":null,"ExtStringAttr5":null,"HasProhibit":0,"HasTempSku":0,"ItemCount":2,"PONbr":"PR00000180","PrintCount":0,"PrintDate":null,"SODate":"2021-07-21 16:22:24","SONbr":"2021072115648","StatusCode":1,"StatusDesc":"已验证","SyncID":15648,"Vcomment":null,"VendorCustID":"1234","Items":[{"ApproveNbr":null,"BillAmount":189.70000000000002,"BillPrice":18.97,"ConvertRate":1,"ExpiryDate":"2022-12-31 09:38:24","ExtNumAttr1":null,"ExtNumAttr2":null,"ExtNumAttr3":null,"ExtNumAttr4":null,"ExtNumAttr5":null,"ExtStringAttr1":null,"ExtStringAttr2":null,"ExtStringAttr3":null,"ExtStringAttr4":null,"ExtStringAttr5":null,"IsProhibit":0,"IsTempSku":0,"POQty":10,"PORowSeq":*********,"PackageQty":1,"ProdDate":"2020-01-01 09:38:11","Qty":10,"RetialPrice":null,"RowSeqID":1,"SerialNbr":"20210621327","SettleAmount":189.70000000000002,"SettlePrice":18.97,"SkuFactory":null,"SkuID":"1000020","SkuName":null,"SkuSpec":null,"SkuUnit":null,"SyncID":15648,"TaxRate":0.16,"Vcomment":null,"VendorSkuID":null,"WholeSalePrice":null},{"ApproveNbr":null,"BillAmount":241.4,"BillPrice":12.07,"ConvertRate":1,"ExpiryDate":"2022-12-31 09:38:24","ExtNumAttr1":null,"ExtNumAttr2":null,"ExtNumAttr3":null,"ExtNumAttr4":null,"ExtNumAttr5":null,"ExtStringAttr1":null,"ExtStringAttr2":null,"ExtStringAttr3":null,"ExtStringAttr4":null,"ExtStringAttr5":null,"IsProhibit":0,"IsTempSku":0,"POQty":20,"PORowSeq":*********,"PackageQty":1,"ProdDate":"2020-01-01 09:38:11","Qty":20,"RetialPrice":null,"RowSeqID":2,"SerialNbr":"20210621225","SettleAmount":241.4,"SettlePrice":12.07,"SkuFactory":null,"SkuID":"1000022","SkuName":null,"SkuSpec":null,"SkuUnit":null,"SyncID":15648,"TaxRate":0.16,"Vcomment":null,"VendorSkuID":null,"WholeSalePrice":null}]}}}
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
