package com.cowell.purchase.service;

import com.alibaba.fastjson.JSON;
import com.cowell.purchase.PurchaseApp;
import com.cowell.purchase.service.dto.ReplenishmentInfo;
import com.cowell.purchase.service.param.ReplenishmentParam;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {PurchaseApp.class})
public class ReplenishmentTest {

    @Autowired
    private PurchaseReplenishmentService service;

    @Test
    public void getPurcahseTypeList() {
        List<ReplenishmentParam> goodsNoListParam = Lists.newArrayList();
        ReplenishmentParam param = new ReplenishmentParam();
        param.setGoodsCode("A1");
        param.setBoxSpecifications(new BigDecimal("20"));
        param.setMonthSaleCount(new BigDecimal(100));
        param.setNowDcStock(new BigDecimal("10"));
        param.setNowStoreStock(new BigDecimal(50));
        goodsNoListParam.add(param);


        List<ReplenishmentInfo> replenishmentInfoList = service.getReplenishmentNum(0L,0L,goodsNoListParam);
        System.out.println(JSON.toJSONString(replenishmentInfoList));
    }



}
