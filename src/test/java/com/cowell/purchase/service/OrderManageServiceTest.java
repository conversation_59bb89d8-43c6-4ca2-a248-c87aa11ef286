package com.cowell.purchase.service;

import com.cowell.purchase.PurchaseApp;
import com.cowell.purchase.enums.ConfirmStatusEnum;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.netflix.feign.EnableFeignClients;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

/**
 * @program: purchase
 * @description: 订单服务单元测试
 * @author: jmlu
 * @create: 2021-05-31 10:07
 **/

@RunWith(SpringRunner.class)
@SpringBootTest(classes = PurchaseApp.class)
@WebAppConfiguration
@EnableFeignClients
public class OrderManageServiceTest {

    @Autowired
    private OrderManageService orderManageService;

    @Test
    public void testGetCountKindGoodsByPurchaseOrderNos() {
        int size = orderManageService.getCountKindGoodsByPurchaseOrderNos(new String[] {"4500048052", "4500043855"});
        System.out.println("======10000003==== size:" + size);
           ;
    }
}
