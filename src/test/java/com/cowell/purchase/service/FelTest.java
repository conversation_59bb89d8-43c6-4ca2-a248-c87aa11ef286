package com.cowell.purchase.service;

import com.greenpineyu.fel.FelEngine;
import com.greenpineyu.fel.FelEngineImpl;
import com.greenpineyu.fel.context.FelContext;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collector;
import java.util.stream.Collectors;

public class FelTest {
class A {
    private Integer id;
    private String name;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

//    @Override
//    public boolean equals(Object o) {
//        if (this == o) return true;
//        if (o == null || getClass() != o.getClass()) return false;
//        A a = (A) o;
//        return Objects.equals(name, a.name);
//    }
//
//    @Override
//    public int hashCode() {
//        return Objects.hash(name);
//    }

    @Override
    public String toString() {
        return "A{" +
            "id=" + id +
            ", name='" + name + '\'' +
            '}';
    }
}
    public static void main(String[] args) {

        List<A> as = new ArrayList<>();
        for(int i = 0; i < 5; i++){
            A a = new FelTest().new A();
            int j = i;
            if(i % 2 == 0){
                j += 1;
            }
            a.setId(i);
            a.setName(String.valueOf(j));
            as.add(a);
        }
        System.out.println(as);
        System.out.println(as.stream().collect(Collectors.toSet()));
//        System.out.println(felFormulaChenes());
    }

    public static Object felCondition(){
        FelEngine fel = new FelEngineImpl();
        FelContext ctx = fel.getContext();
        ctx.set("n", 15);
        Object result = fel.eval("10 < n && n<20 ",ctx);
        return result;
    }

    public static Object felFormula(){
        FelEngine fel = new FelEngineImpl();
        FelContext ctx = fel.getContext();
        ctx.set("T", 1);
        ctx.set("L", 1);
        ctx.set("Q", 60);
        ctx.set("S", 1);
        ctx.set("K", 1);
        ctx.set("W", 1);
        Object result = fel.eval("(T+S+L)*Q/30-K-W ",ctx);
        return result;
    }

    public static Object felFormulaChenes(){
        FelEngine fel = new FelEngineImpl();
        FelContext ctx = fel.getContext();
        ctx.set("T", 1);
        ctx.set("L", 1);
        ctx.set("Q", 60);
        ctx.set("S", 1);
        ctx.set("现有仓存", 1);
        ctx.set("在途数量", 1);
        Object result = fel.eval("(T+S+L)*Q/30-现有仓存-在途数量 ",ctx);
        return result;
    }
}
