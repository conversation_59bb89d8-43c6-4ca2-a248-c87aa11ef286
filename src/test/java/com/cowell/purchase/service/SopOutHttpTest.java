package com.cowell.purchase.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.purchase.util.OKHttpUtilForSelfSSL;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/3/25
 */
public class SopOutHttpTest {
    public static void main(String[] args) throws Exception {

        OKHttpUtilForSelfSSL http = OKHttpUtilForSelfSSL.Builder();
        String codeUrl = "https://api-test-internal.gaojihealth.cn/sop-out/api/noauth/publish";
        String codeParam = "response_type=code&client_id=EntrustDistribution&provision_key=9331835a6850490b973de622385f89f4&authenticated_userid=xx";
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("response_type","code");
        paramMap.put("client_id","EntrustDistribution");
        paramMap.put("provision_key","9331835a6850490b973de622385f89f4");
        paramMap.put("authenticated_userid","xx");

        Map<String, String> commonParamMap = new HashMap<>();
        commonParamMap.put("scope","purchase");
        commonParamMap.put("api","authorize");
        commonParamMap.put("version","1.0");

        Map<String, String> finalParamMap = new HashMap<>();
        finalParamMap.put("pub", JSON.toJSONString(commonParamMap));
        finalParamMap.put("param", JSON.toJSONString(paramMap));
        Object codePostResult = http.post(codeUrl, JSON.toJSONString(finalParamMap));
        System.out.println(codePostResult);
    }
}
