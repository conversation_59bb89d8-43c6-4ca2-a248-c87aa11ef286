package com.cowell.purchase.service;

import com.alibaba.fastjson.JSONObject;
import com.cowell.purchase.PurchaseApp;
import com.cowell.purchase.service.feign.PublishService;
import com.cowell.sop.out.dto.PublicParam;
import com.cowell.sop.out.dto.PublishParamDto;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.sprite.vo.SingleResponse;

/**
 * Create by hfzhang
 *
 * @date 2021/3/25
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = PurchaseApp.class)
public class SopUnitTest {

    @Autowired
    private PublishService publishService;

    @Test
    public void remote(){

        try {
            PublishParamDto publishParamDto = new PublishParamDto();
            PublicParam publishParam = new PublicParam();
            publishParam.setScope("purchase");
            publishParam.setApi("authorize");
            publishParam.setVersion("1.0");
            JSONObject param = new JSONObject();
            param.put("response_type","code");
            param.put("client_id","EntrustDistribution");
            param.put("provision_key","9331835a6850490b973de622385f89f4");
            param.put("authenticated_userid","xx");
            publishParamDto.setPub(publishParam);
            publishParamDto.setParam(param);

            SingleResponse<JSONObject> publish = publishService.publish(publishParamDto);

            System.out.println(publish);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

}