package com.cowell.purchase.service;

import ch.ethz.ssh2.ChannelCondition;
import ch.ethz.ssh2.Connection;
import ch.ethz.ssh2.Session;
import ch.ethz.ssh2.StreamGobbler;
import org.apache.commons.io.IOUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;

/**
 * <AUTHOR>
 * @date 2020/3/13
 */
public class DemoGlueJobHandler {
    public static void main(String[] args) {
        Connection conn = null;
        InputStream stdOut = null;
        InputStream stdErr = null;
        String outStr = "";
        String outErr = "";
        int ret = -1;
        String ip = "***********";
        int port = 22;
        int TIME_OUT = 1000 * 5 * 60;
        String charset = Charset.defaultCharset().toString();
        String cmds = "sh "+"/home/<USER>/srm/test/test.sh ";
        try {
            System.out.println("进来了");
//            InnerRemoteShellExecutor exe = new InnerRemoteShellExecutor("***********",22,"devuser","9aoji_D@v");
//
//            // test.sh
//            System.out.println(exe.exec("sh "+"/home/<USER>/srm/test/test.sh "));

            conn = new Connection(ip,port);
            conn.connect();
//            boolean isConnect = conn.authenticateWithPassword("devuser", "9aoji_D@v");
            boolean isConnect = conn.authenticateWithPublicKey("devuser", new File("D:\\file\\id_rsa_pem_devuser_public"),null);

            if(isConnect){
                Session session = conn.openSession();
                session.execCommand(cmds);
                stdOut = new StreamGobbler(session.getStdout());

                byte[] buf1 = new byte[1024];
                StringBuilder sb1 = new StringBuilder();
                while (stdOut.read(buf1) != -1) {
                    sb1.append(new String(buf1, charset));
                }
                outStr = sb1.toString();
                stdErr = new StreamGobbler(session.getStderr());


                byte[] buf2 = new byte[1024];
                StringBuilder sb2 = new StringBuilder();
                while (stdErr.read(buf2) != -1) {
                    sb2.append(new String(buf2, charset));
                }
                outErr = sb2.toString();
                session.waitForCondition(ChannelCondition.EXIT_STATUS, TIME_OUT);
                ret = session.getExitStatus();
            }else{
                outStr = "远程主机登入失败--"+ip;
                throw new Exception("远程主机登入失败"+ip);
            }


        } catch (Exception e) {
            e.printStackTrace();
        }finally {
        if(conn != null){
            conn.close();
        }
        IOUtils.closeQuietly(stdOut);
        IOUtils.closeQuietly(stdErr);
    }

        System.out.println("outStr:"+outStr);
        System.out.println("outErr:"+outErr);
    }

//    public static String processStream(InputStream in, String charset) throws Exception {
//        byte[] buf = new byte[1024];
//        StringBuilder sb = new StringBuilder();
//        while (in.read(buf) != -1) {
//            sb.append(new String(buf, charset));
//        }
//        return sb.toString();
//    }

    public static class InnerRemoteShellExecutor{
        /**
         * 连接类
         */
        private Connection conn;
        /**
         * 远程机器IP
         */
        private String ip;
        /**
         * 远程机器端口
         */
        private int port;
        /**
         * 账号
         */
        private String userName;
        /**
         * 密码
         */
        private String password;

        private String charset = Charset.defaultCharset().toString();
        /**
         * 超时时间
         */
        private static final int TIME_OUT = 1000 * 5 * 60;

        /**
         * 构造函数
         * @param ip  远程主机IP地址
         * @param port  远程主机端口号
         * @param userName  账号
         * @param password  密码
         */
        public InnerRemoteShellExecutor(String ip,int port,String userName, String password) {
            this.ip = ip;
            this.userName = userName;
            this.password = password;
            this.port = port;
        }

        /**
         * 登入
         * @return
         * @throws IOException
         */
        private boolean login() throws Exception{
            conn = new Connection(ip,port);
            conn.connect();
            return conn.authenticateWithPassword(userName, password);
        }

        /**
         * 执行脚本
         * @param cmds
         * @return
         */
        public String exec(String cmds) throws Exception{
            InputStream stdOut = null;
            InputStream stdErr = null;
            String outStr = "";
            String outErr = "";
            int ret = -1;
            try{
                if(login()){
                    Session session = conn.openSession();
                    session.execCommand(cmds);
                    stdOut = new StreamGobbler(session.getStdout());
                    outStr = processStream(stdOut, charset);
                    stdErr = new StreamGobbler(session.getStderr());
                    outErr = processStream(stdErr, charset);
                    session.waitForCondition(ChannelCondition.EXIT_STATUS, TIME_OUT);
                    ret = session.getExitStatus();
                }else{
                    outStr = "远程主机登入失败--"+ip;
                    throw new Exception("远程主机登入失败"+ip);
                }
            }catch (Exception e){
                e.printStackTrace();
            }finally {
                if(conn != null){
                    conn.close();
                }
                IOUtils.closeQuietly(stdOut);
                IOUtils.closeQuietly(stdErr);
            }
            //返回执行结果
            return outStr;
        }

        private String processStream(InputStream in, String charset) throws Exception {
            byte[] buf = new byte[1024];
            StringBuilder sb = new StringBuilder();
            while (in.read(buf) != -1) {
                sb.append(new String(buf, charset));
            }
            return sb.toString();
        }
    }

}
