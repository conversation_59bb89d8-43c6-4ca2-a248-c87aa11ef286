package com.cowell.purchase.service;/**
 * Create by hfzhang
 *
 * @date 2019/1/10
 */

import com.cowell.purchase.PurchaseApp;
import com.cowell.purchase.service.feign.ErpSaasService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2019/1/10
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {PurchaseApp.class})
public class PurchaseSapTest {

    @Autowired
    private ErpSaasService erpSaasService;
    @Autowired
    private SapService sapService;

    @Test
    public void sendSap(){
        try {

            //PO-622-190110-0008   STO
            //PO-622-190110-0007    SO
            sapService.retryPurchaseOrderToSap("PO-59-190110-0002");
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
