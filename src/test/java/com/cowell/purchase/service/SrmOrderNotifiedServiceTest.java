package com.cowell.purchase.service;

import com.cowell.purchase.PurchaseApp;
import com.cowell.purchase.service.SrmOrderNotifiedService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @program: purchase
 * @description: 订单微信通知服务测试
 * @author: jmlu
 * @create: 2021-06-01 13:42
 **/

@SpringBootTest(classes = PurchaseApp.class)
@RunWith(SpringRunner.class)
public class SrmOrderNotifiedServiceTest {

    @Autowired
    private SrmOrderNotifiedService srmOrderNotifiedService;


    @Test
    public void testIsExist() {
        System.out.println(srmOrderNotifiedService.isExist("o5UuA0T2-NS1TAcd4U0F6ilrIrW0", "4500388312"));

    }

}
