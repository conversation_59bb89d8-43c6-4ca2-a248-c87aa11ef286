package com.cowell.purchase.service;

import com.cowell.purchase.PurchaseApp;
import com.cowell.purchase.config.TsscConstants;
import com.cowell.purchase.service.dto.TokenUserDTO;
import com.cowell.purchase.service.dto.settle.AmountDto;
import com.cowell.purchase.service.param.settle.SettleOrderCreateDeliveryParam;
import com.cowell.purchase.service.param.settle.SettleOrderCreateParam;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.netflix.feign.EnableFeignClients;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = PurchaseApp.class)
@WebAppConfiguration
@EnableFeignClients
public class SettleManaServiceTest {
    @Autowired
    private SettleManageService settleManageService;
    @Autowired
    private RedissonClient redission;

    @Test
    public void preSaveAndSubmint(){
        SettleOrderCreateParam createParam = new SettleOrderCreateParam();
        List<SettleOrderCreateDeliveryParam> inStockVoListParam = Lists.newArrayList();
        List<SettleOrderCreateDeliveryParam> returnStockVoListParam = Lists.newArrayList();
        SettleOrderCreateDeliveryParam inStock = new SettleOrderCreateDeliveryParam();
        inStock.setDeductFromCount("2.000");
        inStock.setDeliveryKey("5001094347_3_2021");
        inStockVoListParam.add(inStock);
        SettleOrderCreateDeliveryParam outStock = new SettleOrderCreateDeliveryParam();
        outStock.setDeductFromCount("1.000");
        outStock.setDeliveryKey("5001094348_3_2021");
        outStock.setRedRush("1");
        returnStockVoListParam.add(outStock);

        createParam.setInStockVoListParam(inStockVoListParam);
        createParam.setReturnStockVoListParam(returnStockVoListParam);

        TokenUserDTO userDTO = new TokenUserDTO();
        userDTO.setUserId(166296309120000L);
        userDTO.setBindingValue("10000003");
        AmountDto amountDto = settleManageService.preSaveAndSubmint(createParam, userDTO);
        System.out.println(amountDto);
    }

    @Test
    public void saveAndSubmit(){

    }

    @Test
    public void redissionTest(){
        long l = System.currentTimeMillis();
        String bguid_ = String.format("%s-%s", "ERP-SAAS", UUID.randomUUID().toString());
        String bguid = StringUtils.replace(bguid_, "-", "");
        RMap<String, String> redissionMap = redission.getMap(TsscConstants.REDISSON_KEY_PULL_INVOICE);
        String requset = redissionMap.get(bguid);
        System.out.println(requset);
        System.out.println("耗时："+(System.currentTimeMillis()-l));
    }
}
