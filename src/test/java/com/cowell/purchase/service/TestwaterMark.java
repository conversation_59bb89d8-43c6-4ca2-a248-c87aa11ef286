package com.cowell.purchase.service;

import java.awt.FontMetrics;
import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.OutputStream;

import javax.swing.JLabel;

import com.cowell.purchase.PurchaseApp;
import com.itextpdf.text.Element;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfGState;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {PurchaseApp.class})
public class TestwaterMark {
    private static int interval = -5;

    @Autowired
    private ITencentCOSService cosService;

    @Test
    public void waterMark() {
        try {
            PdfReader reader = new PdfReader(cosService.downloadFileStream("https://b2b-test-1256841541.cos.ap-beijing.myqcloud.com/EXECUTE-IMG/execute_order_/pdf_test.pdf"));
            OutputStream outputStream = new FileOutputStream("/Users/<USER>/desktop/test_out.pdf");
            PdfStamper stamper = new PdfStamper(reader, outputStream);
//            OutputStream outputStream = new ByteArrayOutputStream();
//            PdfStamper stamper = new PdfStamper(reader, outputStream);

            BaseFont base = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",   BaseFont.EMBEDDED);

            Rectangle pageRect = null;
            PdfGState gs = new PdfGState();
            gs.setFillOpacity(0.3f);
            gs.setStrokeOpacity(0.4f);
            int total = reader.getNumberOfPages() + 1;

            JLabel label = new JLabel();
            FontMetrics metrics;
            int textH = 0;
            int textW = 0;
            label.setText("我是黑豆");
            metrics = label.getFontMetrics(label.getFont());
            textH = metrics.getHeight();
            textW = metrics.stringWidth(label.getText());

            PdfContentByte under;
            for (int i = 1; i < total; i++) {
                pageRect = reader.getPageSizeWithRotation(i);
                under = stamper.getOverContent(i);
                under.saveState();
                under.setGState(gs);
                under.beginText();
                under.setFontAndSize(base, 20);

                // 水印文字成30度角倾斜
                //你可以随心所欲的改你自己想要的角度
                for (int height = interval + textH; height < pageRect.getHeight();
                     height = height + textH*3) {
                    for (int width = interval + textW; width < pageRect.getWidth() + textW;
                         width = width + textW*2) {
                        under.showTextAligned(Element.ALIGN_LEFT
                            , "我是黑豆", width - textW,
                            height - textH, 30);
                    }
                }
                // 添加水印文字
                under.endText();
                byte[] buffer = under.getInternalBuffer().getBuffer();
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream(buffer.length);
                byteArrayOutputStream.write(buffer);
                System.out.println(outputStream);
            }
            //说三遍
            //一定不要忘记关闭流
            //一定不要忘记关闭流
            //一定不要忘记关闭流
            stamper.close();
            reader.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
//        new TestwaterMark().waterMark("/Users/<USER>/desktop/pdf_test.pdf", "/Users/<USER>/desktop/test_out.pdf", "我是黑豆");

    }

}
