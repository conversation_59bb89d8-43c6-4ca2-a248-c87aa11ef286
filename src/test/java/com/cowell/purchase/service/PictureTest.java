package com.cowell.purchase.service;

import com.cowell.framework.utils.img.ImgUtils;
import com.cowell.framework.utils.stream.StreamUtils;
import com.cowell.purchase.PurchaseApp;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.imageio.ImageIO;
import javax.swing.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;

//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = {PurchaseApp.class})
public class PictureTest {
    @Autowired
    private ITencentCOSService cosService;

    public static void main(String[] args) {
//        getWatermarkImg();
        new PictureTest().addWatermark("/Users/<USER>/desktop/111.png", "/Users/<USER>/desktop/after.jpg", "madong111234！", "png");
    }
//    @Test
    public static void getWatermarkImg(){
        String sourceUrl = "https://gjscrm-1256038144.cos.ap-beijing.myqcloud.com/EXECUTE-IMG/execute_order_/ceshishuiyin";
//        InputStream sourceIn = cosService.downloadFileStream(sourceUrl);
        OutputStream sourceOut = new ByteArrayOutputStream();
        try {
            InputStream sourceIn = new FileInputStream(new File("/Users/<USER>/desktop/111.png"));
            //添加水印
            sourceOut = ImgUtils.pressText(sourceIn, "watermarkName",
                "宋体", Font.BOLD, 40, Color.WHITE, 36, 150, 1f);
            sourceIn = StreamUtils.parse(sourceOut);
            byte[] data = readInputStream(sourceIn);
            File imageFile = new File("/Users/<USER>/desktop/after.jpg");
            //创建输出流
            FileOutputStream outStream = new FileOutputStream(imageFile);
            //写入数据
            outStream.write(data);
            //关闭输出流
            outStream.close();
        }catch (Exception e){

        }finally {
            try {
                sourceOut.close();
            } catch (IOException e) {
            }
        }
    }

    public static byte[] readInputStream(InputStream inStream) throws Exception{
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
//创建一个Buffer字符串
        byte[] buffer = new byte[1024];
//每次读取的字符串长度，如果为-1，代表全部读取完毕
        int len = 0;
//使用一个输入流从buffer里把数据读取出来
        while( (len=inStream.read(buffer)) != -1 ){
//用输出流往buffer里写入数据，中间参数代表从哪个位置开始读，len代表读取的长度
            outStream.write(buffer, 0, len);
        }
//关闭输入流
        inStream.close();
//把outStream里的数据写入内存
        return outStream.toByteArray();
    }

    public void addWatermark(String sourceImgPath, String tarImgPath, String waterMarkContent,String fileExt){
        Font font = new Font("宋体", Font.BOLD, 12);//水印字体，大小
        Color markContentColor = Color.white;//水印颜色
        Integer degree = 45;//设置水印文字的旋转角度
        float alpha = 0.5f;//设置水印透明度
        OutputStream outImgStream = null;
        try {
            InputStream sourceIn = new FileInputStream(new File("/Users/<USER>/desktop/111.png"));
            Image srcImg = ImageIO.read(sourceIn);//文件转化为图片
            int srcImgWidth = srcImg.getWidth(null);//获取图片的宽
            int srcImgHeight = srcImg.getHeight(null);//获取图片的高
            // 加水印
            BufferedImage bufImg = new BufferedImage(srcImgWidth, srcImgHeight, BufferedImage.TYPE_INT_RGB);
            Graphics2D g = bufImg.createGraphics();//得到画笔
            g.drawImage(srcImg, 0, 0, srcImgWidth, srcImgHeight, null);
            g.setColor(markContentColor); //设置水印颜色
            g.setFont(font);              //设置字体
            g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, alpha));//设置水印文字透明度
            if (null != degree) {
                g.rotate(Math.toRadians(degree));//设置水印旋转
            }
            JLabel label = new JLabel(waterMarkContent);
            FontMetrics metrics = label.getFontMetrics(font);
            int width = metrics.stringWidth(label.getText());//文字水印的宽
            int rowsNumber = srcImgHeight/width;// 图片的高  除以  文字水印的宽    ——> 打印的行数(以文字水印的宽为间隔)
            int columnsNumber = srcImgWidth/width;//图片的宽 除以 文字水印的宽   ——> 每行打印的列数(以文字水印的宽为间隔)
            //防止图片太小而文字水印太长，所以至少打印一次
            if(rowsNumber < 1){
                rowsNumber = 1;
            }
            if(columnsNumber < 1){
                columnsNumber = 1;
            }
            for(int j=0;j<rowsNumber*3;j++){
                for(int i=0;i<columnsNumber*3;i++){
                    g.drawString(waterMarkContent, (i*width + j*width)/2, (-i*width + j*width)/2);//画出水印,并设置水印位置
                }
            }
            g.dispose();// 释放资源
            // 输出图片
            outImgStream = new FileOutputStream(tarImgPath);
            ImageIO.write(bufImg, fileExt, outImgStream);
        } catch (Exception e) {
            e.printStackTrace();
            e.getMessage();
        } finally{
            try {
                if(outImgStream != null){
                    outImgStream.flush();
                    outImgStream.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
                e.getMessage();
            }
        }
    }


}
