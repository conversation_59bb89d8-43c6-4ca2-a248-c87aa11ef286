package com.cowell.purchase.service;

import com.cowell.purchase.PurchaseApp;
import com.cowell.purchase.service.dto.TokenUserDTO;
import com.cowell.purchase.service.param.ReserveOrderSearchParam;
import jdk.nashorn.internal.parser.Token;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @program: purchase
 * @description: 预约单单元测试
 * @author: jmlu
 * @create: 2021-08-19 16:23
 **/

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {PurchaseApp.class} )
public class ReserveServiceTest {

    @Autowired
    private ReserveService reserveService;

    @Test
    public void testListReserveOrders() {
        TokenUserDTO userDTO = new TokenUserDTO();
        userDTO.setBindingValue("10023642");
        ReserveOrderSearchParam searchParam = new ReserveOrderSearchParam();
        searchParam.setPage(0);
        searchParam.setPageSize(20);
        reserveService.listReserveOrders(userDTO, searchParam);
    }
}
