package com.cowell.purchase.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.purchase.PurchaseApp;
import com.cowell.purchase.entity.PurchaseDictionary;
import com.cowell.purchase.rest.vo.*;
import com.cowell.purchase.service.dto.TokenUserDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {PurchaseApp.class})
public class PurchaseConfigServiceTest {
    @Autowired
    private PurchaseConfigService purchaseConfigService;

    @Test
    public void setUnifiedPurchaseConfigTest() {
        UnifiedPurchaseConfigRequest request = new UnifiedPurchaseConfigRequest();
//        request.setId(1L);
        request.setUseBdp(1);
        request.setOrgId(36L);
        request.setOrgName("测试药店002");
        UnifiedPurchaseConfigInput config = new UnifiedPurchaseConfigInput();
//        config.setUseBdp(false);
        config.setCommissionerApprovalDay(2);
        config.setDelivertyDay(3);
        config.setOnSaleDay(5);
        config.setPaymentApprovalDay(3);
//        config.setPurchaseIntervalDay(1);
        List<FormulaSetting> formulaSetting = new ArrayList<>();
        FormulaSetting setting01 = new FormulaSetting();
//        setting01.setKey("one");
        setting01.setSaleCount("2000~");
        setting01.setSafeDay(11);
        setting01.setFormulaB("(S+T)*Q/30");
        setting01.setFormulaD("(T+S+L)*Q/30-现有仓存-在途数量");
        formulaSetting.add(setting01);
        FormulaSetting setting02 = new FormulaSetting();
//        setting02.setKey("two");
        setting02.setSaleCount("500~2000");
        setting02.setSafeDay(21);
        setting02.setFormulaB("(S+T)*Q/30");
        setting02.setFormulaD("(T+S+L)*Q/30-现有仓存-在途数量");
        formulaSetting.add(setting02);
        FormulaSetting setting03 = new FormulaSetting();
//        setting03.setKey("three");
        setting03.setSaleCount("100~500");
        setting03.setSafeDay(31);
        setting03.setFormulaB("(S+T)*Q/30");
        setting03.setFormulaD("(T+S+L)*Q/30-现有仓存-在途数量");
        formulaSetting.add(setting03);
        FormulaSetting setting04 = new FormulaSetting();
//        setting04.setKey("four");
        setting04.setSaleCount("50~100");
        setting04.setSafeDay(41);
        setting04.setFormulaB("(S+T)*Q/30");
        setting04.setFormulaD("Max(Q/30*(30+T)-现有仓存-在途数量，箱规)");
        formulaSetting.add(setting04);
        FormulaSetting setting05 = new FormulaSetting();
//        setting05.setKey("five");
        setting05.setSaleCount("0~50");
        setting05.setSafeDay(51);
        setting05.setFormulaB("(S+T)*Q/30");
        setting05.setFormulaD("Max(Q/30*(30+T)-现有仓存-在途数量，箱规)");
        formulaSetting.add(setting05);
//        config.setFormulaSetting(formulaSetting);
        request.setUnifiedPurchaseConfig(config);
        TokenUserDTO userDTO = new TokenUserDTO();
        userDTO.setUserId(12345678L);
        userDTO.setUserName("测试36");
        String result = purchaseConfigService.setUnifiedPurchaseConfig(request, userDTO);
        System.out.println("result:" + result);
    }

    @Test
    public void getUnifiedPurchaseConfigTest(){
        UnifiedPurchaseConfigVO configVO = purchaseConfigService.getUnifiedPurchaseConfig(59L);
        System.out.println("result:" + JSONObject.toJSONString(configVO));
    }

    @Test
    public void getUnifiedPurchaseConfigListTest(){
        PurchaseConfigPageRequest page = new PurchaseConfigPageRequest();
        page.setPage(0);
        page.setPageSize(1);
        page.setOrgName("狮城");
        PurchaseConfigPageResponse configVO = purchaseConfigService.getUnifiedPurchaseConfigList(page);
        System.out.println("result:" + JSONObject.toJSONString(configVO));
    }

    @Test
    public void initializePurchaseConfigBatchTest(){
        String configVO = purchaseConfigService.initializePurchaseConfigBatch();
        System.out.println("result:" + configVO);
    }

    @Test
    public void initializePurchaseConfigTest(){
        String configVO = purchaseConfigService.initializePurchaseConfig(622L, "假芝心", false, false);
        System.out.println("result:" + configVO);
    }

    @Test
    public void deletePurchaseConfig(){
        String configVO = purchaseConfigService.deletePurchaseConfig(196L);
        System.out.println("result:" + configVO);
    }

    @Test
    public void setSupplierConfig(){
        PurchaseSupplierEditRequest request = new PurchaseSupplierEditRequest();
        request.setOrgId(59L);
        request.setOrgName("瑞康");
        request.setSupplierNo("10001272");
        request.setSupplierLeadTime(10);
        request.setSupplierName("汤臣倍健药业有限公司");
        List<PurchaseIntervalDayInput> intervalDayInfoList = new ArrayList<>();
        PurchaseIntervalDayInput dictionary01 = new PurchaseIntervalDayInput();
        dictionary01.setIntervalDay(1);
        dictionary01.setKey("one");
        intervalDayInfoList.add(dictionary01);
        PurchaseIntervalDayInput dictionary02 = new PurchaseIntervalDayInput();
        dictionary02.setIntervalDay(2);
        dictionary02.setKey("two");
        intervalDayInfoList.add(dictionary02);
        PurchaseIntervalDayInput dictionary03 = new PurchaseIntervalDayInput();
        dictionary03.setIntervalDay(3);
        dictionary03.setKey("three");
        intervalDayInfoList.add(dictionary03);
        PurchaseIntervalDayInput dictionary04 = new PurchaseIntervalDayInput();
        dictionary04.setIntervalDay(4);
        dictionary04.setKey("four");
        intervalDayInfoList.add(dictionary04);
        request.setIntervalDayInfoList(intervalDayInfoList);
        TokenUserDTO userDTO = new TokenUserDTO();
        userDTO.setName("小丁");
        userDTO.setUserId(123456L);
        String configVO = purchaseConfigService.setSupplierConfig(request, userDTO);
        System.out.println("result:" + configVO);
    }

//    @Test
//    public void updateSupplierConfig(){
//        PurchaseSupplierConfig request = new PurchaseSupplierConfig();
//        request.setOrgId(59L);
//        request.setOrgName("瑞康");
//        request.setSupplierNo("10001272");
//        request.setSupplierLeadTime(10);
//        request.setSupplierName("汤臣倍健药业有限公司");
//        List<PurchaseIntervalDayInfo> intervalDayInfoList = new ArrayList<>();
//        PurchaseIntervalDayInfo dictionary01 = new PurchaseIntervalDayInfo();
//        dictionary01.setIntervalDay(1);
//        dictionary01.setSalesCount("2000~");
//        intervalDayInfoList.add(dictionary01);
//        PurchaseIntervalDayInfo dictionary02 = new PurchaseIntervalDayInfo();
//        dictionary02.setIntervalDay(2);
//        dictionary02.setSalesCount("500~2000");
//        intervalDayInfoList.add(dictionary02);
//        PurchaseIntervalDayInfo dictionary03 = new PurchaseIntervalDayInfo();
//        dictionary03.setIntervalDay(3);
//        dictionary03.setSalesCount("100~500");
//        intervalDayInfoList.add(dictionary03);
//        PurchaseIntervalDayInfo dictionary04 = new PurchaseIntervalDayInfo();
//        dictionary04.setIntervalDay(4);
//        dictionary04.setSalesCount("1~100");
//        intervalDayInfoList.add(dictionary04);
//        request.setIntervalDayInfoList(intervalDayInfoList);
//        TokenUserDTO userDTO = new TokenUserDTO();
//        userDTO.setName("小丁");
//        userDTO.setUserId(123456L);
//        String configVO = purchaseConfigService.setSupplierConfig(request, userDTO);
//        System.out.println("result:" + configVO);
//    }

    @Test
    public void getUnifiedPurchaseConfigList(){
        SupplierConfigPageRequest request = new SupplierConfigPageRequest();
        request.setOrgId(59L);
        request.setPage(0);
        request.setPageSize(1);
        request.setSupplierName("汤臣");
        PurchaseSupplierConfigResponse configVO = purchaseConfigService.getSupplierConfigList(request);
        System.out.println("result:" + JSONObject.toJSONString(configVO));
    }

    @Test
    public void getSupplierConfigById(){
        PurchaseSupplierConfig configVO = purchaseConfigService.getSupplierConfigById(201L, 59L);
        System.out.println("result:" + JSONObject.toJSONString(configVO));
    }

    @Test
    public void setSalesSectionConfig(){
        PurchaseSalesSectionConfig request = new PurchaseSalesSectionConfig();
        request.setOrgId(2892L);
        request.setOrgName("瑞澄大药房");
        List<SalesSectionInfo> salesSectionInfoList = new ArrayList<>();
        SalesSectionInfo info01 = new SalesSectionInfo();
        info01.setKey("one");
        info01.setSaleCount("0~200");
        salesSectionInfoList.add(info01);

        SalesSectionInfo info02 = new SalesSectionInfo();
        info02.setKey("two");
        info02.setSaleCount("200~500");
        salesSectionInfoList.add(info02);

        SalesSectionInfo info03 = new SalesSectionInfo();
        info03.setKey("three");
        info03.setSaleCount("500~1200");
        salesSectionInfoList.add(info03);

        SalesSectionInfo info04 = new SalesSectionInfo();
        info04.setKey("four");
        info04.setSaleCount("1200~");
        salesSectionInfoList.add(info04);
        request.setSalesSectionInfoList(salesSectionInfoList);

        TokenUserDTO userDTO = new TokenUserDTO();
        userDTO.setUserId(1234567L);
        userDTO.setName("小丁");

        String configVO = purchaseConfigService.setSalesSectionConfig(request, userDTO);
        System.out.println("result:" + JSONObject.toJSONString(configVO));
    }

    @Test
    public void refreshPurchaseConfig() {
        purchaseConfigService.refreshPurchaseConfig(true,true,true, "", "");
    }
}
