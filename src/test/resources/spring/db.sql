CREATE TABLE `pos_record_base` (
  `pos_record_id` bigint(32) NOT NULL COMMENT '顾客基本信息id',
  `order_id` bigint(32) NOT NULL COMMENT '订单号',
  `user_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '用户id',
  `business_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '连锁店ID',
  `store_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '门店ID',
  `type` tinyint(3) NOT NULL DEFAULT 0 COMMENT '类型(1、特管药品信息 2、处方信息)',
  `record_status` tinyint(3) NOT NULL DEFAULT 0 COMMENT '是否登记(0、新建 1、未登记  2、已登记)',
  `customer` varchar(32) NOT NULL DEFAULT '' COMMENT '顾客姓名',
  `customer_identity` varchar(32) NOT NULL DEFAULT '' COMMENT '顾客身份证',
  `customer_sex` tinyint(3) NOT NULL DEFAULT 0 COMMENT '顾客性别:1=男 2=女 0=未登记',
  `customer_address` varchar(256) NOT NULL DEFAULT '' COMMENT '顾客地址',
  `customer_tel` varchar(16) NOT NULL DEFAULT '' COMMENT '顾客联系方式',
  `record_time` timestamp NULL COMMENT '登记时间',
  `status` tinyint(3) NOT NULL DEFAULT 0 COMMENT '状态(-1删除,0正常)',
  `gmt_create` timestamp COMMENT '创建时间',
  `gmt_update` timestamp COMMENT '更新时间',
  `extend` varchar(3000) NULL COMMENT '扩展字段',
  `version` int(11) NOT NULL DEFAULT 0 COMMENT '版本号',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  `created_by_name` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人name',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人ID',
  PRIMARY KEY (`pos_record_id`)
);


CREATE TABLE `pos_prescription_info` (
  `pos_record_id` bigint(32) NOT NULL COMMENT '顾客基本信息id',
  `prescription_no` varchar(128) NOT NULL DEFAULT '' COMMENT '处方单号，可选登记信息',
  `prescription_date` date DEFAULT NULL COMMENT '处方日期',
  `prescription_type` int(11) NOT NULL DEFAULT 0 COMMENT '处方类型：1=西药方 2=中/西药方 3=中药方',
  `hospital` varchar(64) NOT NULL DEFAULT '' COMMENT '医疗机构名称',
  `doctor` varchar(32) NOT NULL DEFAULT '' COMMENT '医生姓名',
  `customer_age` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '顾客年龄',
  `diagnosis` varchar(256) NOT NULL DEFAULT '' COMMENT '诊断结果',
  `instruction` varchar(256) NOT NULL DEFAULT '' COMMENT '医嘱',
  `pharmacist_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '审方药师ID',
  `pharmacist_name` varchar(32) NOT NULL DEFAULT '' COMMENT '审方药师name',
  `dispenser_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '调配员ID',
  `dispenser_name` varchar(32) NOT NULL DEFAULT '' COMMENT '调配员name',
  `reviewer_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '复核员ID',
  `reviewer_name` varchar(32) NOT NULL DEFAULT 0 COMMENT '复核员name',
  `gmt_create` timestamp COMMENT '创建时间',
  `gmt_update` timestamp COMMENT '更新时间',
  `extend` varchar(3000) NULL COMMENT '扩展字段',
  `version` int(11) NOT NULL DEFAULT 0 COMMENT '版本号',
  PRIMARY KEY (`pos_record_id`)
);

CREATE TABLE `pos_record_id_generator` (
  `id` bigint(64) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  PRIMARY KEY (`id`)
);





15262
78924
80347
63923
